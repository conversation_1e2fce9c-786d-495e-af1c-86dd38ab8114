{"cells": [{"cell_type": "markdown", "id": "073a8d6d", "metadata": {}, "source": ["# XGBoost"]}, {"cell_type": "code", "execution_count": 124, "id": "001e6934", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import xgboost as xgb\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, roc_auc_score, make_scorer, confusion_matrix, ConfusionMatrixDisplay\n", "from sklearn.model_selection import GridSearchCV"]}, {"cell_type": "markdown", "id": "7631559b", "metadata": {}, "source": ["## Data"]}, {"cell_type": "markdown", "id": "c46fe9a1", "metadata": {}, "source": ["### Import Data"]}, {"cell_type": "code", "execution_count": 96, "id": "319b8915", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "CustomerID", "rawType": "object", "type": "string"}, {"name": "Count", "rawType": "int64", "type": "integer"}, {"name": "Country", "rawType": "object", "type": "string"}, {"name": "State", "rawType": "object", "type": "string"}, {"name": "City", "rawType": "object", "type": "string"}, {"name": "Zip Code", "rawType": "int64", "type": "integer"}, {"name": "<PERSON><PERSON>", "rawType": "object", "type": "string"}, {"name": "Latitude", "rawType": "float64", "type": "float"}, {"name": "Longitude", "rawType": "float64", "type": "float"}, {"name": "Gender", "rawType": "object", "type": "string"}, {"name": "Senior Citizen", "rawType": "object", "type": "string"}, {"name": "Partner", "rawType": "object", "type": "string"}, {"name": "Dependents", "rawType": "object", "type": "string"}, {"name": "Tenure Months", "rawType": "int64", "type": "integer"}, {"name": "Phone Service", "rawType": "object", "type": "string"}, {"name": "Multiple Lines", "rawType": "object", "type": "string"}, {"name": "Internet Service", "rawType": "object", "type": "string"}, {"name": "Online Security", "rawType": "object", "type": "string"}, {"name": "Online Backup", "rawType": "object", "type": "string"}, {"name": "Device Protection", "rawType": "object", "type": "string"}, {"name": "Tech Support", "rawType": "object", "type": "string"}, {"name": "Streaming TV", "rawType": "object", "type": "string"}, {"name": "Streaming Movies", "rawType": "object", "type": "string"}, {"name": "Contract", "rawType": "object", "type": "string"}, {"name": "Paperless Billing", "rawType": "object", "type": "string"}, {"name": "Payment Method", "rawType": "object", "type": "string"}, {"name": "Monthly Charges", "rawType": "float64", "type": "float"}, {"name": "Total Charges", "rawType": "object", "type": "unknown"}, {"name": "Churn Label", "rawType": "object", "type": "string"}, {"name": "Churn Value", "rawType": "int64", "type": "integer"}, {"name": "Churn Score", "rawType": "int64", "type": "integer"}, {"name": "CLTV", "rawType": "int64", "type": "integer"}, {"name": "Churn Reason", "rawType": "object", "type": "string"}], "ref": "adda7dc5-8ae2-4996-b389-986f54426b99", "rows": [["0", "3668-QPYBK", "1", "United States", "California", "Los Angeles", "90003", "33.964131, -118.272783", "33.964131", "-118.272783", "Male", "No", "No", "No", "2", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Mailed check", "53.85", "108.15", "Yes", "1", "86", "3239", "Competitor made better offer"], ["1", "9237-HQITU", "1", "United States", "California", "Los Angeles", "90005", "34.059281, -118.30742", "34.059281", "-118.30742", "Female", "No", "No", "Yes", "2", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "70.7", "151.65", "Yes", "1", "67", "2701", "Moved"], ["2", "9305-CDSKC", "1", "United States", "California", "Los Angeles", "90006", "34.048013, -118.293953", "34.048013", "-118.293953", "Female", "No", "No", "Yes", "8", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "99.65", "820.5", "Yes", "1", "86", "5372", "Moved"], ["3", "7892-POOKP", "1", "United States", "California", "Los Angeles", "90010", "34.062125, -118.315709", "34.062125", "-118.315709", "Female", "No", "Yes", "Yes", "28", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "Yes", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "104.8", "3046.05", "Yes", "1", "84", "5003", "Moved"], ["4", "0280-XJGEX", "1", "United States", "California", "Los Angeles", "90015", "34.039224, -118.266293", "34.039224", "-118.266293", "Male", "No", "No", "Yes", "49", "Yes", "Yes", "Fiber optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Bank transfer (automatic)", "103.7", "5036.3", "Yes", "1", "89", "5340", "Competitor had better devices"], ["5", "4190-MFLUW", "1", "United States", "California", "Los Angeles", "90020", "34.066367, -118.309868", "34.066367", "-118.309868", "Female", "No", "Yes", "No", "10", "Yes", "No", "DSL", "No", "No", "Yes", "Yes", "No", "No", "Month-to-month", "No", "Credit card (automatic)", "55.2", "528.35", "Yes", "1", "78", "5925", "Competitor offered higher download speeds"], ["6", "8779-QRDMV", "1", "United States", "California", "Los Angeles", "90022", "34.02381, -118.156582", "34.02381", "-118.156582", "Male", "Yes", "No", "No", "1", "No", "No phone service", "DSL", "No", "No", "Yes", "No", "No", "Yes", "Month-to-month", "Yes", "Electronic check", "39.65", "39.65", "Yes", "1", "100", "5433", "Competitor offered more data"], ["7", "1066-JKSGK", "1", "United States", "California", "Los Angeles", "90024", "34.066303, -118.435479", "34.066303", "-118.435479", "Male", "No", "No", "No", "1", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Month-to-month", "No", "Mailed check", "20.15", "20.15", "Yes", "1", "92", "4832", "Competitor made better offer"], ["8", "6467-CHFZW", "1", "United States", "California", "Los Angeles", "90028", "34.099869, -118.326843", "34.099869", "-118.326843", "Male", "No", "Yes", "Yes", "47", "Yes", "Yes", "Fiber optic", "No", "Yes", "No", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "99.35", "4749.15", "Yes", "1", "77", "5789", "Competitor had better devices"], ["9", "8665-UTDHZ", "1", "United States", "California", "Los Angeles", "90029", "34.089953, -118.294824", "34.089953", "-118.294824", "Male", "No", "Yes", "No", "1", "No", "No phone service", "DSL", "No", "Yes", "No", "No", "No", "No", "Month-to-month", "No", "Electronic check", "30.2", "30.2", "Yes", "1", "97", "2915", "Competitor had better devices"], ["10", "8773-HHUOZ", "1", "United States", "California", "Los Angeles", "90032", "34.078821, -118.177576", "34.078821", "-118.177576", "Female", "No", "No", "No", "17", "Yes", "No", "DSL", "No", "No", "No", "No", "Yes", "Yes", "Month-to-month", "Yes", "Mailed check", "64.7", "1093.1", "Yes", "1", "74", "3022", "Competitor offered higher download speeds"], ["11", "6047-YHPVI", "1", "United States", "California", "Los Angeles", "90039", "34.110845, -118.259595", "34.110845", "-118.259595", "Male", "No", "No", "Yes", "5", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "69.7", "316.9", "Yes", "1", "66", "2454", "Competitor offered higher download speeds"], ["12", "5380-WJKOV", "1", "United States", "California", "Los Angeles", "90041", "34.137412, -118.207607", "34.137412", "-118.207607", "Male", "No", "No", "Yes", "34", "Yes", "Yes", "Fiber optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "106.35", "3549.25", "Yes", "1", "65", "2941", "Competitor offered higher download speeds"], ["13", "8168-UQWWF", "1", "United States", "California", "Los Angeles", "90042", "34.11572, -118.192754", "34.11572", "-118.192754", "Female", "No", "No", "Yes", "11", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Bank transfer (automatic)", "97.85", "1105.4", "Yes", "1", "70", "5674", "Competitor offered more data"], ["14", "7760-OYPDY", "1", "United States", "California", "Los Angeles", "90056", "33.987945, -118.370442", "33.987945", "-118.370442", "Female", "No", "No", "Yes", "2", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "Yes", "No", "Month-to-month", "Yes", "Electronic check", "80.65", "144.15", "Yes", "1", "90", "5586", "Competitor offered more data"], ["15", "9420-LOJKX", "1", "United States", "California", "Los Angeles", "90061", "33.92128, -118.274186", "33.92128", "-118.274186", "Female", "No", "No", "Yes", "15", "Yes", "No", "Fiber optic", "Yes", "Yes", "No", "No", "Yes", "Yes", "Month-to-month", "Yes", "Credit card (automatic)", "99.1", "1426.4", "Yes", "1", "82", "2966", "Competitor offered more data"], ["16", "7495-OOKFY", "1", "United States", "California", "Los Angeles", "90063", "34.044271, -118.185237", "34.044271", "-118.185237", "Female", "Yes", "Yes", "Yes", "8", "Yes", "Yes", "Fiber optic", "No", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Credit card (automatic)", "80.65", "633.3", "Yes", "1", "69", "5302", "Competitor made better offer"], ["17", "1658-BYGOY", "1", "United States", "California", "Los Angeles", "90065", "34.108833, -118.229715", "34.108833", "-118.229715", "Male", "Yes", "No", "Yes", "18", "Yes", "Yes", "Fiber optic", "No", "No", "No", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "95.45", "1752.55", "Yes", "1", "81", "3179", "Competitor made better offer"], ["18", "5698-BQJOH", "1", "United States", "California", "Beverly Hills", "90211", "34.063947, -118.383001", "34.063947", "-118.383001", "Female", "No", "No", "Yes", "9", "Yes", "Yes", "Fiber optic", "No", "No", "No", "No", "Yes", "Yes", "Month-to-month", "No", "Electronic check", "94.4", "857.25", "Yes", "1", "96", "5571", "Price too high"], ["19", "5919-TMRGD", "1", "United States", "California", "Huntington Park", "90255", "33.97803, -118.217141", "33.97803", "-118.217141", "Female", "No", "No", "Yes", "1", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "Yes", "No", "Month-to-month", "Yes", "Electronic check", "79.35", "79.35", "Yes", "1", "87", "2483", "Product dissatisfaction"], ["20", "9191-MYQKX", "1", "United States", "California", "<PERSON><PERSON><PERSON>", "90262", "33.923573, -118.200669", "33.923573", "-118.200669", "Female", "No", "Yes", "Yes", "7", "Yes", "No", "Fiber optic", "No", "No", "Yes", "No", "No", "No", "Month-to-month", "Yes", "Bank transfer (automatic)", "75.15", "496.9", "Yes", "1", "70", "3457", "Service dissatisfaction"], ["21", "8637-XJIVR", "1", "United States", "California", "Marina Del Rey", "90292", "33.977468, -118.445475", "33.977468", "-118.445475", "Female", "No", "No", "Yes", "12", "Yes", "Yes", "Fiber optic", "Yes", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "78.95", "927.35", "Yes", "1", "86", "5806", "Lack of self-service on Website"], ["22", "0278-YXOOG", "1", "United States", "California", "Inglewood", "90301", "33.956445, -118.358634", "33.956445", "-118.358634", "Male", "No", "No", "No", "5", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Month-to-month", "No", "Mailed check", "21.05", "113.85", "Yes", "1", "66", "2604", "Network reliability"], ["23", "4598-XLKNJ", "1", "United States", "California", "Inglewood", "90303", "33.936291, -118.332639", "33.936291", "-118.332639", "Female", "Yes", "Yes", "Yes", "25", "Yes", "No", "Fiber optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "98.5", "2514.5", "Yes", "1", "88", "5337", "Limited range of services"], ["24", "3192-NQECA", "1", "United States", "California", "Santa Monica", "90403", "34.031529, -118.491156", "34.031529", "-118.491156", "Male", "No", "Yes", "No", "68", "Yes", "Yes", "Fiber optic", "No", "Yes", "Yes", "Yes", "Yes", "Yes", "Two year", "Yes", "Bank transfer (automatic)", "110.0", "7611.85", "Yes", "1", "86", "5034", "Lack of affordable download/upload speed"]], "shape": {"columns": 33, "rows": 25}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CustomerID</th>\n", "      <th>Count</th>\n", "      <th>Country</th>\n", "      <th>State</th>\n", "      <th>City</th>\n", "      <th>Zip Code</th>\n", "      <th>Lat <PERSON></th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>Gender</th>\n", "      <th>...</th>\n", "      <th>Contract</th>\n", "      <th>Paperless Billing</th>\n", "      <th>Payment Method</th>\n", "      <th>Monthly Charges</th>\n", "      <th>Total Charges</th>\n", "      <th>Churn Label</th>\n", "      <th>Churn Value</th>\n", "      <th>Churn Score</th>\n", "      <th>CLTV</th>\n", "      <th><PERSON>rn Reason</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3668-QPYBK</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90003</td>\n", "      <td>33.964131, -118.272783</td>\n", "      <td>33.964131</td>\n", "      <td>-118.272783</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>86</td>\n", "      <td>3239</td>\n", "      <td>Competitor made better offer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9237-HQITU</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90005</td>\n", "      <td>34.059281, -118.30742</td>\n", "      <td>34.059281</td>\n", "      <td>-118.307420</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>2701</td>\n", "      <td>Moved</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>9305-CDSKC</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90006</td>\n", "      <td>34.048013, -118.293953</td>\n", "      <td>34.048013</td>\n", "      <td>-118.293953</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>99.65</td>\n", "      <td>820.5</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>86</td>\n", "      <td>5372</td>\n", "      <td>Moved</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7892-POOKP</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90010</td>\n", "      <td>34.062125, -118.315709</td>\n", "      <td>34.062125</td>\n", "      <td>-118.315709</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>104.80</td>\n", "      <td>3046.05</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>84</td>\n", "      <td>5003</td>\n", "      <td>Moved</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0280-XJGEX</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90015</td>\n", "      <td>34.039224, -118.266293</td>\n", "      <td>34.039224</td>\n", "      <td>-118.266293</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>103.70</td>\n", "      <td>5036.3</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>89</td>\n", "      <td>5340</td>\n", "      <td>Competitor had better devices</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>4190-MFLUW</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90020</td>\n", "      <td>34.066367, -118.309868</td>\n", "      <td>34.066367</td>\n", "      <td>-118.309868</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>No</td>\n", "      <td>Credit card (automatic)</td>\n", "      <td>55.20</td>\n", "      <td>528.35</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>78</td>\n", "      <td>5925</td>\n", "      <td>Competitor offered higher download speeds</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>8779-QRDMV</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90022</td>\n", "      <td>34.02381, -118.156582</td>\n", "      <td>34.023810</td>\n", "      <td>-118.156582</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>39.65</td>\n", "      <td>39.65</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>100</td>\n", "      <td>5433</td>\n", "      <td>Competitor offered more data</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1066-JKSGK</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90024</td>\n", "      <td>34.066303, -118.435479</td>\n", "      <td>34.066303</td>\n", "      <td>-118.435479</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>20.15</td>\n", "      <td>20.15</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>92</td>\n", "      <td>4832</td>\n", "      <td>Competitor made better offer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>6467-CHFZW</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90028</td>\n", "      <td>34.099869, -118.326843</td>\n", "      <td>34.099869</td>\n", "      <td>-118.326843</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>99.35</td>\n", "      <td>4749.15</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>77</td>\n", "      <td>5789</td>\n", "      <td>Competitor had better devices</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>8665-UTDHZ</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90029</td>\n", "      <td>34.089953, -118.294824</td>\n", "      <td>34.089953</td>\n", "      <td>-118.294824</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>No</td>\n", "      <td>Electronic check</td>\n", "      <td>30.20</td>\n", "      <td>30.2</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>97</td>\n", "      <td>2915</td>\n", "      <td>Competitor had better devices</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>8773-HHUOZ</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90032</td>\n", "      <td>34.078821, -118.177576</td>\n", "      <td>34.078821</td>\n", "      <td>-118.177576</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>64.70</td>\n", "      <td>1093.1</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>74</td>\n", "      <td>3022</td>\n", "      <td>Competitor offered higher download speeds</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>6047-YHPVI</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90039</td>\n", "      <td>34.110845, -118.259595</td>\n", "      <td>34.110845</td>\n", "      <td>-118.259595</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>69.70</td>\n", "      <td>316.9</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>66</td>\n", "      <td>2454</td>\n", "      <td>Competitor offered higher download speeds</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>5380-WJKOV</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90041</td>\n", "      <td>34.137412, -118.207607</td>\n", "      <td>34.137412</td>\n", "      <td>-118.207607</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>106.35</td>\n", "      <td>3549.25</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>65</td>\n", "      <td>2941</td>\n", "      <td>Competitor offered higher download speeds</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>8168-UQWWF</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90042</td>\n", "      <td>34.11572, -118.192754</td>\n", "      <td>34.115720</td>\n", "      <td>-118.192754</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>97.85</td>\n", "      <td>1105.4</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>70</td>\n", "      <td>5674</td>\n", "      <td>Competitor offered more data</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>7760-OYPDY</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90056</td>\n", "      <td>33.987945, -118.370442</td>\n", "      <td>33.987945</td>\n", "      <td>-118.370442</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>80.65</td>\n", "      <td>144.15</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>90</td>\n", "      <td>5586</td>\n", "      <td>Competitor offered more data</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>9420-LOJKX</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90061</td>\n", "      <td>33.92128, -118.274186</td>\n", "      <td>33.921280</td>\n", "      <td>-118.274186</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Credit card (automatic)</td>\n", "      <td>99.10</td>\n", "      <td>1426.4</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>82</td>\n", "      <td>2966</td>\n", "      <td>Competitor offered more data</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>7495-OOKFY</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90063</td>\n", "      <td>34.044271, -118.185237</td>\n", "      <td>34.044271</td>\n", "      <td>-118.185237</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Credit card (automatic)</td>\n", "      <td>80.65</td>\n", "      <td>633.3</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>69</td>\n", "      <td>5302</td>\n", "      <td>Competitor made better offer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>1658-BYGOY</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Los Angeles</td>\n", "      <td>90065</td>\n", "      <td>34.108833, -118.229715</td>\n", "      <td>34.108833</td>\n", "      <td>-118.229715</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>95.45</td>\n", "      <td>1752.55</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>81</td>\n", "      <td>3179</td>\n", "      <td>Competitor made better offer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>5698-BQJOH</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Beverly Hills</td>\n", "      <td>90211</td>\n", "      <td>34.063947, -118.383001</td>\n", "      <td>34.063947</td>\n", "      <td>-118.383001</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>No</td>\n", "      <td>Electronic check</td>\n", "      <td>94.40</td>\n", "      <td>857.25</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>96</td>\n", "      <td>5571</td>\n", "      <td>Price too high</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>5919-TMRGD</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Huntington Park</td>\n", "      <td>90255</td>\n", "      <td>33.97803, -118.217141</td>\n", "      <td>33.978030</td>\n", "      <td>-118.217141</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>79.35</td>\n", "      <td>79.35</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>87</td>\n", "      <td>2483</td>\n", "      <td>Product dissatisfaction</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>9191-MYQKX</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>90262</td>\n", "      <td>33.923573, -118.200669</td>\n", "      <td>33.923573</td>\n", "      <td>-118.200669</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>75.15</td>\n", "      <td>496.9</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>70</td>\n", "      <td>3457</td>\n", "      <td>Service dissatisfaction</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>8637-XJIVR</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td><PERSON></td>\n", "      <td>90292</td>\n", "      <td>33.977468, -118.445475</td>\n", "      <td>33.977468</td>\n", "      <td>-118.445475</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>78.95</td>\n", "      <td>927.35</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>86</td>\n", "      <td>5806</td>\n", "      <td>Lack of self-service on Website</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>0278-YXOOG</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Inglewood</td>\n", "      <td>90301</td>\n", "      <td>33.956445, -118.358634</td>\n", "      <td>33.956445</td>\n", "      <td>-118.358634</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>21.05</td>\n", "      <td>113.85</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>66</td>\n", "      <td>2604</td>\n", "      <td>Network reliability</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>4598-XLKNJ</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td>Inglewood</td>\n", "      <td>90303</td>\n", "      <td>33.936291, -118.332639</td>\n", "      <td>33.936291</td>\n", "      <td>-118.332639</td>\n", "      <td>Female</td>\n", "      <td>...</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>98.50</td>\n", "      <td>2514.5</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>88</td>\n", "      <td>5337</td>\n", "      <td>Limited range of services</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>3192-NQECA</td>\n", "      <td>1</td>\n", "      <td>United States</td>\n", "      <td>California</td>\n", "      <td><PERSON></td>\n", "      <td>90403</td>\n", "      <td>34.031529, -118.491156</td>\n", "      <td>34.031529</td>\n", "      <td>-118.491156</td>\n", "      <td>Male</td>\n", "      <td>...</td>\n", "      <td>Two year</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>110.00</td>\n", "      <td>7611.85</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>86</td>\n", "      <td>5034</td>\n", "      <td>Lack of affordable download/upload speed</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>25 rows × 33 columns</p>\n", "</div>"], "text/plain": ["    CustomerID  Count        Country       State             City  Zip Code  \\\n", "0   3668-QPYBK      1  United States  California      Los Angeles     90003   \n", "1   9237-HQITU      1  United States  California      Los Angeles     90005   \n", "2   9305-CDSKC      1  United States  California      Los Angeles     90006   \n", "3   7892-POOKP      1  United States  California      Los Angeles     90010   \n", "4   0280-XJGEX      1  United States  California      Los Angeles     90015   \n", "5   4190-MFLUW      1  United States  California      Los Angeles     90020   \n", "6   8779-QRDMV      1  United States  California      Los Angeles     90022   \n", "7   1066-JKSGK      1  United States  California      Los Angeles     90024   \n", "8   6467-CHFZW      1  United States  California      Los Angeles     90028   \n", "9   8665-UTDHZ      1  United States  California      Los Angeles     90029   \n", "10  8773-HHUOZ      1  United States  California      Los Angeles     90032   \n", "11  6047-YHPVI      1  United States  California      Los Angeles     90039   \n", "12  5380-WJKOV      1  United States  California      Los Angeles     90041   \n", "13  8168-UQWWF      1  United States  California      Los Angeles     90042   \n", "14  7760-OYPDY      1  United States  California      Los Angeles     90056   \n", "15  9420-LOJKX      1  United States  California      Los Angeles     90061   \n", "16  7495-OOKFY      1  United States  California      Los Angeles     90063   \n", "17  1658-BYGOY      1  United States  California      Los Angeles     90065   \n", "18  5698-BQJOH      1  United States  California    Beverly Hills     90211   \n", "19  5919-TMRGD      1  United States  California  Huntington Park     90255   \n", "20  9191-MYQKX      1  United States  California          Lynwood     90262   \n", "21  8637-XJIVR      1  United States  California   Marina Del Rey     90292   \n", "22  0278-YXOOG      1  United States  California        Inglewood     90301   \n", "23  4598-XLKNJ      1  United States  California        Inglewood     90303   \n", "24  3192-NQECA      1  United States  California     Santa Monica     90403   \n", "\n", "                  Lat Long   Latitude   Longitude  Gender  ...  \\\n", "0   33.964131, -118.272783  33.964131 -118.272783    Male  ...   \n", "1    34.059281, -118.30742  34.059281 -118.307420  Female  ...   \n", "2   34.048013, -118.293953  34.048013 -118.293953  Female  ...   \n", "3   34.062125, -118.315709  34.062125 -118.315709  Female  ...   \n", "4   34.039224, -118.266293  34.039224 -118.266293    Male  ...   \n", "5   34.066367, -118.309868  34.066367 -118.309868  Female  ...   \n", "6    34.02381, -118.156582  34.023810 -118.156582    Male  ...   \n", "7   34.066303, -118.435479  34.066303 -118.435479    Male  ...   \n", "8   34.099869, -118.326843  34.099869 -118.326843    Male  ...   \n", "9   34.089953, -118.294824  34.089953 -118.294824    Male  ...   \n", "10  34.078821, -118.177576  34.078821 -118.177576  Female  ...   \n", "11  34.110845, -118.259595  34.110845 -118.259595    Male  ...   \n", "12  34.137412, -118.207607  34.137412 -118.207607    Male  ...   \n", "13   34.11572, -118.192754  34.115720 -118.192754  Female  ...   \n", "14  33.987945, -118.370442  33.987945 -118.370442  Female  ...   \n", "15   33.92128, -118.274186  33.921280 -118.274186  Female  ...   \n", "16  34.044271, -118.185237  34.044271 -118.185237  Female  ...   \n", "17  34.108833, -118.229715  34.108833 -118.229715    Male  ...   \n", "18  34.063947, -118.383001  34.063947 -118.383001  Female  ...   \n", "19   33.97803, -118.217141  33.978030 -118.217141  Female  ...   \n", "20  33.923573, -118.200669  33.923573 -118.200669  Female  ...   \n", "21  33.977468, -118.445475  33.977468 -118.445475  Female  ...   \n", "22  33.956445, -118.358634  33.956445 -118.358634    Male  ...   \n", "23  33.936291, -118.332639  33.936291 -118.332639  Female  ...   \n", "24  34.031529, -118.491156  34.031529 -118.491156    Male  ...   \n", "\n", "          Contract Paperless Billing             Payment Method  \\\n", "0   Month-to-month               Yes               Mailed check   \n", "1   Month-to-month               Yes           Electronic check   \n", "2   Month-to-month               Yes           Electronic check   \n", "3   Month-to-month               Yes           Electronic check   \n", "4   Month-to-month               Yes  Bank transfer (automatic)   \n", "5   Month-to-month                No    Credit card (automatic)   \n", "6   Month-to-month               Yes           Electronic check   \n", "7   Month-to-month                No               Mailed check   \n", "8   Month-to-month               Yes           Electronic check   \n", "9   Month-to-month                No           Electronic check   \n", "10  Month-to-month               Yes               Mailed check   \n", "11  Month-to-month               Yes           Electronic check   \n", "12  Month-to-month               Yes           Electronic check   \n", "13  Month-to-month               Yes  Bank transfer (automatic)   \n", "14  Month-to-month               Yes           Electronic check   \n", "15  Month-to-month               Yes    Credit card (automatic)   \n", "16  Month-to-month               Yes    Credit card (automatic)   \n", "17  Month-to-month               Yes           Electronic check   \n", "18  Month-to-month                No           Electronic check   \n", "19  Month-to-month               Yes           Electronic check   \n", "20  Month-to-month               Yes  Bank transfer (automatic)   \n", "21  Month-to-month               Yes           Electronic check   \n", "22  Month-to-month                No               Mailed check   \n", "23  Month-to-month               Yes           Electronic check   \n", "24        Two year               Yes  Bank transfer (automatic)   \n", "\n", "    Monthly Charges Total Charges Churn Label Churn Value Churn Score  CLTV  \\\n", "0             53.85        108.15         Yes           1          86  3239   \n", "1             70.70        151.65         Yes           1          67  2701   \n", "2             99.65         820.5         Yes           1          86  5372   \n", "3            104.80       3046.05         Yes           1          84  5003   \n", "4            103.70        5036.3         Yes           1          89  5340   \n", "5             55.20        528.35         Yes           1          78  5925   \n", "6             39.65         39.65         Yes           1         100  5433   \n", "7             20.15         20.15         Yes           1          92  4832   \n", "8             99.35       4749.15         Yes           1          77  5789   \n", "9             30.20          30.2         Yes           1          97  2915   \n", "10            64.70        1093.1         Yes           1          74  3022   \n", "11            69.70         316.9         Yes           1          66  2454   \n", "12           106.35       3549.25         Yes           1          65  2941   \n", "13            97.85        1105.4         Yes           1          70  5674   \n", "14            80.65        144.15         Yes           1          90  5586   \n", "15            99.10        1426.4         Yes           1          82  2966   \n", "16            80.65         633.3         Yes           1          69  5302   \n", "17            95.45       1752.55         Yes           1          81  3179   \n", "18            94.40        857.25         Yes           1          96  5571   \n", "19            79.35         79.35         Yes           1          87  2483   \n", "20            75.15         496.9         Yes           1          70  3457   \n", "21            78.95        927.35         Yes           1          86  5806   \n", "22            21.05        113.85         Yes           1          66  2604   \n", "23            98.50        2514.5         Yes           1          88  5337   \n", "24           110.00       7611.85         Yes           1          86  5034   \n", "\n", "                                 Churn Reason  \n", "0                Competitor made better offer  \n", "1                                       Moved  \n", "2                                       Moved  \n", "3                                       Moved  \n", "4               Competitor had better devices  \n", "5   Competitor offered higher download speeds  \n", "6                Competitor offered more data  \n", "7                Competitor made better offer  \n", "8               Competitor had better devices  \n", "9               Competitor had better devices  \n", "10  Competitor offered higher download speeds  \n", "11  Competitor offered higher download speeds  \n", "12  Competitor offered higher download speeds  \n", "13               Competitor offered more data  \n", "14               Competitor offered more data  \n", "15               Competitor offered more data  \n", "16               Competitor made better offer  \n", "17               Competitor made better offer  \n", "18                             Price too high  \n", "19                    Product dissatisfaction  \n", "20                    Service dissatisfaction  \n", "21            Lack of self-service on Website  \n", "22                        Network reliability  \n", "23                  Limited range of services  \n", "24   Lack of affordable download/upload speed  \n", "\n", "[25 rows x 33 columns]"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_excel('Telco_customer_churn.xlsx')\n", "\n", "df.head(25)"]}, {"cell_type": "code", "execution_count": 97, "id": "c416dc45", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1], dtype=int64)"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["df.Count.unique()"]}, {"cell_type": "code", "execution_count": 98, "id": "a34243fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['United States'], dtype=object)"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["df.Country.unique()"]}, {"cell_type": "code", "execution_count": 99, "id": "1b79e9db", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['California'], dtype=object)"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["df.State.unique()"]}, {"cell_type": "code", "execution_count": 100, "id": "64040000", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "City", "rawType": "object", "type": "string"}, {"name": "Zip Code", "rawType": "int64", "type": "integer"}, {"name": "Latitude", "rawType": "float64", "type": "float"}, {"name": "Longitude", "rawType": "float64", "type": "float"}, {"name": "Gender", "rawType": "object", "type": "string"}, {"name": "Senior Citizen", "rawType": "object", "type": "string"}, {"name": "Partner", "rawType": "object", "type": "string"}, {"name": "Dependents", "rawType": "object", "type": "string"}, {"name": "Tenure Months", "rawType": "int64", "type": "integer"}, {"name": "Phone Service", "rawType": "object", "type": "string"}, {"name": "Multiple Lines", "rawType": "object", "type": "string"}, {"name": "Internet Service", "rawType": "object", "type": "string"}, {"name": "Online Security", "rawType": "object", "type": "string"}, {"name": "Online Backup", "rawType": "object", "type": "string"}, {"name": "Device Protection", "rawType": "object", "type": "string"}, {"name": "Tech Support", "rawType": "object", "type": "string"}, {"name": "Streaming TV", "rawType": "object", "type": "string"}, {"name": "Streaming Movies", "rawType": "object", "type": "string"}, {"name": "Contract", "rawType": "object", "type": "string"}, {"name": "Paperless Billing", "rawType": "object", "type": "string"}, {"name": "Payment Method", "rawType": "object", "type": "string"}, {"name": "Monthly Charges", "rawType": "float64", "type": "float"}, {"name": "Total Charges", "rawType": "object", "type": "unknown"}, {"name": "Churn Value", "rawType": "int64", "type": "integer"}], "ref": "4b0af4c6-3128-48ad-beee-8227f045b3af", "rows": [["0", "Los Angeles", "90003", "33.964131", "-118.272783", "Male", "No", "No", "No", "2", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Mailed check", "53.85", "108.15", "1"], ["1", "Los Angeles", "90005", "34.059281", "-118.30742", "Female", "No", "No", "Yes", "2", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "70.7", "151.65", "1"], ["2", "Los Angeles", "90006", "34.048013", "-118.293953", "Female", "No", "No", "Yes", "8", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "99.65", "820.5", "1"], ["3", "Los Angeles", "90010", "34.062125", "-118.315709", "Female", "No", "Yes", "Yes", "28", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "Yes", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "104.8", "3046.05", "1"], ["4", "Los Angeles", "90015", "34.039224", "-118.266293", "Male", "No", "No", "Yes", "49", "Yes", "Yes", "Fiber optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Bank transfer (automatic)", "103.7", "5036.3", "1"]], "shape": {"columns": 24, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>City</th>\n", "      <th>Zip Code</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>Gender</th>\n", "      <th>Senior Citizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>Tenure Months</th>\n", "      <th>Phone Service</th>\n", "      <th>...</th>\n", "      <th>Device Protection</th>\n", "      <th>Tech Support</th>\n", "      <th>Streaming TV</th>\n", "      <th>Streaming Movies</th>\n", "      <th>Contract</th>\n", "      <th>Paperless Billing</th>\n", "      <th>Payment Method</th>\n", "      <th>Monthly Charges</th>\n", "      <th>Total Charges</th>\n", "      <th>Churn Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Los Angeles</td>\n", "      <td>90003</td>\n", "      <td>33.964131</td>\n", "      <td>-118.272783</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Los Angeles</td>\n", "      <td>90005</td>\n", "      <td>34.059281</td>\n", "      <td>-118.307420</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Los Angeles</td>\n", "      <td>90006</td>\n", "      <td>34.048013</td>\n", "      <td>-118.293953</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>8</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>99.65</td>\n", "      <td>820.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Los Angeles</td>\n", "      <td>90010</td>\n", "      <td>34.062125</td>\n", "      <td>-118.315709</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>28</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>104.80</td>\n", "      <td>3046.05</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Los Angeles</td>\n", "      <td>90015</td>\n", "      <td>34.039224</td>\n", "      <td>-118.266293</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>49</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>103.70</td>\n", "      <td>5036.3</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["          City  Zip Code   Latitude   Longitude  Gender Senior Citizen  \\\n", "0  Los Angeles     90003  33.964131 -118.272783    Male             No   \n", "1  Los Angeles     90005  34.059281 -118.307420  Female             No   \n", "2  Los Angeles     90006  34.048013 -118.293953  Female             No   \n", "3  Los Angeles     90010  34.062125 -118.315709  Female             No   \n", "4  Los Angeles     90015  34.039224 -118.266293    Male             No   \n", "\n", "  Partner Dependents  Tenure Months Phone Service  ... Device Protection  \\\n", "0      No         No              2           Yes  ...                No   \n", "1      No        Yes              2           Yes  ...                No   \n", "2      No        Yes              8           Yes  ...               Yes   \n", "3     Yes        Yes             28           Yes  ...               Yes   \n", "4      No        Yes             49           Yes  ...               Yes   \n", "\n", "  Tech Support Streaming TV Streaming Movies        Contract  \\\n", "0           No           No               No  Month-to-month   \n", "1           No           No               No  Month-to-month   \n", "2           No          Yes              Yes  Month-to-month   \n", "3          Yes          Yes              Yes  Month-to-month   \n", "4           No          Yes              Yes  Month-to-month   \n", "\n", "  Paperless Billing             Payment Method Monthly Charges Total Charges  \\\n", "0               Yes               Mailed check           53.85        108.15   \n", "1               Yes           Electronic check           70.70        151.65   \n", "2               Yes           Electronic check           99.65         820.5   \n", "3               Yes           Electronic check          104.80       3046.05   \n", "4               Yes  Bank transfer (automatic)          103.70        5036.3   \n", "\n", "  Churn Value  \n", "0           1  \n", "1           1  \n", "2           1  \n", "3           1  \n", "4           1  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["df.drop(['Churn Label', 'Churn Score', 'CLTV', 'Churn Reason', 'Count', 'Country', 'State', 'CustomerID', 'Lat Long'], axis=1, inplace=True)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 101, "id": "5510698d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_41884\\*********.py:1: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df.City.replace(' ', '_', regex=True, inplace=True)\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "City", "rawType": "object", "type": "string"}, {"name": "Zip_Code", "rawType": "int64", "type": "integer"}, {"name": "Latitude", "rawType": "float64", "type": "float"}, {"name": "Longitude", "rawType": "float64", "type": "float"}, {"name": "Gender", "rawType": "object", "type": "string"}, {"name": "Senior_Citizen", "rawType": "object", "type": "string"}, {"name": "Partner", "rawType": "object", "type": "string"}, {"name": "Dependents", "rawType": "object", "type": "string"}, {"name": "Tenure_Months", "rawType": "int64", "type": "integer"}, {"name": "Phone_Service", "rawType": "object", "type": "string"}, {"name": "Multiple_Lines", "rawType": "object", "type": "string"}, {"name": "Internet_Service", "rawType": "object", "type": "string"}, {"name": "Online_Security", "rawType": "object", "type": "string"}, {"name": "Online_Backup", "rawType": "object", "type": "string"}, {"name": "Device_Protection", "rawType": "object", "type": "string"}, {"name": "Tech_Support", "rawType": "object", "type": "string"}, {"name": "Streaming_TV", "rawType": "object", "type": "string"}, {"name": "Streaming_Movies", "rawType": "object", "type": "string"}, {"name": "Contract", "rawType": "object", "type": "string"}, {"name": "Paperless_Billing", "rawType": "object", "type": "string"}, {"name": "Payment_Method", "rawType": "object", "type": "string"}, {"name": "Monthly_Charges", "rawType": "float64", "type": "float"}, {"name": "Total_Charges", "rawType": "object", "type": "unknown"}, {"name": "Churn_Value", "rawType": "int64", "type": "integer"}], "ref": "cf39e9e9-d1e7-4d29-b7a0-795c3912612d", "rows": [["0", "Los_Angeles", "90003", "33.964131", "-118.272783", "Male", "No", "No", "No", "2", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Mailed check", "53.85", "108.15", "1"], ["1", "Los_Angeles", "90005", "34.059281", "-118.30742", "Female", "No", "No", "Yes", "2", "Yes", "No", "Fiber optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic check", "70.7", "151.65", "1"], ["2", "Los_Angeles", "90006", "34.048013", "-118.293953", "Female", "No", "No", "Yes", "8", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "99.65", "820.5", "1"], ["3", "Los_Angeles", "90010", "34.062125", "-118.315709", "Female", "No", "Yes", "Yes", "28", "Yes", "Yes", "Fiber optic", "No", "No", "Yes", "Yes", "Yes", "Yes", "Month-to-month", "Yes", "Electronic check", "104.8", "3046.05", "1"], ["4", "Los_Angeles", "90015", "34.039224", "-118.266293", "Male", "No", "No", "Yes", "49", "Yes", "Yes", "Fiber optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Bank transfer (automatic)", "103.7", "5036.3", "1"]], "shape": {"columns": 24, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>City</th>\n", "      <th>Zip_Code</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>Gender</th>\n", "      <th>Senior_Citizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>Tenure_Months</th>\n", "      <th>Phone_Service</th>\n", "      <th>...</th>\n", "      <th>Device_Protection</th>\n", "      <th>Tech_Support</th>\n", "      <th>Streaming_TV</th>\n", "      <th>Streaming_Movies</th>\n", "      <th>Contract</th>\n", "      <th>Paperless_Billing</th>\n", "      <th>Payment_Method</th>\n", "      <th>Monthly_Charges</th>\n", "      <th>Total_Charges</th>\n", "      <th>Churn_Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90003</td>\n", "      <td>33.964131</td>\n", "      <td>-118.272783</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90005</td>\n", "      <td>34.059281</td>\n", "      <td>-118.307420</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90006</td>\n", "      <td>34.048013</td>\n", "      <td>-118.293953</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>8</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>99.65</td>\n", "      <td>820.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90010</td>\n", "      <td>34.062125</td>\n", "      <td>-118.315709</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>28</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>104.80</td>\n", "      <td>3046.05</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90015</td>\n", "      <td>34.039224</td>\n", "      <td>-118.266293</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>49</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>103.70</td>\n", "      <td>5036.3</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["          City  Zip_Code   Latitude   Longitude  Gender Senior_Citizen  \\\n", "0  Los_Angeles     90003  33.964131 -118.272783    Male             No   \n", "1  Los_Angeles     90005  34.059281 -118.307420  Female             No   \n", "2  Los_Angeles     90006  34.048013 -118.293953  Female             No   \n", "3  Los_Angeles     90010  34.062125 -118.315709  Female             No   \n", "4  Los_Angeles     90015  34.039224 -118.266293    Male             No   \n", "\n", "  Partner Dependents  Tenure_Months Phone_Service  ... Device_Protection  \\\n", "0      No         No              2           Yes  ...                No   \n", "1      No        Yes              2           Yes  ...                No   \n", "2      No        Yes              8           Yes  ...               Yes   \n", "3     Yes        Yes             28           Yes  ...               Yes   \n", "4      No        Yes             49           Yes  ...               Yes   \n", "\n", "  Tech_Support Streaming_TV Streaming_Movies        Contract  \\\n", "0           No           No               No  Month-to-month   \n", "1           No           No               No  Month-to-month   \n", "2           No          Yes              Yes  Month-to-month   \n", "3          Yes          Yes              Yes  Month-to-month   \n", "4           No          Yes              Yes  Month-to-month   \n", "\n", "  Paperless_Billing             Payment_Method Monthly_Charges Total_Charges  \\\n", "0               Yes               Mailed check           53.85        108.15   \n", "1               Yes           Electronic check           70.70        151.65   \n", "2               Yes           Electronic check           99.65         820.5   \n", "3               Yes           Electronic check          104.80       3046.05   \n", "4               Yes  Bank transfer (automatic)          103.70        5036.3   \n", "\n", "  Churn_Value  \n", "0           1  \n", "1           1  \n", "2           1  \n", "3           1  \n", "4           1  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["df.City.replace(' ', '_', regex=True, inplace=True)\n", "\n", "df.columns = df.columns.str.replace(' ', '_')\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": 102, "id": "f9de9f18", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Los_Angeles', 'Beverly_Hills', 'Huntington_Park', 'Lynwood',\n", "       'Marina_Del_Rey', 'Inglewood', 'Santa_Monica', 'Torrance',\n", "       'Whit<PERSON>', '<PERSON>_<PERSON><PERSON>'], dtype=object)"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["df.City.unique()[:10]"]}, {"cell_type": "markdown", "id": "409aeb11", "metadata": {}, "source": ["### Missing Data"]}, {"cell_type": "code", "execution_count": 103, "id": "3b2a72fe", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "object", "type": "unknown"}], "ref": "4735989e-0dfd-4f88-b116-c404eccce4af", "rows": [["City", "object"], ["Zip_Code", "int64"], ["Latitude", "float64"], ["Longitude", "float64"], ["Gender", "object"], ["Senior_Citizen", "object"], ["Partner", "object"], ["Dependents", "object"], ["Tenure_Months", "int64"], ["Phone_Service", "object"], ["Multiple_Lines", "object"], ["Internet_Service", "object"], ["Online_Security", "object"], ["Online_Backup", "object"], ["Device_Protection", "object"], ["Tech_Support", "object"], ["Streaming_TV", "object"], ["Streaming_Movies", "object"], ["Contract", "object"], ["Paperless_Billing", "object"], ["Payment_Method", "object"], ["Monthly_Charges", "float64"], ["Total_Charges", "object"], ["Churn_Value", "int64"]], "shape": {"columns": 1, "rows": 24}}, "text/plain": ["City                  object\n", "Zip_Code               int64\n", "Latitude             float64\n", "Longitude            float64\n", "Gender                object\n", "Senior_Citizen        object\n", "Partner               object\n", "Dependents            object\n", "Tenure_Months          int64\n", "Phone_Service         object\n", "Multiple_Lines        object\n", "Internet_Service      object\n", "Online_Security       object\n", "Online_Backup         object\n", "Device_Protection     object\n", "Tech_Support          object\n", "Streaming_TV          object\n", "Streaming_Movies      object\n", "Contract              object\n", "Paperless_Billing     object\n", "Payment_Method        object\n", "Monthly_Charges      float64\n", "Total_Charges         object\n", "Churn_Value            int64\n", "dtype: object"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": 104, "id": "b96fea97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["City: ['Los_Angeles' 'Beverly_Hills' 'Huntington_Park' ... 'Standish' '<PERSON><PERSON><PERSON>'\n", " 'Olympic_Valley'] \n", "\n", "Gender: ['Male' 'Female'] \n", "\n", "Senior_Citizen: ['No' 'Yes'] \n", "\n", "Partner: ['No' 'Yes'] \n", "\n", "Dependents: ['No' 'Yes'] \n", "\n", "Phone_Service: ['Yes' 'No'] \n", "\n", "Multiple_Lines: ['No' 'Yes' 'No phone service'] \n", "\n", "Internet_Service: ['DSL' 'Fiber optic' 'No'] \n", "\n", "Online_Security: ['Yes' 'No' 'No internet service'] \n", "\n", "Online_Backup: ['Yes' 'No' 'No internet service'] \n", "\n", "Device_Protection: ['No' 'Yes' 'No internet service'] \n", "\n", "Tech_Support: ['No' 'Yes' 'No internet service'] \n", "\n", "Streaming_TV: ['No' 'Yes' 'No internet service'] \n", "\n", "Streaming_Movies: ['No' 'Yes' 'No internet service'] \n", "\n", "Contract: ['Month-to-month' 'Two year' 'One year'] \n", "\n", "Paperless_Billing: ['Yes' 'No'] \n", "\n", "Payment_Method: ['Mailed check' 'Electronic check' 'Bank transfer (automatic)'\n", " 'Credit card (automatic)'] \n", "\n", "Total_Charges: [108.15 151.65 820.5 ... 7362.9 346.45 6844.5] \n", "\n"]}], "source": ["for col in df.columns:\n", "    print(f\"{col}: {df[col].unique()} \\n\") if df[col].dtype == 'object' else None"]}, {"cell_type": "code", "execution_count": 105, "id": "35c71487", "metadata": {}, "outputs": [{"data": {"text/plain": ["11"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "City", "rawType": "object", "type": "string"}, {"name": "Zip_Code", "rawType": "int64", "type": "integer"}, {"name": "Latitude", "rawType": "float64", "type": "float"}, {"name": "Longitude", "rawType": "float64", "type": "float"}, {"name": "Gender", "rawType": "object", "type": "string"}, {"name": "Senior_Citizen", "rawType": "object", "type": "string"}, {"name": "Partner", "rawType": "object", "type": "string"}, {"name": "Dependents", "rawType": "object", "type": "string"}, {"name": "Tenure_Months", "rawType": "int64", "type": "integer"}, {"name": "Phone_Service", "rawType": "object", "type": "string"}, {"name": "Multiple_Lines", "rawType": "object", "type": "string"}, {"name": "Internet_Service", "rawType": "object", "type": "string"}, {"name": "Online_Security", "rawType": "object", "type": "string"}, {"name": "Online_Backup", "rawType": "object", "type": "string"}, {"name": "Device_Protection", "rawType": "object", "type": "string"}, {"name": "Tech_Support", "rawType": "object", "type": "string"}, {"name": "Streaming_TV", "rawType": "object", "type": "string"}, {"name": "Streaming_Movies", "rawType": "object", "type": "string"}, {"name": "Contract", "rawType": "object", "type": "string"}, {"name": "Paperless_Billing", "rawType": "object", "type": "string"}, {"name": "Payment_Method", "rawType": "object", "type": "string"}, {"name": "Monthly_Charges", "rawType": "float64", "type": "float"}, {"name": "Total_Charges", "rawType": "object", "type": "string"}, {"name": "Churn_Value", "rawType": "int64", "type": "integer"}], "ref": "05721e6c-7463-4346-9652-982252f2c4c4", "rows": [["2234", "<PERSON><PERSON>Bernardino", "92408", "34.084909", "-117.258107", "Female", "No", "Yes", "No", "0", "No", "No phone service", "DSL", "Yes", "No", "Yes", "Yes", "Yes", "No", "Two year", "Yes", "Bank transfer (automatic)", "52.55", " ", "0"], ["2438", "Independence", "93526", "36.869584", "-118.189241", "Male", "No", "No", "No", "0", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Two year", "No", "Mailed check", "20.25", " ", "0"], ["2568", "San_Mateo", "94401", "37.590421", "-122.306467", "Female", "No", "Yes", "No", "0", "Yes", "No", "DSL", "Yes", "Yes", "Yes", "No", "Yes", "Yes", "Two year", "No", "Mailed check", "80.85", " ", "0"], ["2667", "Cupertino", "95014", "37.306612", "-122.080621", "Male", "No", "Yes", "Yes", "0", "Yes", "Yes", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Two year", "No", "Mailed check", "25.75", " ", "0"], ["2856", "Redcrest", "95569", "40.363446", "-123.835041", "Female", "No", "Yes", "No", "0", "No", "No phone service", "DSL", "Yes", "Yes", "Yes", "Yes", "Yes", "No", "Two year", "No", "Credit card (automatic)", "56.05", " ", "0"], ["4331", "Los_Angeles", "90029", "34.089953", "-118.294824", "Male", "No", "Yes", "Yes", "0", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Two year", "No", "Mailed check", "19.85", " ", "0"], ["4687", "Sun_City", "92585", "33.739412", "-117.173334", "Male", "No", "Yes", "Yes", "0", "Yes", "Yes", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Two year", "No", "Mailed check", "25.35", " ", "0"], ["5104", "<PERSON><PERSON>", "95005", "37.078873", "-122.090386", "Female", "No", "Yes", "Yes", "0", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "Two year", "No", "Mailed check", "20.0", " ", "0"], ["5719", "La_Verne", "91750", "34.144703", "-117.770299", "Male", "No", "Yes", "Yes", "0", "Yes", "No", "No", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "No internet service", "One year", "Yes", "Mailed check", "19.7", " ", "0"], ["6772", "Bell", "90201", "33.970343", "-118.171368", "Female", "No", "Yes", "Yes", "0", "Yes", "Yes", "DSL", "No", "Yes", "Yes", "Yes", "Yes", "No", "Two year", "No", "Mailed check", "73.35", " ", "0"], ["6840", "Wilmington", "90744", "33.782068", "-118.262263", "Male", "No", "No", "Yes", "0", "Yes", "Yes", "DSL", "Yes", "Yes", "No", "Yes", "No", "No", "Two year", "Yes", "Bank transfer (automatic)", "61.9", " ", "0"]], "shape": {"columns": 24, "rows": 11}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>City</th>\n", "      <th>Zip_Code</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>Gender</th>\n", "      <th>Senior_Citizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>Tenure_Months</th>\n", "      <th>Phone_Service</th>\n", "      <th>...</th>\n", "      <th>Device_Protection</th>\n", "      <th>Tech_Support</th>\n", "      <th>Streaming_TV</th>\n", "      <th>Streaming_Movies</th>\n", "      <th>Contract</th>\n", "      <th>Paperless_Billing</th>\n", "      <th>Payment_Method</th>\n", "      <th>Monthly_Charges</th>\n", "      <th>Total_Charges</th>\n", "      <th>Churn_Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2234</th>\n", "      <td><PERSON><PERSON>Bernardino</td>\n", "      <td>92408</td>\n", "      <td>34.084909</td>\n", "      <td>-117.258107</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Two year</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>52.55</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2438</th>\n", "      <td>Independence</td>\n", "      <td>93526</td>\n", "      <td>36.869584</td>\n", "      <td>-118.189241</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>Two year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>20.25</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2568</th>\n", "      <td><PERSON><PERSON>Mateo</td>\n", "      <td>94401</td>\n", "      <td>37.590421</td>\n", "      <td>-122.306467</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Two year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>80.85</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2667</th>\n", "      <td>Cupertino</td>\n", "      <td>95014</td>\n", "      <td>37.306612</td>\n", "      <td>-122.080621</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>Two year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>25.75</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2856</th>\n", "      <td>Redcrest</td>\n", "      <td>95569</td>\n", "      <td>40.363446</td>\n", "      <td>-123.835041</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Two year</td>\n", "      <td>No</td>\n", "      <td>Credit card (automatic)</td>\n", "      <td>56.05</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4331</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90029</td>\n", "      <td>34.089953</td>\n", "      <td>-118.294824</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>Two year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>19.85</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4687</th>\n", "      <td>Sun_City</td>\n", "      <td>92585</td>\n", "      <td>33.739412</td>\n", "      <td>-117.173334</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>Two year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>25.35</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5104</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>95005</td>\n", "      <td>37.078873</td>\n", "      <td>-122.090386</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>Two year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>20.00</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5719</th>\n", "      <td>La_Verne</td>\n", "      <td>91750</td>\n", "      <td>34.144703</td>\n", "      <td>-117.770299</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>No internet service</td>\n", "      <td>One year</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>19.70</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6772</th>\n", "      <td>Bell</td>\n", "      <td>90201</td>\n", "      <td>33.970343</td>\n", "      <td>-118.171368</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Two year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>73.35</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6840</th>\n", "      <td>Wilmington</td>\n", "      <td>90744</td>\n", "      <td>33.782068</td>\n", "      <td>-118.262263</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Two year</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>61.90</td>\n", "      <td></td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>11 rows × 24 columns</p>\n", "</div>"], "text/plain": ["                City  Zip_Code   Latitude   Longitude  Gender Senior_Citizen  \\\n", "2234  <PERSON><PERSON>Bernardino     92408  34.084909 -117.258107  Female             No   \n", "2438    Independence     93526  36.869584 -118.189241    Male             No   \n", "2568       <PERSON><PERSON>Mateo     94401  37.590421 -122.306467  Female             No   \n", "2667       Cupertino     95014  37.306612 -122.080621    Male             No   \n", "2856        Redcrest     95569  40.363446 -123.835041  Female             No   \n", "4331     Los_Angeles     90029  34.089953 -118.294824    Male             No   \n", "4687        Sun_City     92585  33.739412 -117.173334    Male             No   \n", "5104      <PERSON><PERSON>     95005  37.078873 -122.090386  Female             No   \n", "5719        La_Verne     91750  34.144703 -117.770299    Male             No   \n", "6772            Bell     90201  33.970343 -118.171368  Female             No   \n", "6840      Wilmington     90744  33.782068 -118.262263    Male             No   \n", "\n", "     Partner Dependents  Tenure_Months Phone_Service  ...  \\\n", "2234     Yes         No              0            No  ...   \n", "2438      No         No              0           Yes  ...   \n", "2568     Yes         No              0           Yes  ...   \n", "2667     Yes        Yes              0           Yes  ...   \n", "2856     Yes         No              0            No  ...   \n", "4331     Yes        Yes              0           Yes  ...   \n", "4687     Yes        Yes              0           Yes  ...   \n", "5104     Yes        Yes              0           Yes  ...   \n", "5719     Yes        Yes              0           Yes  ...   \n", "6772     Yes        Yes              0           Yes  ...   \n", "6840      No        Yes              0           Yes  ...   \n", "\n", "        Device_Protection         Tech_Support         Streaming_TV  \\\n", "2234                  Yes                  Yes                  Yes   \n", "2438  No internet service  No internet service  No internet service   \n", "2568                  Yes                   No                  Yes   \n", "2667  No internet service  No internet service  No internet service   \n", "2856                  Yes                  Yes                  Yes   \n", "4331  No internet service  No internet service  No internet service   \n", "4687  No internet service  No internet service  No internet service   \n", "5104  No internet service  No internet service  No internet service   \n", "5719  No internet service  No internet service  No internet service   \n", "6772                  Yes                  Yes                  Yes   \n", "6840                   No                  Yes                   No   \n", "\n", "         Streaming_Movies  Contract Paperless_Billing  \\\n", "2234                   No  Two year               Yes   \n", "2438  No internet service  Two year                No   \n", "2568                  Yes  Two year                No   \n", "2667  No internet service  Two year                No   \n", "2856                   No  Two year                No   \n", "4331  No internet service  Two year                No   \n", "4687  No internet service  Two year                No   \n", "5104  No internet service  Two year                No   \n", "5719  No internet service  One year               Yes   \n", "6772                   No  Two year                No   \n", "6840                   No  Two year               Yes   \n", "\n", "                 Payment_Method Monthly_Charges Total_Charges Churn_Value  \n", "2234  Bank transfer (automatic)           52.55                         0  \n", "2438               Mailed check           20.25                         0  \n", "2568               Mailed check           80.85                         0  \n", "2667               Mailed check           25.75                         0  \n", "2856    Credit card (automatic)           56.05                         0  \n", "4331               Mailed check           19.85                         0  \n", "4687               Mailed check           25.35                         0  \n", "5104               Mailed check           20.00                         0  \n", "5719               Mailed check           19.70                         0  \n", "6772               Mailed check           73.35                         0  \n", "6840  Bank transfer (automatic)           61.90                         0  \n", "\n", "[11 rows x 24 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(len(df.loc[df['Total_Charges'] == ' ']))\n", "display(df.loc[df['Total_Charges'] == ' '])\n"]}, {"cell_type": "code", "execution_count": 106, "id": "5b697bf8", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df.loc[(df['Total_Charges'] == ' '), 'Total_Charges'] = 0\n", "\n", "display(len(df.loc[df['Total_Charges'] == ' ']))"]}, {"cell_type": "code", "execution_count": 107, "id": "0b85a468", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "object", "type": "unknown"}], "ref": "a0256b41-1a1c-4ff7-9744-f301b30502e0", "rows": [["City", "object"], ["Zip_Code", "int64"], ["Latitude", "float64"], ["Longitude", "float64"], ["Gender", "object"], ["Senior_Citizen", "object"], ["Partner", "object"], ["Dependents", "object"], ["Tenure_Months", "int64"], ["Phone_Service", "object"], ["Multiple_Lines", "object"], ["Internet_Service", "object"], ["Online_Security", "object"], ["Online_Backup", "object"], ["Device_Protection", "object"], ["Tech_Support", "object"], ["Streaming_TV", "object"], ["Streaming_Movies", "object"], ["Contract", "object"], ["Paperless_Billing", "object"], ["Payment_Method", "object"], ["Monthly_Charges", "float64"], ["Total_Charges", "float64"], ["Churn_Value", "int64"]], "shape": {"columns": 1, "rows": 24}}, "text/plain": ["City                  object\n", "Zip_Code               int64\n", "Latitude             float64\n", "Longitude            float64\n", "Gender                object\n", "Senior_Citizen        object\n", "Partner               object\n", "Dependents            object\n", "Tenure_Months          int64\n", "Phone_Service         object\n", "Multiple_Lines        object\n", "Internet_Service      object\n", "Online_Security       object\n", "Online_Backup         object\n", "Device_Protection     object\n", "Tech_Support          object\n", "Streaming_TV          object\n", "Streaming_Movies      object\n", "Contract              object\n", "Paperless_Billing     object\n", "Payment_Method        object\n", "Monthly_Charges      float64\n", "Total_Charges        float64\n", "Churn_Value            int64\n", "dtype: object"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["df.Total_Charges = pd.to_numeric(df.Total_Charges)\n", "df.dtypes"]}, {"cell_type": "code", "execution_count": 109, "id": "16850a26", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "City", "rawType": "object", "type": "string"}, {"name": "Zip_Code", "rawType": "int64", "type": "integer"}, {"name": "Latitude", "rawType": "float64", "type": "float"}, {"name": "Longitude", "rawType": "float64", "type": "float"}, {"name": "Gender", "rawType": "object", "type": "string"}, {"name": "Senior_Citizen", "rawType": "object", "type": "string"}, {"name": "Partner", "rawType": "object", "type": "string"}, {"name": "Dependents", "rawType": "object", "type": "string"}, {"name": "Tenure_Months", "rawType": "int64", "type": "integer"}, {"name": "Phone_Service", "rawType": "object", "type": "string"}, {"name": "Multiple_Lines", "rawType": "object", "type": "string"}, {"name": "Internet_Service", "rawType": "object", "type": "string"}, {"name": "Online_Security", "rawType": "object", "type": "string"}, {"name": "Online_Backup", "rawType": "object", "type": "string"}, {"name": "Device_Protection", "rawType": "object", "type": "string"}, {"name": "Tech_Support", "rawType": "object", "type": "string"}, {"name": "Streaming_TV", "rawType": "object", "type": "string"}, {"name": "Streaming_Movies", "rawType": "object", "type": "string"}, {"name": "Contract", "rawType": "object", "type": "string"}, {"name": "Paperless_Billing", "rawType": "object", "type": "string"}, {"name": "Payment_Method", "rawType": "object", "type": "string"}, {"name": "Monthly_Charges", "rawType": "float64", "type": "float"}, {"name": "Total_Charges", "rawType": "float64", "type": "float"}, {"name": "Churn_Value", "rawType": "int64", "type": "integer"}], "ref": "e23d4dd6-f021-46cc-9935-0eaa03d28eb8", "rows": [["0", "Los_Angeles", "90003", "33.964131", "-118.272783", "Male", "No", "No", "No", "2", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Mailed_check", "53.85", "108.15", "1"], ["1", "Los_Angeles", "90005", "34.059281", "-118.30742", "Female", "No", "No", "Yes", "2", "Yes", "No", "Fiber_optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic_check", "70.7", "151.65", "1"], ["2", "Los_Angeles", "90006", "34.048013", "-118.293953", "Female", "No", "No", "Yes", "8", "Yes", "Yes", "Fiber_optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic_check", "99.65", "820.5", "1"], ["3", "Los_Angeles", "90010", "34.062125", "-118.315709", "Female", "No", "Yes", "Yes", "28", "Yes", "Yes", "Fiber_optic", "No", "No", "Yes", "Yes", "Yes", "Yes", "Month-to-month", "Yes", "Electronic_check", "104.8", "3046.05", "1"], ["4", "Los_Angeles", "90015", "34.039224", "-118.266293", "Male", "No", "No", "Yes", "49", "Yes", "Yes", "Fiber_optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Bank_transfer_(automatic)", "103.7", "5036.3", "1"]], "shape": {"columns": 24, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>City</th>\n", "      <th>Zip_Code</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>Gender</th>\n", "      <th>Senior_Citizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>Tenure_Months</th>\n", "      <th>Phone_Service</th>\n", "      <th>...</th>\n", "      <th>Device_Protection</th>\n", "      <th>Tech_Support</th>\n", "      <th>Streaming_TV</th>\n", "      <th>Streaming_Movies</th>\n", "      <th>Contract</th>\n", "      <th>Paperless_Billing</th>\n", "      <th>Payment_Method</th>\n", "      <th>Monthly_Charges</th>\n", "      <th>Total_Charges</th>\n", "      <th>Churn_Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90003</td>\n", "      <td>33.964131</td>\n", "      <td>-118.272783</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed_check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90005</td>\n", "      <td>34.059281</td>\n", "      <td>-118.307420</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic_check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90006</td>\n", "      <td>34.048013</td>\n", "      <td>-118.293953</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>8</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic_check</td>\n", "      <td>99.65</td>\n", "      <td>820.50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90010</td>\n", "      <td>34.062125</td>\n", "      <td>-118.315709</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>28</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic_check</td>\n", "      <td>104.80</td>\n", "      <td>3046.05</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90015</td>\n", "      <td>34.039224</td>\n", "      <td>-118.266293</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>49</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Bank_transfer_(automatic)</td>\n", "      <td>103.70</td>\n", "      <td>5036.30</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["          City  Zip_Code   Latitude   Longitude  Gender Senior_Citizen  \\\n", "0  Los_Angeles     90003  33.964131 -118.272783    Male             No   \n", "1  Los_Angeles     90005  34.059281 -118.307420  Female             No   \n", "2  Los_Angeles     90006  34.048013 -118.293953  Female             No   \n", "3  Los_Angeles     90010  34.062125 -118.315709  Female             No   \n", "4  Los_Angeles     90015  34.039224 -118.266293    Male             No   \n", "\n", "  Partner Dependents  Tenure_Months Phone_Service  ... Device_Protection  \\\n", "0      No         No              2           Yes  ...                No   \n", "1      No        Yes              2           Yes  ...                No   \n", "2      No        Yes              8           Yes  ...               Yes   \n", "3     Yes        Yes             28           Yes  ...               Yes   \n", "4      No        Yes             49           Yes  ...               Yes   \n", "\n", "  Tech_Support Streaming_TV Streaming_Movies        Contract  \\\n", "0           No           No               No  Month-to-month   \n", "1           No           No               No  Month-to-month   \n", "2           No          Yes              Yes  Month-to-month   \n", "3          Yes          Yes              Yes  Month-to-month   \n", "4           No          Yes              Yes  Month-to-month   \n", "\n", "  Paperless_Billing             Payment_Method Monthly_Charges Total_Charges  \\\n", "0               Yes               Mailed_check           53.85        108.15   \n", "1               Yes           Electronic_check           70.70        151.65   \n", "2               Yes           Electronic_check           99.65        820.50   \n", "3               Yes           Electronic_check          104.80       3046.05   \n", "4               Yes  Bank_transfer_(automatic)          103.70       5036.30   \n", "\n", "  Churn_Value  \n", "0           1  \n", "1           1  \n", "2           1  \n", "3           1  \n", "4           1  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["df.replace(' ', '_', regex=True, inplace=True)\n", "df.head()"]}, {"cell_type": "markdown", "id": "862fc304", "metadata": {}, "source": ["### Format Data"]}, {"cell_type": "code", "execution_count": 110, "id": "33858f44", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "City", "rawType": "object", "type": "string"}, {"name": "Zip_Code", "rawType": "int64", "type": "integer"}, {"name": "Latitude", "rawType": "float64", "type": "float"}, {"name": "Longitude", "rawType": "float64", "type": "float"}, {"name": "Gender", "rawType": "object", "type": "string"}, {"name": "Senior_Citizen", "rawType": "object", "type": "string"}, {"name": "Partner", "rawType": "object", "type": "string"}, {"name": "Dependents", "rawType": "object", "type": "string"}, {"name": "Tenure_Months", "rawType": "int64", "type": "integer"}, {"name": "Phone_Service", "rawType": "object", "type": "string"}, {"name": "Multiple_Lines", "rawType": "object", "type": "string"}, {"name": "Internet_Service", "rawType": "object", "type": "string"}, {"name": "Online_Security", "rawType": "object", "type": "string"}, {"name": "Online_Backup", "rawType": "object", "type": "string"}, {"name": "Device_Protection", "rawType": "object", "type": "string"}, {"name": "Tech_Support", "rawType": "object", "type": "string"}, {"name": "Streaming_TV", "rawType": "object", "type": "string"}, {"name": "Streaming_Movies", "rawType": "object", "type": "string"}, {"name": "Contract", "rawType": "object", "type": "string"}, {"name": "Paperless_Billing", "rawType": "object", "type": "string"}, {"name": "Payment_Method", "rawType": "object", "type": "string"}, {"name": "Monthly_Charges", "rawType": "float64", "type": "float"}, {"name": "Total_Charges", "rawType": "float64", "type": "float"}], "ref": "c30d46ef-0d76-45a0-b2be-4c856289c1f1", "rows": [["0", "Los_Angeles", "90003", "33.964131", "-118.272783", "Male", "No", "No", "No", "2", "Yes", "No", "DSL", "Yes", "Yes", "No", "No", "No", "No", "Month-to-month", "Yes", "Mailed_check", "53.85", "108.15"], ["1", "Los_Angeles", "90005", "34.059281", "-118.30742", "Female", "No", "No", "Yes", "2", "Yes", "No", "Fiber_optic", "No", "No", "No", "No", "No", "No", "Month-to-month", "Yes", "Electronic_check", "70.7", "151.65"], ["2", "Los_Angeles", "90006", "34.048013", "-118.293953", "Female", "No", "No", "Yes", "8", "Yes", "Yes", "Fiber_optic", "No", "No", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Electronic_check", "99.65", "820.5"], ["3", "Los_Angeles", "90010", "34.062125", "-118.315709", "Female", "No", "Yes", "Yes", "28", "Yes", "Yes", "Fiber_optic", "No", "No", "Yes", "Yes", "Yes", "Yes", "Month-to-month", "Yes", "Electronic_check", "104.8", "3046.05"], ["4", "Los_Angeles", "90015", "34.039224", "-118.266293", "Male", "No", "No", "Yes", "49", "Yes", "Yes", "Fiber_optic", "No", "Yes", "Yes", "No", "Yes", "Yes", "Month-to-month", "Yes", "Bank_transfer_(automatic)", "103.7", "5036.3"]], "shape": {"columns": 23, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>City</th>\n", "      <th>Zip_Code</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>Gender</th>\n", "      <th>Senior_Citizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>Tenure_Months</th>\n", "      <th>Phone_Service</th>\n", "      <th>...</th>\n", "      <th>Online_Backup</th>\n", "      <th>Device_Protection</th>\n", "      <th>Tech_Support</th>\n", "      <th>Streaming_TV</th>\n", "      <th>Streaming_Movies</th>\n", "      <th>Contract</th>\n", "      <th>Paperless_Billing</th>\n", "      <th>Payment_Method</th>\n", "      <th>Monthly_Charges</th>\n", "      <th>Total_Charges</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90003</td>\n", "      <td>33.964131</td>\n", "      <td>-118.272783</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed_check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90005</td>\n", "      <td>34.059281</td>\n", "      <td>-118.307420</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic_check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90006</td>\n", "      <td>34.048013</td>\n", "      <td>-118.293953</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>8</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic_check</td>\n", "      <td>99.65</td>\n", "      <td>820.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90010</td>\n", "      <td>34.062125</td>\n", "      <td>-118.315709</td>\n", "      <td>Female</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>28</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic_check</td>\n", "      <td>104.80</td>\n", "      <td>3046.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Los_Angeles</td>\n", "      <td>90015</td>\n", "      <td>34.039224</td>\n", "      <td>-118.266293</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>49</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Bank_transfer_(automatic)</td>\n", "      <td>103.70</td>\n", "      <td>5036.30</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 23 columns</p>\n", "</div>"], "text/plain": ["          City  Zip_Code   Latitude   Longitude  Gender Senior_Citizen  \\\n", "0  Los_Angeles     90003  33.964131 -118.272783    Male             No   \n", "1  Los_Angeles     90005  34.059281 -118.307420  Female             No   \n", "2  Los_Angeles     90006  34.048013 -118.293953  Female             No   \n", "3  Los_Angeles     90010  34.062125 -118.315709  Female             No   \n", "4  Los_Angeles     90015  34.039224 -118.266293    Male             No   \n", "\n", "  Partner Dependents  Tenure_Months Phone_Service  ... Online_Backup  \\\n", "0      No         No              2           Yes  ...           Yes   \n", "1      No        Yes              2           Yes  ...            No   \n", "2      No        Yes              8           Yes  ...            No   \n", "3     Yes        Yes             28           Yes  ...            No   \n", "4      No        Yes             49           Yes  ...           Yes   \n", "\n", "  Device_Protection Tech_Support Streaming_TV Streaming_Movies  \\\n", "0                No           No           No               No   \n", "1                No           No           No               No   \n", "2               Yes           No          Yes              Yes   \n", "3               Yes          Yes          Yes              Yes   \n", "4               Yes           No          Yes              Yes   \n", "\n", "         Contract Paperless_Billing             Payment_Method  \\\n", "0  Month-to-month               Yes               Mailed_check   \n", "1  Month-to-month               Yes           Electronic_check   \n", "2  Month-to-month               Yes           Electronic_check   \n", "3  Month-to-month               Yes           Electronic_check   \n", "4  Month-to-month               Yes  Bank_transfer_(automatic)   \n", "\n", "  Monthly_Charges Total_Charges  \n", "0           53.85        108.15  \n", "1           70.70        151.65  \n", "2           99.65        820.50  \n", "3          104.80       3046.05  \n", "4          103.70       5036.30  \n", "\n", "[5 rows x 23 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Churn_Value", "rawType": "int64", "type": "integer"}], "ref": "8ab3c53f-2dbb-42c3-94c8-b9c15e494fc2", "rows": [["0", "1"], ["1", "1"], ["2", "1"], ["3", "1"], ["4", "1"]], "shape": {"columns": 1, "rows": 5}}, "text/plain": ["0    1\n", "1    1\n", "2    1\n", "3    1\n", "4    1\n", "Name: Churn_Value, dtype: int64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X = df.drop('Churn_Value', axis=1)\n", "y = df['Churn_Value']\n", "\n", "display(X.head())\n", "display(y.head())"]}, {"cell_type": "code", "execution_count": 111, "id": "353d6b83", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Zip_Code',\n", " 'Latitude',\n", " 'Longitude',\n", " 'Tenure_Months',\n", " 'Monthly_Charges',\n", " 'Total_Charges']"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["['City',\n", " 'Gender',\n", " 'Senior_Citizen',\n", " 'Partner',\n", " 'Dependents',\n", " 'Phone_Service',\n", " 'Multiple_Lines',\n", " 'Internet_Service',\n", " 'Online_Security',\n", " 'Online_Backup',\n", " 'Device_Protection',\n", " 'Tech_Support',\n", " 'Streaming_TV',\n", " 'Streaming_Movies',\n", " 'Contract',\n", " 'Paperless_Billing',\n", " 'Payment_Method']"]}, "metadata": {}, "output_type": "display_data"}], "source": ["numerical_features = X.select_dtypes(include=['int64', 'float64']).columns.tolist()\n", "categorical_features = X.select_dtypes(include=['object']).columns.tolist()\n", "\n", "display(numerical_features)\n", "display(categorical_features)"]}, {"cell_type": "code", "execution_count": 112, "id": "e1fa8a4c", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "888bdec8-3640-4560-b235-a3488025a2eb", "rows": [["City", "0"], ["Zip_Code", "0"], ["Latitude", "0"], ["Longitude", "0"], ["Gender", "0"], ["Senior_Citizen", "0"], ["Partner", "0"], ["Dependents", "0"], ["Tenure_Months", "0"], ["Phone_Service", "0"], ["Multiple_Lines", "0"], ["Internet_Service", "0"], ["Online_Security", "0"], ["Online_Backup", "0"], ["Device_Protection", "0"], ["Tech_Support", "0"], ["Streaming_TV", "0"], ["Streaming_Movies", "0"], ["Contract", "0"], ["Paperless_Billing", "0"], ["Payment_Method", "0"], ["Monthly_Charges", "0"], ["Total_Charges", "0"], ["Churn_Value", "0"]], "shape": {"columns": 1, "rows": 24}}, "text/plain": ["City                 0\n", "Zip_Code             0\n", "Latitude             0\n", "Longitude            0\n", "Gender               0\n", "Senior_Citizen       0\n", "Partner              0\n", "Dependents           0\n", "Tenure_Months        0\n", "Phone_Service        0\n", "Multiple_Lines       0\n", "Internet_Service     0\n", "Online_Security      0\n", "Online_Backup        0\n", "Device_Protection    0\n", "Tech_Support         0\n", "Streaming_TV         0\n", "Streaming_Movies     0\n", "Contract             0\n", "Paperless_Billing    0\n", "Payment_Method       0\n", "Monthly_Charges      0\n", "Total_Charges        0\n", "Churn_Value          0\n", "dtype: int64"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum()"]}, {"cell_type": "markdown", "id": "3c8e0495", "metadata": {}, "source": ["## Model Pipeline"]}, {"cell_type": "code", "execution_count": 116, "id": "ad24ab5e", "metadata": {}, "outputs": [], "source": ["from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "\n", "num_pipeline = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='mean')),\n", "    ('scaler', StandardScaler())\n", "])\n", "\n", "cat_pipeline = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='most_frequent')),\n", "    ('encoder', OneHotEncoder(handle_unknown='ignore', sparse_output=False))\n", "    ])\n", "\n", "transformer = ColumnTransformer([\n", "    ('num', num_pipeline, numerical_features),\n", "    ('cat', cat_pipeline, categorical_features)\n", "])\n", "\n", "preprocessor = Pipeline([\n", "    ('preprocessor', transformer)\n", "])\n"]}, {"cell_type": "code", "execution_count": 113, "id": "50d3d0e8", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.2653698707936959"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(y)/len(y)"]}, {"cell_type": "code", "execution_count": 131, "id": "63c87c5f", "metadata": {}, "outputs": [], "source": ["Xtrain, Xtest, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "X_train_preprocessed = preprocessor.fit_transform(Xtrain)\n", "X_test_preprocessed = preprocessor.transform(Xtest)"]}, {"cell_type": "code", "execution_count": 132, "id": "ba099d54", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.2653532126375577"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["0.2654364797728886"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(sum(y_train)/len(y_train))\n", "display(sum(y_test)/len(y_test))"]}, {"cell_type": "markdown", "id": "d31183a1", "metadata": {}, "source": ["### Base model"]}, {"cell_type": "code", "execution_count": 137, "id": "daa2b5c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\tvalidation_0-aucpr:0.65155\n", "[1]\tvalidation_0-aucpr:0.66125\n", "[2]\tvalidation_0-aucpr:0.65302\n", "[3]\tvalidation_0-aucpr:0.65921\n", "[4]\tvalidation_0-aucpr:0.66422\n", "[5]\tvalidation_0-aucpr:0.66344\n", "[6]\tvalidation_0-aucpr:0.66060\n", "[7]\tvalidation_0-aucpr:0.66323\n", "[8]\tvalidation_0-aucpr:0.66307\n", "[9]\tvalidation_0-aucpr:0.66649\n", "[10]\tvalidation_0-aucpr:0.65986\n", "[11]\tvalidation_0-aucpr:0.65817\n", "[12]\tvalidation_0-aucpr:0.65745\n", "[13]\tvalidation_0-aucpr:0.65482\n", "[14]\tvalidation_0-aucpr:0.65294\n", "[15]\tvalidation_0-aucpr:0.65322\n", "[16]\tvalidation_0-aucpr:0.65747\n", "[17]\tvalidation_0-aucpr:0.65781\n", "[18]\tvalidation_0-aucpr:0.65944\n"]}, {"data": {"text/html": ["<style>#sk-container-id-5 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-5 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-5 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-5 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-5 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-5 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-5 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-5 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-5 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-5 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-5 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-5 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-5 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-5 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-5 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-5 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-5 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-5 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-5 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-5 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-5 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-5 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-5 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-5 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-5 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-5 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-5 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-5 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-5 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-5 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-5 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-5 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-5 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-5 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-5 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-5 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-5 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-5 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-5 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-5 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-5 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-5 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-5\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=10,\n", "              enable_categorical=False, eval_metric=&#x27;aucpr&#x27;, feature_types=None,\n", "              gamma=None, grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=None, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=None, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=None, n_jobs=None,\n", "              num_parallel_tree=None, random_state=42, ...)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-5\" type=\"checkbox\" checked><label for=\"sk-estimator-id-5\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;XGBClassifier<span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=10,\n", "              enable_categorical=False, eval_metric=&#x27;aucpr&#x27;, feature_types=None,\n", "              gamma=None, grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=None, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=None, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=None, n_jobs=None,\n", "              num_parallel_tree=None, random_state=42, ...)</pre></div> </div></div></div></div>"], "text/plain": ["XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=10,\n", "              enable_categorical=False, eval_metric='aucpr', feature_types=None,\n", "              gamma=None, grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=None, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=None, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=None, n_jobs=None,\n", "              num_parallel_tree=None, random_state=42, ...)"]}, "execution_count": 137, "metadata": {}, "output_type": "execute_result"}], "source": ["clf_xgb = xgb.XGBClassifier(objective='binary:logistic', missing=np.nan,\n", "                            early_stopping_rounds=10,\n", "                            eval_metric='aucpr',\n", "                            random_state=42)\n", "clf_xgb.fit(X_train_preprocessed, y_train,\n", "            eval_set=[(X_test_preprocessed, y_test)],\n", "            )"]}, {"cell_type": "code", "execution_count": 151, "id": "ac92b611", "metadata": {}, "outputs": [{"data": {"text/plain": ["<sklearn.metrics._plot.confusion_matrix.ConfusionMatrixDisplay at 0x16bba1639e0>"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ConfusionMatrixDisplay.from_estimator(clf_xgb, X_test_preprocessed, y_test, display_labels=['Stayed', 'Left'])\n"]}, {"cell_type": "markdown", "id": "1ac9917d", "metadata": {}, "source": ["### improve Model"]}, {"cell_type": "code", "execution_count": 206, "id": "fc7d3ef2", "metadata": {}, "outputs": [], "source": ["grid = {\n", "    'max_depth': [3, 4, 5],\n", "    'learning_rate': [0.01, 0.1, 0.2],\n", "    'gamma': [0, 0.1, 0.25],\n", "    'reg_lambda': [0, 1, 10],\n", "    'scale_pos_weight': [1, 3, 5]\n", "}\n", "\n", "optimal_params = GridSearchCV(\n", "    clf_xgb,\n", "    grid,\n", "    cv=5,\n", "    scoring='roc_auc',\n", "    verbose=0)\n", "\n", "# optimal_params.fit(X_train_preprocessed, y_train,\n", "#             eval_set=[(X_test_preprocessed, y_test)],\n", "#             )\n", "# display(optimal_params.best_score_, optimal_params.best_params_)\n", "\n", "best_params_saved = {'gamma': 0,\n", " 'learning_rate': 0.1,\n", " 'max_depth': 5,\n", " 'reg_lambda': 10,\n", " 'scale_pos_weight': 3}\n", "\n", "best_score_saved = 0.8667926300472022"]}, {"cell_type": "code", "execution_count": 207, "id": "9b6d76f4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0]\tvalidation_0-aucpr:0.60604\n", "[1]\tvalidation_0-aucpr:0.60865\n", "[2]\tvalidation_0-aucpr:0.61295\n", "[3]\tvalidation_0-aucpr:0.61979\n", "[4]\tvalidation_0-aucpr:0.62316\n", "[5]\tvalidation_0-aucpr:0.62350\n", "[6]\tvalidation_0-aucpr:0.63643\n", "[7]\tvalidation_0-aucpr:0.64125\n", "[8]\tvalidation_0-aucpr:0.64615\n", "[9]\tvalidation_0-aucpr:0.65342\n", "[10]\tvalidation_0-aucpr:0.64766\n", "[11]\tvalidation_0-aucpr:0.66172\n", "[12]\tvalidation_0-aucpr:0.65783\n", "[13]\tvalidation_0-aucpr:0.65655\n", "[14]\tvalidation_0-aucpr:0.66223\n", "[15]\tvalidation_0-aucpr:0.66036\n", "[16]\tvalidation_0-aucpr:0.65478\n", "[17]\tvalidation_0-aucpr:0.66198\n", "[18]\tvalidation_0-aucpr:0.65778\n", "[19]\tvalidation_0-aucpr:0.65719\n", "[20]\tvalidation_0-aucpr:0.65558\n", "[21]\tvalidation_0-aucpr:0.65581\n", "[22]\tvalidation_0-aucpr:0.65712\n", "[23]\tvalidation_0-aucpr:0.65675\n"]}, {"data": {"text/html": ["<style>#sk-container-id-22 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-22 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-22 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-22 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-22 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-22 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-22 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-22 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-22 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-22 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-22 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-22 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-22 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-22 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-22 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-22 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-22 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-22 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-22 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-22 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-22 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-22 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-22 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-22 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-22 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-22 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-22 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-22 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-22 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-22 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-22 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-22 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-22\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=10,\n", "              enable_categorical=False, eval_metric=&#x27;aucpr&#x27;, feature_types=None,\n", "              gamma=0, grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=0.1, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=5, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=None, n_jobs=None,\n", "              num_parallel_tree=None, random_state=42, ...)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-22\" type=\"checkbox\" checked><label for=\"sk-estimator-id-22\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;XGBClassifier<span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=10,\n", "              enable_categorical=False, eval_metric=&#x27;aucpr&#x27;, feature_types=None,\n", "              gamma=0, grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=0.1, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=5, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=None, n_jobs=None,\n", "              num_parallel_tree=None, random_state=42, ...)</pre></div> </div></div></div></div>"], "text/plain": ["XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=10,\n", "              enable_categorical=False, eval_metric='aucpr', feature_types=None,\n", "              gamma=0, grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=0.1, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=5, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=None, n_jobs=None,\n", "              num_parallel_tree=None, random_state=42, ...)"]}, "execution_count": 207, "metadata": {}, "output_type": "execute_result"}], "source": ["clf_xgb_optimal = xgb.XGBClassifier(**best_params_saved, objective='binary:logistic', missing=np.nan,\n", "                            early_stopping_rounds=10,\n", "                            eval_metric='aucpr',\n", "                            random_state=42)\n", "clf_xgb_optimal.fit(X_train_preprocessed, y_train,\n", "            eval_set=[(X_test_preprocessed, y_test)],\n", "            )\n"]}, {"cell_type": "code", "execution_count": 208, "id": "d68a9539", "metadata": {}, "outputs": [{"data": {"text/plain": ["<sklearn.metrics._plot.confusion_matrix.ConfusionMatrixDisplay at 0x16bbbde41d0>"]}, "execution_count": 208, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ConfusionMatrixDisplay.from_estimator(clf_xgb_optimal, X_test_preprocessed, y_test, display_labels=['Stayed', 'Left'])"]}], "metadata": {"kernelspec": {"display_name": "ml_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}