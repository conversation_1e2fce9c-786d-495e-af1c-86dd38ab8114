
# Import core data manipulation and visualization libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')


# Import sklearn modules for machine learning
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder


# Import base models for stacking - diverse algorithms for better ensemble
# Note: All models must support predict_proba for stack_method='predict_proba'


# Import stacking utilities
from sklearn.ensemble import StackingClassifier


# Import evaluation metrics
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score

# Import utilities for saving models
import joblib

# Set visualization style for better plots
sns.set_palette("husl")
plt.style.use('dark_background')  # Matplotlib's dark theme


# Set random seed for reproducibility
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)




# Load the Wine Quality dataset - Portuguese "Vinho Verde" wine
# This dataset contains physicochemical properties and quality ratings
url = 'https://archive.ics.uci.edu/ml/machine-learning-databases/wine-quality/winequality-red.csv'

# Load with semicolon separator
wine_df = pd.read_csv(url, sep=';')

# Display basic dataset information
print(f"Dataset shape: {wine_df.shape}")
print(f"Number of samples: {wine_df.shape[0]:,}")
print(f"Number of features: {wine_df.shape[1]-1} (excluding target)")
print(f"\nFeatures: {list(wine_df.columns)[:-1]}")
print(f"Target: {wine_df.columns[-1]}")
print(f"Target unique: {wine_df['quality'].unique()}")

# Show first few samples
print("\nFirst 5 samples:")
display(wine_df.head())



# Check for data quality issues
print("Data Quality Check:")
print("="*50)
print(f"Missing values: {wine_df.isnull().sum().sum()}")
print(f"Duplicate rows: {wine_df.duplicated().sum()}")

# Analyze quality distribution
quality_counts = wine_df['quality'].value_counts().sort_index()
print("\nQuality Distribution:")
for quality, count in quality_counts.items():
    percentage = count / len(wine_df) * 100
    print(f"  Quality {quality}: {count:4d} samples ({percentage:5.1f}%)")

# Statistical summary
print("\nFeature Statistics:")
display(wine_df.describe().round(2))

# Convert quality to categorical classes for better learning
# Group: 3-4 as 'Low', 5-6 as 'Medium', 7-8 as 'High'
def categorize_quality(quality):
    if quality <= 4:
        return 'Low'
    elif quality <= 6:
        return 'Medium'
    else:
        return 'High'

wine_df['quality_category'] = wine_df['quality'].apply(categorize_quality)
print("\nQuality Categories:")
category_counts = wine_df['quality_category'].value_counts()
for category, count in category_counts.items():
    print(f"  {category:6}: {count:4d} samples ({count/len(wine_df)*100:5.1f}%)")


# Analyze key chemical relationships with quality
fig, axes = plt.subplots(2, 3, figsize=(15, 10))
axes = axes.ravel()

# Select key features known to affect wine quality
key_features = ['alcohol', 'volatile acidity', 'citric acid', 
                'residual sugar', 'sulphates', 'pH']

for idx, feature in enumerate(key_features):
    # Box plot by quality category
    wine_df.boxplot(column=feature, by='quality_category', ax=axes[idx])
    axes[idx].set_title(f'{feature.title()} by Quality')
    axes[idx].set_xlabel('Quality Category')
    axes[idx].set_ylabel(feature.title())
    axes[idx].get_figure().suptitle('')  # Remove automatic title

plt.suptitle('Wine Chemistry vs Quality Analysis', fontsize=16, fontweight='bold', y=1.02)
plt.tight_layout()
plt.show()

# Correlation analysis
plt.figure(figsize=(12, 10))
correlation_matrix = wine_df.drop(['quality_category'], axis=1).corr()
mask = np.triu(np.ones_like(correlation_matrix), k=1)
sns.heatmap(correlation_matrix, annot=True, fmt='.2f', cmap='coolwarm', 
            center=0, mask=mask, square=True, linewidths=1)
plt.title('Feature Correlation Matrix', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

# Key insights
quality_corr = correlation_matrix['quality'].sort_values(ascending=False)
print("\nTop Features Correlated with Quality:")
print("="*50)
for feature, corr in quality_corr[1:6].items():
    print(f"  {feature:20}: {corr:+.3f}")
print("\nBottom Features Correlated with Quality:")
for feature, corr in quality_corr[-5:].items():
    print(f"  {feature:20}: {corr:+.3f}")



# Separate features and target
# Drop both 'quality' (original target) and 'quality_category' (categorical version)
X = wine_df.drop(['quality', 'quality_category'], axis=1)
y = wine_df['quality']

# Encode target labels for sklearn
label_encoder = LabelEncoder()
y_encoded = label_encoder.fit_transform(y)


# Perform stratified train-test split to maintain class balance
X_train, X_test, y_train, y_test = train_test_split(
    X, y_encoded, 
    test_size=0.2,           # 20% for testing
    random_state=RANDOM_STATE,
    stratify=y_encoded        # Maintain class proportions
)

print(f"Training set: {X_train.shape[0]} samples")
print(f"Test set: {X_test.shape[0]} samples")
print(f"\nClass distribution maintained:")
unique, counts = np.unique(y_train, return_counts=True)
for label, count in zip(unique, counts):
    class_name = label_encoder.inverse_transform([label])[0]
    print(f"  {class_name:6}: Train={count:3d} ({count/len(y_train)*100:.1f}%)")

# Store class names for later use
class_names = label_encoder.classes_


# Initialize and fit scaler on training data only (prevent data leakage)
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)  # Only transform, don't fit

# Convert back to DataFrame for convenience
X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)

# Define diverse base models for stacking
from sklearn.linear_model import LogisticRegression
from sklearn.neighbors import KNeighborsClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier, GradientBoostingClassifier
from sklearn.naive_bayes import GaussianNB

# Each model captures different patterns in wine chemistry
# All models must support predict_proba for stack_method='predict_proba'
base_models = [
    ('lr', LogisticRegression(random_state=RANDOM_STATE, max_iter=1000)),      # Linear boundaries
    ('knn', KNeighborsClassifier(n_neighbors=7)),                             # Local patterns
    ('tree', DecisionTreeClassifier(random_state=RANDOM_STATE, max_depth=5)),  # Simple rules
    ('svm', SVC(probability=True, random_state=RANDOM_STATE, kernel='rbf')),   # Complex boundaries
    ('rf', RandomForestClassifier(n_estimators=100, random_state=RANDOM_STATE)), # Ensemble
    ('extra', ExtraTreesClassifier(n_estimators=100, random_state=RANDOM_STATE)), # Extra randomization
    ('gb', GradientBoostingClassifier(n_estimators=100, random_state=RANDOM_STATE)), # Boosting
    ('nb', GaussianNB())                                                      # Probabilistic
]

print("Base Models for Stacking:")
print("="*60)
for name, model in base_models:
    model_type = model.__class__.__name__
    print(f"{name.upper():6} - {model_type:30}")


# Train and evaluate individual models using cross-validation
print("\nIndividual Model Performance (5-Fold CV):")
print("="*60)

individual_scores = {}
trained_models = {}

for name, model in base_models:
    # Cross-validation for robust evaluation
    cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='accuracy')
    individual_scores[name] = cv_scores.mean()
    
    # Train on full training set for later use
    model.fit(X_train_scaled, y_train)
    trained_models[name] = model
    
    # Display results with performance indicators
    mean_score = cv_scores.mean()
    if mean_score >= 0.60:
        indicator = "✓✓"
    elif mean_score >= 0.55:
        indicator = "✓"
    else:
        indicator = "○"
    
    print(f"{indicator} {name.upper():6} Accuracy: {mean_score:.4f} (+/- {cv_scores.std():.4f})")

# Identify best individual model
best_individual = max(individual_scores.items(), key=lambda x: x[1])
worst_individual = min(individual_scores.items(), key=lambda x: x[1])
avg_individual = np.mean(list(individual_scores.values()))

print(f"\n Performance Summary:")
print(f" Best model:  {best_individual[0].upper()} ({best_individual[1]:.4f})")
print(f" Worst model: {worst_individual[0].upper()} ({worst_individual[1]:.4f})")
print(f" Average:     {avg_individual:.4f}")
print(f" Range:       {best_individual[1] - worst_individual[1]:.4f}")


# Create StackingClassifier with cross-validation for meta-features
stacking_clf = StackingClassifier(
    estimators=base_models,
    final_estimator=LogisticRegression(random_state=RANDOM_STATE, max_iter=1000),
    cv=5,
    stack_method='predict_proba'
)


print("Stacking Configuration:")
print("="*60)
print(f"Number of base models: {len(base_models)}")
print(f"Meta-learner: {stacking_clf.final_estimator.__class__.__name__}")
print(f"Cross-validation folds: {stacking_clf.cv}")
print(f"Stack method: {stacking_clf.stack_method}")
print(f"\nMeta-features per sample: {len(base_models) * len(class_names)}")
print(f"  ({len(base_models)} models × {len(class_names)} classes)")


# Train the stacking classifier
print("\n Training Stacking Classifier...")
print("This involves:")
print("  1. Training base models with cross-validation")
print("  2. Generating out-of-fold predictions")
print("  3. Training meta-model on these predictions")
print()

import time
start_time = time.time()

# Fit the stacking model
stacking_clf.fit(X_train_scaled, y_train)

training_time = time.time() - start_time

# Make predictions on test set
stacking_pred = stacking_clf.predict(X_test_scaled)
stacking_proba = stacking_clf.predict_proba(X_test_scaled)

# Calculate accuracy
stacking_accuracy = accuracy_score(y_test, stacking_pred)

print(f"✓ Training completed in {training_time:.1f} seconds")
print(f"\n Stacking Test Accuracy: {stacking_accuracy:.4f}")
print(f" Best individual accuracy: {best_individual[1]:.4f}")
print(f" Improvement over best: {(stacking_accuracy - best_individual[1])*100:+.2f}%")
print(f" Improvement over average: {(stacking_accuracy - avg_individual)*100:+.2f}%")



# Detailed performance evaluation
print("Detailed Performance Report:")
print("="*60)

# Convert predictions back to labels
y_test_labels = label_encoder.inverse_transform(y_test)
stacking_pred_labels = label_encoder.inverse_transform(stacking_pred)

# Classification report
print(classification_report(y_test_labels, stacking_pred_labels, 
                           target_names=class_names))




# Import ConfusionMatrixDisplay for cleaner visualization


'''
# Create confusion matrix using ConfusionMatrixDisplay [[1]]
fig, ax = plt.subplots(figsize=(8, 6))
disp = ConfusionMatrixDisplay.from_predictions(
    y_test,
    stacking_pred,
    display_labels=class_names,
    cmap='Blues',
    ax=ax,
    values_format='d',
    colorbar=True
)

# Customize the display
ax.set_title('Stacking Classifier - Confusion Matrix', fontsize=14, fontweight='bold')
ax.set_xlabel('Predicted Quality', fontsize=12)
ax.set_ylabel('True Quality', fontsize=12)

# Add text annotations for percentages (optional enhancement)
cm = disp.confusion_matrix
for i in range(len(cm)):
    for j in range(len(cm)):
        percentage = cm[i, j] / cm.sum() * 100
        ax.text(j, i + 0.15, f'({percentage:.1f}%)', 
               ha='center', va='center', fontsize=9, color='gray')

plt.tight_layout()
plt.show()

# Per-class accuracy
print("\nPer-Class Accuracy:")
for i, class_name in enumerate(class_names):
    if cm[i, :].sum() > 0:  # Avoid division by zero
        class_acc = cm[i, i] / cm[i, :].sum()
        n_samples = cm[i, :].sum()
        print(f"  {class_name:8}: {class_acc:.4f} ({n_samples} samples)")

# Overall metrics
precision = precision_score(y_test, stacking_pred, average='weighted')
recall = recall_score(y_test, stacking_pred, average='weighted')
f1 = f1_score(y_test, stacking_pred, average='weighted')

print(f"\nWeighted Metrics:")
print(f"  Precision: {precision:.4f}")
print(f"  Recall:    {recall:.4f}")
print(f"  F1-Score:  {f1:.4f}")
'''


'''
# Compare all models on test set
print("Model Comparison on Test Set:")
print("="*60)

results = {}

# Evaluate individual models
for name, model in trained_models.items():
    pred = model.predict(X_test_scaled)
    acc = accuracy_score(y_test, pred)
    results[name] = acc
    indicator = "✓" if acc >= 0.55 else "○"
    print(f"{indicator} {name.upper():8} Accuracy: {acc:.4f}")

# Add stacking results
results['STACKING'] = stacking_accuracy
print(f"\n {'STACKING':8} Accuracy: {stacking_accuracy:.4f} ")

# Visualize comparison
plt.figure(figsize=(12, 7))

# Sort models by performance
sorted_results = dict(sorted(results.items(), key=lambda x: x[1]))
models = list(sorted_results.keys())
scores = list(sorted_results.values())
colors = ['red' if m == 'STACKING' else 'skyblue' for m in models]

bars = plt.barh(models, scores, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)

# Add value labels on bars
for bar, score in zip(bars, scores):
    width = bar.get_width()
    plt.text(width + 0.002, bar.get_y() + bar.get_height()/2,
             f'{score:.3f}', ha='left', va='center', fontweight='bold')

# Add reference lines
plt.axvline(x=avg_individual, color='gray', linestyle='--', alpha=0.5, 
           label=f'Average ({avg_individual:.3f})')
plt.axvline(x=best_individual[1], color='green', linestyle='--', alpha=0.5,
           label=f'Best Individual ({best_individual[1]:.3f})')

plt.xlabel('Accuracy', fontsize=12)
plt.ylabel('Model', fontsize=12)
plt.title('Wine Quality Prediction: Individual Models vs Stacking Ensemble', 
         fontsize=14, fontweight='bold')
plt.xlim([min(scores) * 0.95, max(scores) * 1.02])
plt.legend(loc='lower right')
plt.grid(axis='x', alpha=0.3)
plt.tight_layout()
plt.show()

# Calculate improvement statistics
improvements = {}
for name, score in results.items():
    if name != 'STACKING':
        improvements[name] = (stacking_accuracy - score) * 100

print(f"\n Stacking Improvements:")
print("="*40)
for name, improvement in sorted(improvements.items(), key=lambda x: x[1], reverse=True):
    print(f"  vs {name:8}: {improvement:+6.2f}%")

print(f"\n Summary Statistics:")
print(f"  Average improvement: {np.mean(list(improvements.values())):+.2f}%")
print(f"  Median improvement:  {np.median(list(improvements.values())):+.2f}%")
print(f"  Max improvement:     {max(improvements.values()):+.2f}%")
print(f"  Min improvement:     {min(improvements.values()):+.2f}%")

'''


'''
# Analyze meta-model coefficients to understand model importance
meta_model = stacking_clf.final_estimator_
n_classes = len(class_names)

# Get coefficients (shape: n_classes x n_features for multi-class)
if hasattr(meta_model, 'coef_'):
    coef_matrix = meta_model.coef_
    
    # Calculate average importance per base model
    model_importance = {}
    for model_idx, (name, _) in enumerate(base_models):
        # Each model contributes n_classes features
        start_idx = model_idx * n_classes
        end_idx = start_idx + n_classes
        
        # Average absolute coefficients across all classes and features
        importance = np.abs(coef_matrix[:, start_idx:end_idx]).mean()
        model_importance[name] = importance
    
    # Visualize model importance
    plt.figure(figsize=(10, 6))
    sorted_importance = dict(sorted(model_importance.items(), key=lambda x: x[1]))
    models = list(sorted_importance.keys())
    importances = list(sorted_importance.values())
    
    bars = plt.barh(models, importances, color='coral', alpha=0.7)
    plt.xlabel('Average Coefficient Magnitude')
    plt.ylabel('Base Model')
    plt.title('Base Model Importance in Meta-Model', fontsize=14, fontweight='bold')
    
    # Add value labels
    for bar, imp in zip(bars, importances):
        width = bar.get_width()
        plt.text(width + 0.001, bar.get_y() + bar.get_height()/2,
                f'{imp:.3f}', ha='left', va='center')
    
    plt.tight_layout()
    plt.show()
    
    # Summary statistics
    print("Base Model Importance Ranking:")
    print("="*40)
    for rank, (model, imp) in enumerate(sorted(model_importance.items(), 
                                               key=lambda x: x[1], reverse=True), 1):
        print(f"  {rank}. {model.upper():8}: {imp:.4f}")

# Analyze feature importance from Random Forest in ensemble
rf_model = trained_models['rf']
feature_importance = pd.DataFrame({
    'feature': X_train.columns,
    'importance': rf_model.feature_importances_
}).sort_values('importance', ascending=False)

print("\nTop Wine Chemistry Features (from RF):")
print("="*40)
for idx, row in feature_importance.head(8).iterrows():
    print(f"  {row['feature']:20}: {row['importance']:.4f}")

'''


'''
# Analyze predictions for specific wine samples
sample_indices = [10, 50, 100]  # Select diverse samples

for sample_idx in sample_indices:
    sample = X_test_scaled.iloc[[sample_idx]]
    true_label = label_encoder.inverse_transform([y_test[sample_idx]])[0]
    
    print(f"\n" + "="*60)
    print(f"Wine Sample {sample_idx} Analysis:")
    print(f"True Quality: {true_label}")
    
    # Key chemistry values (unscaled for interpretation)
    original_sample = X_test.iloc[sample_idx]
    print(f"\nKey Chemistry:")
    print(f"  Alcohol:          {original_sample['alcohol']:.1f}%")
    print(f"  Volatile Acidity: {original_sample['volatile acidity']:.2f}")
    print(f"  Citric Acid:      {original_sample['citric acid']:.2f}")
    print(f"  Sulphates:        {original_sample['sulphates']:.2f}")
    
    print(f"\nBase Model Predictions:")
    base_predictions = {}
    for name, model in trained_models.items():
        pred = model.predict(sample)[0]
        pred_label = label_encoder.inverse_transform([pred])[0]
        pred_proba = model.predict_proba(sample)[0]
        confidence = pred_proba.max()
        base_predictions[name] = pred_label
        
        correct = "✓" if pred_label == true_label else "✗"
        print(f"  {correct} {name:8}: {pred_label:6} (confidence: {confidence:.2f})")
    
    # Stacking prediction
    stacking_pred = stacking_clf.predict(sample)[0]
    stacking_pred_label = label_encoder.inverse_transform([stacking_pred])[0]
    stacking_proba = stacking_clf.predict_proba(sample)[0]
    stacking_conf = stacking_proba.max()
    
    correct = "✓" if stacking_pred_label == true_label else "✗"
    print(f"\n{correct} STACKING: {stacking_pred_label:6} (confidence: {stacking_conf:.2f})")
    
    # Consensus analysis
    votes = pd.Series(list(base_predictions.values())).value_counts()
    print(f"\nConsensus: {votes.to_dict()}")
    print(f"Majority vote would predict: {votes.index[0]}")
    
print("\n Key Insight: Stacking learns when to trust which models!")
'''


'''
# Analyze prediction confidence distribution
all_proba = stacking_clf.predict_proba(X_test_scaled)
max_proba = np.max(all_proba, axis=1)

# Ensure stacking_pred is an array
stacking_pred = stacking_clf.predict(X_test_scaled)
# Convert to numpy array if needed
if not isinstance(stacking_pred, np.ndarray):
    stacking_pred = np.array(stacking_pred)

# Confidence by correctness
correct_mask = stacking_pred == y_test
correct_conf = max_proba[correct_mask]
incorrect_conf = max_proba[~correct_mask]

# Visualize confidence distributions
fig, axes = plt.subplots(1, 2, figsize=(12, 5))

# Histogram of all confidences
axes[0].hist(max_proba, bins=30, edgecolor='black', alpha=0.7, color='blue')
axes[0].axvline(max_proba.mean(), color='red', linestyle='--', linewidth=2,
               label=f'Mean: {max_proba.mean():.3f}')
axes[0].set_xlabel('Prediction Confidence')
axes[0].set_ylabel('Frequency')
axes[0].set_title('Distribution of Prediction Confidence')
axes[0].legend()
axes[0].grid(alpha=0.3)

# Box plot comparison
bp = axes[1].boxplot([correct_conf, incorrect_conf], 
                     labels=['Correct', 'Incorrect'],
                     patch_artist=True)
bp['boxes'][0].set_facecolor('lightgreen')
bp['boxes'][1].set_facecolor('lightcoral')
axes[1].set_ylabel('Prediction Confidence')
axes[1].set_title('Confidence by Prediction Correctness')
axes[1].grid(axis='y', alpha=0.3)

plt.suptitle('Stacking Model Confidence Analysis', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

# Statistics
print("Confidence Statistics:")
print("="*40)
print(f"Overall mean confidence:    {max_proba.mean():.3f}")
print(f"Correct predictions mean:   {correct_conf.mean():.3f}")
print(f"Incorrect predictions mean: {incorrect_conf.mean():.3f}")
print(f"Confidence gap:            {correct_conf.mean() - incorrect_conf.mean():.3f}")

# Confidence thresholds - FIXED VERSION
high_conf_threshold = 0.7
low_conf_threshold = 0.5

# Method 1: Using numpy where to get indices
high_conf_indices = np.where(max_proba >= high_conf_threshold)[0]
low_conf_indices = np.where(max_proba < low_conf_threshold)[0]

# High confidence analysis
n_high_conf = len(high_conf_indices)
print(f"\nHigh Confidence (>={high_conf_threshold:.1f}):")
print(f"  Samples:  {n_high_conf} ({n_high_conf/len(y_test)*100:.1f}%)")

if n_high_conf > 0:
    # Extract predictions and labels for high confidence samples
    high_conf_predictions = stacking_pred[high_conf_indices]
    high_conf_labels = y_test.iloc[high_conf_indices] if hasattr(y_test, 'iloc') else y_test[high_conf_indices]
    
    # Calculate accuracy
    high_conf_acc = accuracy_score(high_conf_labels, high_conf_predictions)
    print(f"  Accuracy: {high_conf_acc:.3f}")

# Low confidence analysis
n_low_conf = len(low_conf_indices)
if n_low_conf > 0:
    print(f"\nLow Confidence (<{low_conf_threshold:.1f}):")
    print(f"  Samples:  {n_low_conf} ({n_low_conf/len(y_test)*100:.1f}%)")
    
    # Extract predictions and labels for low confidence samples
    low_conf_predictions = stacking_pred[low_conf_indices]
    low_conf_labels = y_test.iloc[low_conf_indices] if hasattr(y_test, 'iloc') else y_test[low_conf_indices]
    
    # Calculate accuracy
    low_conf_acc = accuracy_score(low_conf_labels, low_conf_predictions)
    print(f"  Accuracy: {low_conf_acc:.3f}")
    print(f"  ⚠ These predictions need expert review")
'''


'''
# Save complete model package for production deployment
model_package = {
    'stacking_model': stacking_clf,
    'scaler': scaler,
    'label_encoder': label_encoder,
    'feature_names': X.columns.tolist(),
    'class_names': class_names.tolist(),
    'model_performance': {
        'test_accuracy': stacking_accuracy,
        'individual_scores': individual_scores,
        'improvement_over_best': (stacking_accuracy - best_individual[1])*100,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    },
    'base_models': [name for name, _ in base_models],
    'thresholds': {
        'high_confidence': 0.7,
        'low_confidence': 0.5
    },
    'chemistry_ranges': {
        'alcohol': (X['alcohol'].min(), X['alcohol'].max()),
        'pH': (X['pH'].min(), X['pH'].max()),
        'volatile_acidity': (X['volatile acidity'].min(), X['volatile acidity'].max())
    }
}

# Save to file
model_filename = 'wine_quality_stacking_model.pkl'
joblib.dump(model_package, model_filename)
print(f"✓ Model saved to '{model_filename}'")
print(f"\nPackage contents:")
for key in model_package.keys():
    if key not in ['stacking_model', 'scaler', 'label_encoder']:
        print(f"  - {key}")

'''


# Load model package


'''

# Production prediction function
def predict_wine_quality(wine_features, return_details=False):
    """
    Predict wine quality using the stacking ensemble.
    
    Parameters:
    -----------
    wine_features : array-like of shape (n_samples, 11)
        Wine chemistry features in order:
        [fixed acidity, volatile acidity, citric acid, residual sugar,
         chlorides, free sulfur dioxide, total sulfur dioxide, density,
         pH, sulphates, alcohol]
    return_details : bool
        Whether to return detailed predictions with confidence
    
    Returns:
    --------
    predictions : array
        Predicted quality categories
    details : dict (if return_details=True)
        Detailed predictions with confidence and recommendations
    """
    # Convert to DataFrame
    feature_df = pd.DataFrame(wine_features, columns=loaded_package['feature_names'])
    
    # Validate chemistry ranges
    for feature, (min_val, max_val) in loaded_package['chemistry_ranges'].items():
        if feature in feature_df.columns:
            if (feature_df[feature] < min_val * 0.8).any() or (feature_df[feature] > max_val * 1.2).any():
                print(f"⚠ Warning: {feature} values outside typical range [{min_val:.2f}, {max_val:.2f}]")
    
    # Scale features
    features_scaled = loaded_package['scaler'].transform(feature_df)
    
    # Make predictions
    predictions_encoded = loaded_package['stacking_model'].predict(features_scaled)
    probabilities = loaded_package['stacking_model'].predict_proba(features_scaled)
    
    # Decode predictions
    predictions = loaded_package['label_encoder'].inverse_transform(predictions_encoded)
    
    if return_details:
        details = []
        for i in range(len(predictions)):
            confidence = probabilities[i].max()
            
            # Generate recommendation based on confidence
            if confidence >= loaded_package['thresholds']['high_confidence']:
                recommendation = "High confidence - Ready for bottling"
            elif confidence >= loaded_package['thresholds']['low_confidence']:
                recommendation = "Moderate confidence - Consider expert tasting"
            else:
                recommendation = "Low confidence - Requires expert evaluation"
            
            # Probability distribution
            prob_dist = {class_name: prob for class_name, prob in 
                        zip(loaded_package['class_names'], probabilities[i])}
            
            details.append({
                'prediction': predictions[i],
                'confidence': confidence,
                'probabilities': prob_dist,
                'recommendation': recommendation
            })
        
        return predictions, details
    
    return predictions

'''


'''
# Test production function with example wines
test_wines = [
    [7.4, 0.70, 0.00, 1.9, 0.076, 11, 34, 0.9978, 3.51, 0.56, 9.4],   # Typical medium
    [8.8, 0.28, 0.41, 1.8, 0.045, 15, 35, 0.9917, 3.36, 0.80, 11.9],  # Likely high
    [7.2, 0.73, 0.02, 2.5, 0.076, 10, 28, 0.9980, 3.35, 0.46, 9.2]    # Likely low
]

print("Production Model Test:")
print("="*60)

predictions, details = predict_wine_quality(test_wines, return_details=True)

for i, (pred, detail) in enumerate(zip(predictions, details)):
    print(f"\nWine Sample {i+1}:")
    print(f"  Quality: {pred}")
    print(f"  Confidence: {detail['confidence']:.1%}")
    print(f"  Recommendation: {detail['recommendation']}")
    print(f"  Probability Distribution:")
    for quality, prob in detail['probabilities'].items():
        bar = '█' * int(prob * 20)
        print(f"    {quality:6}: {bar:20} {prob:.3f}")

print("\n✓ Production deployment ready!")
print(f"Model accuracy: {loaded_package['model_performance']['test_accuracy']:.1%}")

'''
