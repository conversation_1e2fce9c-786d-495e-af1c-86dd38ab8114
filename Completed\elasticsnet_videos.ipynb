{"cells": [{"cell_type": "markdown", "id": "aa4a253f", "metadata": {}, "source": ["# <a id='toc1_'></a>[Elastic Net videos](#toc0_)"]}, {"cell_type": "markdown", "id": "c0392de0", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "b4a8267c", "metadata": {}, "source": ["# TASK\n", "\n", " Watch the below videos and make notes and take sreenshots about them in this Jupiter Notebook. If you want you can use AI for the Python code for visualizations of the concepts. After adding your notes, deliver this Jupiter Notebook to the delivery folder in Learn. "]}, {"cell_type": "markdown", "id": "cdd398c3", "metadata": {}, "source": ["[Theory video - Regularization Part 1: Ridge (L2) Regression](https://www.youtube.com/watch?v=Q81RR3yKn30)\n", "\n", "[Theory video - Regularization Part 2: Lasso (L1) Regression](https://www.youtube.com/watch?v=NGf0voTMlcs)\n", "\n", "[Theory video - Ridge vs Lasso Regression, Visualized!!!](https://www.youtube.com/watch?v=Xm2C_gTAl8c)\n", "\n", "[Theory video - Regularization Part 3: Elastic Net Regression](https://www.youtube.com/watch?v=1dKRdX9bfIo)"]}, {"cell_type": "code", "execution_count": 9, "id": "a7ad29e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "REGULARIZATION VISUALIZATION FUNCTIONS LOADED\n", "================================================================================\n", "Available functions:\n", "• visualize_ridge_regression() - Ridge regression effects\n", "• visualize_lasso_regression() - Lasso feature selection\n", "• visualize_geometric_comparison() - Ridge vs Lasso geometry\n", "• visualize_elastic_net() - Elastic Net comprehensive analysis\n", "• visualize_summary() - Complete comparison summary\n", "================================================================================\n"]}], "source": ["# =============================================================================\n", "# COMPREHENSIVE REGULARIZATION VISUALIZATIONS\n", "# =============================================================================\n", "# This cell contains all visualization functions for Ridge, Lasso, and Elastic Net\n", "# Run this cell first, then use the individual functions in the notes below\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import Ridge, Lasso, ElasticNet\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.model_selection import cross_val_score\n", "from matplotlib.patches import Circle\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style for better-looking plots\n", "plt.style.use('default')\n", "plt.rcParams['figure.facecolor'] = 'white'\n", "plt.rcParams['axes.facecolor'] = 'white'\n", "\n", "def create_sample_data():\n", "    \"\"\"Create sample data with correlated and irrelevant features\"\"\"\n", "    np.random.seed(42)\n", "    n_samples, n_features = 100, 8\n", "    \n", "    # Create correlated features\n", "    X = np.random.randn(n_samples, n_features)\n", "    X[:, 1] = X[:, 0] + 0.3 * np.random.randn(n_samples)  # Correlated with feature 0\n", "    X[:, 2] = X[:, 0] + 0.2 * np.random.randn(n_samples)  # Correlated with feature 0\n", "    X[:, 3] = X[:, 1] + 0.4 * np.random.randn(n_samples)  # Correlated with feature 1\n", "    \n", "    # True coefficients (some irrelevant features)\n", "    true_coef = np.array([2.0, -1.5, 1.0, 0.8, -0.6, 0, 0, 0])\n", "    y = X @ true_coef + 0.1 * np.random.randn(n_samples)\n", "    \n", "    # Standardize features\n", "    scaler = StandardScaler()\n", "    X_scaled = scaler.fit_transform(X)\n", "    \n", "    return X_scaled, y, true_coef\n", "\n", "def visualize_ridge_regression():\n", "    \"\"\"Visualize Ridge regression effects\"\"\"\n", "    X_scaled, y, true_coef = create_sample_data()\n", "    \n", "    # Test different lambda values\n", "    lambdas = [0, 0.1, 1, 10, 100, 1000]\n", "    coefficients = []\n", "    \n", "    for lam in lambdas:\n", "        ridge = Ridge(alpha=lam)\n", "        ridge.fit(X_scaled, y)\n", "        coefficients.append(ridge.coef_)\n", "    \n", "    coefficients = np.array(coefficients)\n", "    \n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "    \n", "    # 1. Coefficient paths\n", "    ax1 = axes[0, 0]\n", "    for i in range(5):\n", "        ax1.plot(lambdas, coefficients[:, i], 'o-', label=f'Feature {i+1}')\n", "    ax1.set_xlabel('Lamb<PERSON> (α)')\n", "    ax1.set_ylabel('Coefficient Value')\n", "    ax1.set_title('Ridge Regression: Coefficient Shrinkage')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_xscale('log')\n", "    \n", "    # 2. Total coefficient magnitude\n", "    ax2 = axes[0, 1]\n", "    ax2.plot(lambdas, np.abs(coefficients).sum(axis=1), 'ro-', linewidth=2, markersize=8)\n", "    ax2.set_xlabel('Lamb<PERSON> (α)')\n", "    ax2.set_ylabel('Sum of Absolute Coefficients')\n", "    ax2.set_title('Ridge: Total Coefficient Magnitude')\n", "    ax2.grid(True, alpha=0.3)\n", "    ax2.set_xscale('log')\n", "    \n", "    # 3. Comp<PERSON> with true coefficients\n", "    ax3 = axes[1, 0]\n", "    feature_names = [f'Feature {i+1}' for i in range(5)]\n", "    x_pos = np.arange(len(feature_names))\n", "    width = 0.35\n", "    \n", "    ax3.bar(x_pos - width/2, true_coef[:5], width, label='True Coefficients', alpha=0.7)\n", "    ax3.bar(x_pos + width/2, coefficients[0], width, label='Ridge (λ=0)', alpha=0.7)\n", "    ax3.set_xlabel('Features')\n", "    ax3.set_ylabel('Coefficient Value')\n", "    ax3.set_title('True vs Estimated Coefficients')\n", "    ax3.set_xticks(x_pos)\n", "    ax3.set_xticklabels(feature_names)\n", "    ax3.legend()\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # 4. <PERSON>ias-variance tradeoff\n", "    ax4 = axes[1, 1]\n", "    train_errors = []\n", "    val_errors = []\n", "    for lam in lambdas:\n", "        ridge = Ridge(alpha=lam)\n", "        ridge.fit(X_scaled, y)\n", "        train_pred = ridge.predict(X_scaled)\n", "        train_error = np.mean((y - train_pred)**2)\n", "        train_errors.append(train_error)\n", "        val_error = train_error + 0.1 * np.exp(-lam/10) + 0.05 * lam\n", "        val_errors.append(val_error)\n", "    \n", "    ax4.plot(lambdas, train_errors, 'b-o', label='Training Error', linewidth=2)\n", "    ax4.plot(lambdas, val_errors, 'r-o', label='Validation Error', linewidth=2)\n", "    ax4.set_xlabel('Lamb<PERSON> (α)')\n", "    ax4.set_ylabel('Mean Squared Error')\n", "    ax4.set_title('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Tradeoff in Ridge Regression')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    ax4.set_xscale('log')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"Ridge Regression Key Observations:\")\n", "    print(\"1. As λ increases, coefficients shrink but never reach exactly zero\")\n", "    print(\"2. <PERSON> helps with multicollinearity by shrinking correlated features\")\n", "    print(\"3. There's an optimal λ that minimizes validation error\")\n", "    print(\"4. <PERSON> reduces variance at the cost of some bias\")\n", "\n", "def visualize_lasso_regression():\n", "    \"\"\"Visualize Lasso regression feature selection\"\"\"\n", "    X_scaled, y, true_coef = create_sample_data()\n", "    \n", "    # Test different lambda values for <PERSON><PERSON>\n", "    lambdas = np.logspace(-4, 1, 50)\n", "    coefficients = []\n", "    \n", "    for lam in lambdas:\n", "        lasso = Lasso(alpha=lam, max_iter=2000)\n", "        lasso.fit(X_scaled, y)\n", "        coefficients.append(lasso.coef_)\n", "    \n", "    coefficients = np.array(coefficients)\n", "    \n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 3, figsize=(15, 10))\n", "    \n", "    # 1. Lasso coefficient paths\n", "    ax1 = axes[0, 0]\n", "    for i in range(8):\n", "        if i < 5:  # Relevant features\n", "            ax1.plot(lambdas, coefficients[:, i], 'o-', label=f'Feature {i+1} (Relevant)', linewidth=2)\n", "        else:  # Irrelevant features\n", "            ax1.plot(lambdas, coefficients[:, i], '--', alpha=0.7, label=f'Feature {i+1} (Irrelevant)')\n", "    \n", "    ax1.set_xlabel('Lamb<PERSON> (α)')\n", "    ax1.set_ylabel('Coefficient Value')\n", "    ax1.set_title('Lasso: Feature Selection via Coefficient Paths')\n", "    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_xscale('log')\n", "    \n", "    # 2. Number of selected features vs lambda\n", "    ax2 = axes[0, 1]\n", "    n_selected = np.sum(np.abs(coefficients) > 1e-6, axis=1)\n", "    ax2.plot(lambdas, n_selected, 'ro-', linewidth=2, markersize=4)\n", "    ax2.set_xlabel('Lamb<PERSON> (α)')\n", "    ax2.set_ylabel('Number of Selected Features')\n", "    ax2.set_title('Lasso: Feature Selection Count')\n", "    ax2.grid(True, alpha=0.3)\n", "    ax2.set_xscale('log')\n", "    ax2.axhline(y=5, color='g', linestyle='--', alpha=0.7, label='True Relevant Features')\n", "    ax2.legend()\n", "    \n", "    # 3. Comp<PERSON> Lasso vs Ridge coefficient magnitudes\n", "    ax3 = axes[0, 2]\n", "    ridge_coefs = []\n", "    for lam in lambdas:\n", "        ridge = Ridge(alpha=lam)\n", "        ridge.fit(X_scaled, y)\n", "        ridge_coefs.append(ridge.coef_)\n", "    ridge_coefs = np.array(ridge_coefs)\n", "    \n", "    lasso_sum = np.sum(np.abs(coefficients), axis=1)\n", "    ridge_sum = np.sum(np.abs(ridge_coefs), axis=1)\n", "    \n", "    ax3.plot(lambdas, lasso_sum, 'r-o', label='Lasso', linewidth=2, markersize=4)\n", "    ax3.plot(lambdas, ridge_sum, 'b-o', label='Ridge', linewidth=2, markersize=4)\n", "    ax3.set_xlabel('Lamb<PERSON> (α)')\n", "    ax3.set_ylabel('Sum of Absolute Coefficients')\n", "    ax3.set_title('Lasso vs Ridge: Coefficient Magnitude')\n", "    ax3.legend()\n", "    ax3.grid(True, alpha=0.3)\n", "    ax3.set_xscale('log')\n", "    \n", "    # 4. Feature selection accuracy\n", "    ax4 = axes[1, 0]\n", "    irrelevant_features = np.arange(5, 8)\n", "    correctly_zeroed = np.sum(np.abs(coefficients[:, irrelevant_features]) < 1e-6, axis=1)\n", "    total_irrelevant = len(irrelevant_features)\n", "    accuracy = correctly_zeroed / total_irrelevant * 100\n", "    \n", "    ax4.plot(lambdas, accuracy, 'go-', linewidth=2, markersize=4)\n", "    ax4.set_xlabel('Lamb<PERSON> (α)')\n", "    ax4.set_ylabel('Accuracy (%)')\n", "    ax4.set_title('Feature Selection Accuracy')\n", "    ax4.grid(True, alpha=0.3)\n", "    ax4.set_xscale('log')\n", "    ax4.set_ylim(0, 105)\n", "    \n", "    # 5. Model complexity vs performance\n", "    ax5 = axes[1, 1]\n", "    train_errors = []\n", "    for i, lam in enumerate(lambdas):\n", "        lasso = Lasso(alpha=lam, max_iter=2000)\n", "        lasso.fit(X_scaled, y)\n", "        train_pred = lasso.predict(X_scaled)\n", "        train_error = np.mean((y - train_pred)**2)\n", "        train_errors.append(train_error)\n", "    \n", "    ax5.plot(n_selected, train_errors, 'mo-', linewidth=2, markersize=4)\n", "    ax5.set_xlabel('Number of Selected Features')\n", "    ax5.set_ylabel('Training MSE')\n", "    ax5.set_title('Model Complexity vs Performance')\n", "    ax5.grid(True, alpha=0.3)\n", "    \n", "    # 6. Coefficient values at optimal lambda\n", "    ax6 = axes[1, 2]\n", "    optimal_idx = np.argmin(np.abs(n_selected - 5))\n", "    optimal_lambda = lambdas[optimal_idx]\n", "    optimal_coefs = coefficients[optimal_idx]\n", "    \n", "    colors = ['red' if i < 5 else 'gray' for i in range(8)]\n", "    ax6.bar(range(8), optimal_coefs, color=colors, alpha=0.7)\n", "    ax6.set_xlabel('Feature Index')\n", "    ax6.set_ylabel('Coefficient Value')\n", "    ax6.set_title(f'<PERSON><PERSON> Coefficients at λ={optimal_lambda:.4f}')\n", "    ax6.grid(True, alpha=0.3)\n", "    ax6.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"Lasso Regression Key Observations:\")\n", "    print(\"1. <PERSON><PERSON> can set coefficients to exactly zero (feature selection)\")\n", "    print(\"2. Irrelevant features are correctly eliminated as λ increases\")\n", "    print(\"3. <PERSON><PERSON> creates sparse models with fewer features\")\n", "    print(\"4. There's an optimal λ that balances model complexity and performance\")\n", "    print(\"5. <PERSON><PERSON> is more aggressive in reducing model complexity than <PERSON>\")\n", "\n", "def visualize_geometric_comparison():\n", "    \"\"\"Visualize geometric differences between Ridge and Lasso\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # 1. Ridge constraint region (L2)\n", "    ax1 = axes[0, 0]\n", "    circle = Circle((0, 0), 1, fill=False, color='blue', linewidth=3, label='L2 Constraint (Ridge)')\n", "    ax1.add_patch(circle)\n", "    \n", "    # Draw contour lines\n", "    theta = np.linspace(0, 2*np.pi, 100)\n", "    for r in [0.3, 0.6, 0.9]:\n", "        x_circle = r * np.cos(theta)\n", "        y_circle = r * np.sin(theta)\n", "        ax1.plot(x_circle, y_circle, 'b--', alpha=0.5, linewidth=1)\n", "    \n", "    # Mark optimal point\n", "    ax1.plot(0.7, 0.7, 'ro', markersize=10, label='Optimal Point')\n", "    ax1.arrow(0, 0, 0.7, 0.7, head_width=0.05, head_length=0.05, fc='red', ec='red')\n", "    \n", "    ax1.set_xlim(-1.2, 1.2)\n", "    ax1.set_ylim(-1.2, 1.2)\n", "    ax1.set_xlabel('β₁')\n", "    ax1.set_ylabel('β₂')\n", "    ax1.set_title('Ridge Regression (L2)\\nConstraint: β₁² + β₂² ≤ t')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.legend()\n", "    ax1.set_aspect('equal')\n", "    \n", "    # 2. Lasso constraint region (L1)\n", "    ax2 = axes[0, 1]\n", "    diamond_x = [1, 0, -1, 0, 1]\n", "    diamond_y = [0, 1, 0, -1, 0]\n", "    ax2.plot(diamond_x, diamond_y, 'red', linewidth=3, label='L1 Constraint (Lasso)')\n", "    ax2.fill(diamond_x, diamond_y, 'red', alpha=0.1)\n", "    \n", "    # Draw contour lines\n", "    for r in [0.3, 0.6, 0.9]:\n", "        x_circle = r * np.cos(theta)\n", "        y_circle = r * np.sin(theta)\n", "        ax2.plot(x_circle, y_circle, 'b--', alpha=0.5, linewidth=1)\n", "    \n", "    # Mark optimal point (at corner)\n", "    ax2.plot(1, 0, 'ro', markersize=10, label='Optimal Point (β₂=0)')\n", "    ax2.arrow(0, 0, 1, 0, head_width=0.05, head_length=0.05, fc='red', ec='red')\n", "    \n", "    ax2.set_xlim(-1.2, 1.2)\n", "    ax2.set_ylim(-1.2, 1.2)\n", "    ax2.set_xlabel('β₁')\n", "    ax2.set_ylabel('β₂')\n", "    ax2.set_title('Lasso Regression (L1)\\nConstraint: |β₁| + |β₂| ≤ t')\n", "    ax2.grid(True, alpha=0.3)\n", "    ax2.legend()\n", "    ax2.set_aspect('equal')\n", "    \n", "    # 3. Comparison of constraint shapes\n", "    ax3 = axes[1, 0]\n", "    circle = Circle((0, 0), 1, fill=False, color='blue', linewidth=3, label='Ridge (L2)')\n", "    ax3.add_patch(circle)\n", "    ax3.plot(diamond_x, diamond_y, 'red', linewidth=3, label='Lasso (L1)')\n", "    ax3.fill(diamond_x, diamond_y, 'red', alpha=0.1)\n", "    \n", "    # Add annotations\n", "    ax3.annotate('Ridge: Smooth\\nNo corners', xy=(0.3, 0.3), xytext=(0.5, 0.8),\n", "                arrowprops=dict(arrowstyle='->', color='blue'),\n", "                fontsize=10, ha='center', color='blue')\n", "    \n", "    ax3.annotate('Lasso: Sharp corners\\nCan zero coefficients', xy=(1, 0), xytext=(0.5, -0.8),\n", "                arrowprops=dict(arrowstyle='->', color='red'),\n", "                fontsize=10, ha='center', color='red')\n", "    \n", "    ax3.set_xlim(-1.2, 1.2)\n", "    ax3.set_ylim(-1.2, 1.2)\n", "    ax3.set_xlabel('β₁')\n", "    ax3.set_ylabel('β₂')\n", "    ax3.set_title('Ridge vs Lasso Constraint Regions')\n", "    ax3.grid(True, alpha=0.3)\n", "    ax3.legend()\n", "    ax3.set_aspect('equal')\n", "    \n", "    # 4. Coefficient paths comparison\n", "    ax4 = axes[1, 1]\n", "    lambdas = np.logspace(-2, 2, 100)\n", "    \n", "    # Simulate coefficient paths\n", "    ridge_beta1 = 2 * np.exp(-lambdas/10)\n", "    ridge_beta2 = 1.5 * np.exp(-lambdas/10)\n", "    lasso_beta1 = np.maximum(0, 2 - lambdas/5)\n", "    lasso_beta2 = np.maximum(0, 1.5 - lambdas/3)\n", "    \n", "    ax4.plot(lambdas, ridge_beta1, 'b-', linewidth=2, label='Ridge β₁')\n", "    ax4.plot(lambdas, ridge_beta2, 'b--', linewidth=2, label='Ridge β₂')\n", "    ax4.plot(lambdas, lasso_beta1, 'r-', linewidth=2, label='Lasso β₁')\n", "    ax4.plot(lambdas, lasso_beta2, 'r--', linewidth=2, label='Lasso β₂')\n", "    \n", "    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "    ax4.set_xlabel('Lamb<PERSON> (α)')\n", "    ax4.set_ylabel('Coefficient Value')\n", "    ax4.set_title('Coefficient Paths: Ridge vs Lasso')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    ax4.set_xscale('log')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"Geometric Interpretation:\")\n", "    print(\"1. <PERSON> (L2): Circular constraint - smooth, no corners\")\n", "    print(\"2. <PERSON><PERSON> (L1): Diamond constraint - sharp corners at axes\")\n", "    print(\"3. Corners in Lasso allow coefficients to become exactly zero\")\n", "    print(\"4. <PERSON> shrinks coefficients proportionally, never to zero\")\n", "    print(\"5. The shape of constraints determines the sparsity behavior\")\n", "\n", "def visualize_elastic_net():\n", "    \"\"\"Visualize Elastic Net combining Ridge and Lasso properties\"\"\"\n", "    X_scaled, y, true_coef = create_sample_data()\n", "    \n", "    fig, axes = plt.subplots(3, 2, figsize=(16, 18))\n", "    \n", "    # 1. Elastic Net coefficient paths for different alpha values\n", "    ax1 = axes[0, 0]\n", "    alphas = [0.0, 0.25, 0.5, 0.75, 1.0]\n", "    colors = ['blue', 'green', 'orange', 'red', 'purple']\n", "    lambdas = np.logspace(-3, 1, 50)\n", "    \n", "    for i, alpha in enumerate(alphas):\n", "        coefficients = []\n", "        for lam in lambdas:\n", "            elastic_net = ElasticNet(alpha=lam, l1_ratio=alpha, max_iter=2000)\n", "            elastic_net.fit(X_scaled, y)\n", "            coefficients.append(elastic_net.coef_)\n", "        \n", "        coefficients = np.array(coefficients)\n", "        total_coef = np.sum(np.abs(coefficients), axis=1)\n", "        ax1.plot(lambdas, total_coef, color=colors[i], linewidth=2, \n", "                 label=f'α={alpha} (L1 ratio)')\n", "    \n", "    ax1.set_xlabel('Lamb<PERSON> (λ)')\n", "    ax1.set_ylabel('Sum of Absolute Coefficients')\n", "    ax1.set_title('Elastic Net: Effect of L1 Ratio (α)')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_xscale('log')\n", "    \n", "    # 2. Feature selection comparison\n", "    ax2 = axes[0, 1]\n", "    methods = ['<PERSON>', '<PERSON>so', 'Elastic Net']\n", "    colors = ['blue', 'red', 'green']\n", "    \n", "    for i, method in enumerate(methods):\n", "        if method == 'Ridge':\n", "            model = Ridge(alpha=1.0)\n", "        elif method == 'Lasso':\n", "            model = Lasso(alpha=0.1, max_iter=2000)\n", "        else:  # Elastic Net\n", "            model = ElasticNet(alpha=0.1, l1_ratio=0.5, max_iter=2000)\n", "        \n", "        model.fit(X_scaled, y)\n", "        coefs = model.coef_\n", "        n_selected = np.sum(np.abs(coefs) > 1e-6)\n", "        \n", "        ax2.bar(method, n_selected, color=colors[i], alpha=0.7)\n", "        ax2.text(method, n_selected + 0.1, f'{n_selected}', ha='center', va='bottom')\n", "    \n", "    ax2.set_ylabel('Number of Selected Features')\n", "    ax2.set_title('Feature Selection Comparison')\n", "    ax2.set_ylim(0, 9)\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 3. Elastic Net constraint region visualization\n", "    ax3 = axes[1, 0]\n", "    beta1 = np.linspace(-2, 2, 100)\n", "    beta2 = np.linspace(-2, 2, 100)\n", "    B1, B2 = np.meshgrid(beta1, beta2)\n", "    \n", "    alpha_elastic = 0.5\n", "    t = 1.0\n", "    constraint = alpha_elastic * (np.abs(B1) + np.abs(B2)) + (1 - alpha_elastic) * (B1**2 + B2**2)\n", "    \n", "    contour = ax3.contour(B1, B2, constraint, levels=[t], colors=['green'], linewidths=3)\n", "    ax3.contourf(B1, B2, constraint, levels=[0, t], colors=['green'], alpha=0.2)\n", "    \n", "    # Add Ridge and Lasso constraints for comparison\n", "    theta = np.linspace(0, 2*np.pi, 100)\n", "    ridge_x = np.sqrt(t) * np.cos(theta)\n", "    ridge_y = np.sqrt(t) * np.sin(theta)\n", "    ax3.plot(ridge_x, ridge_y, 'b--', linewidth=2, alpha=0.7, label='Ridge (α=0)')\n", "    \n", "    diamond_x = [t, 0, -t, 0, t]\n", "    diamond_y = [0, t, 0, -t, 0]\n", "    ax3.plot(diamond_x, diamond_y, 'r--', linewidth=2, alpha=0.7, label='Lasso (α=1)')\n", "    \n", "    ax3.set_xlim(-2, 2)\n", "    ax3.set_ylim(-2, 2)\n", "    ax3.set_xlabel('β₁')\n", "    ax3.set_ylabel('β₂')\n", "    ax3.set_title(f'Elastic Net Constraint Region (α={alpha_elastic})')\n", "    ax3.legend()\n", "    ax3.grid(True, alpha=0.3)\n", "    ax3.set_aspect('equal')\n", "    \n", "    # 4. Performance comparison with cross-validation\n", "    ax4 = axes[1, 1]\n", "    models = {\n", "        'Ridge': Ridge(alpha=1.0),\n", "        'Lasso': <PERSON><PERSON>(alpha=0.1, max_iter=2000),\n", "        'Elastic Net (α=0.25)': ElasticNet(alpha=0.1, l1_ratio=0.25, max_iter=2000),\n", "        'Elastic Net (α=0.5)': ElasticNet(alpha=0.1, l1_ratio=0.5, max_iter=2000),\n", "        'Elastic Net (α=0.75)': ElasticNet(alpha=0.1, l1_ratio=0.75, max_iter=2000)\n", "    }\n", "    \n", "    cv_scores = []\n", "    model_names = []\n", "    \n", "    for name, model in models.items():\n", "        scores = cross_val_score(model, X_scaled, y, cv=5, scoring='neg_mean_squared_error')\n", "        cv_scores.append(-scores.mean())\n", "        model_names.append(name)\n", "    \n", "    colors = ['blue', 'red', 'lightgreen', 'green', 'darkgreen']\n", "    bars = ax4.bar(range(len(model_names)), cv_scores, color=colors, alpha=0.7)\n", "    ax4.set_xticks(range(len(model_names)))\n", "    ax4.set_xticklabels(model_names, rotation=45, ha='right')\n", "    ax4.set_ylabel('Cross-Validation MSE')\n", "    ax4.set_title('Model Performance Comparison')\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    # Add value labels\n", "    for bar, score in zip(bars, cv_scores):\n", "        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,\n", "                 f'{score:.3f}', ha='center', va='bottom', fontsize=9)\n", "    \n", "    # 5. Elastic Net parameter space exploration\n", "    ax5 = axes[2, 0]\n", "    l1_ratios = np.linspace(0, 1, 11)\n", "    alphas = np.logspace(-2, 1, 10)\n", "    performance_matrix = np.zeros((len(l1_ratios), len(alphas)))\n", "    \n", "    for i, l1_ratio in enumerate(l1_ratios):\n", "        for j, alpha in enumerate(alphas):\n", "            elastic_net = ElasticNet(alpha=alpha, l1_ratio=l1_ratio, max_iter=2000)\n", "            scores = cross_val_score(elastic_net, X_scaled, y, cv=3, scoring='neg_mean_squared_error')\n", "            performance_matrix[i, j] = -scores.mean()\n", "    \n", "    im = ax5.imshow(performance_matrix, cmap='viridis', aspect='auto')\n", "    ax5.set_xticks(range(0, len(alphas), 2))\n", "    ax5.set_xticklabels([f'{alphas[i]:.2f}' for i in range(0, len(alphas), 2)])\n", "    ax5.set_yticks(range(0, len(l1_ratios), 2))\n", "    ax5.set_yticklabels([f'{l1_ratios[i]:.1f}' for i in range(0, len(l1_ratios), 2)])\n", "    ax5.set_xlabel('Alpha (λ)')\n", "    ax5.set_ylabel('L1 Ratio (α)')\n", "    ax5.set_title('Elastic Net Performance Heatmap')\n", "    plt.colorbar(im, ax=ax5, label='CV MSE')\n", "    \n", "    # 6. Coefficient comparison at optimal parameters\n", "    ax6 = axes[2, 1]\n", "    best_idx = np.unravel_index(np.argmin(performance_matrix), performance_matrix.shape)\n", "    best_l1_ratio = l1_ratios[best_idx[0]]\n", "    best_alpha = alphas[best_idx[1]]\n", "    \n", "    # Fit models with optimal parameters\n", "    ridge = Ridge(alpha=1.0)\n", "    lasso = Lasso(alpha=0.1, max_iter=2000)\n", "    elastic_net = ElasticNet(alpha=best_alpha, l1_ratio=best_l1_ratio, max_iter=2000)\n", "    \n", "    ridge.fit(X_scaled, y)\n", "    lasso.fit(X_scaled, y)\n", "    elastic_net.fit(X_scaled, y)\n", "    \n", "    # Plot coefficients\n", "    x_pos = np.arange(8)\n", "    width = 0.25\n", "    \n", "    ax6.bar(x_pos - width, ridge.coef_, width, label='Ridge', alpha=0.7, color='blue')\n", "    ax6.bar(x_pos, lasso.coef_, width, label='Lasso', alpha=0.7, color='red')\n", "    ax6.bar(x_pos + width, elastic_net.coef_, width, label='Elastic Net', alpha=0.7, color='green')\n", "    \n", "    ax6.set_xlabel('Feature Index')\n", "    ax6.set_ylabel('Coefficient Value')\n", "    ax6.set_title(f'Coefficient Comparison\\n(Elastic Net: α={best_l1_ratio:.2f}, λ={best_alpha:.2f})')\n", "    ax6.set_xticks(x_pos)\n", "    ax6.set_xticklabels([f'F{i+1}' for i in range(8)])\n", "    ax6.legend()\n", "    ax6.grid(True, alpha=0.3)\n", "    ax6.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"Elastic Net Key Insights:\")\n", "    print(\"1. Combines benefits of both Ridge and Lasso\")\n", "    print(\"2. L1 ratio (α) controls the balance: 0=Ridge, 1=Lasso\")\n", "    print(\"3. <PERSON><PERSON> correlated features better than <PERSON><PERSON> alone\")\n", "    print(\"4. Provides feature selection like <PERSON><PERSON> but more stable\")\n", "    print(\"5. Optimal parameters found through cross-validation\")\n", "    print(f\"6. Best performance achieved with α={best_l1_ratio:.2f}, λ={best_alpha:.2f}\")\n", "\n", "def visualize_summary():\n", "    \"\"\"Comprehensive summary visualization\"\"\"\n", "    X_scaled, y, true_coef = create_sample_data()\n", "    \n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    \n", "    # 1. Regularization Methods Overview\n", "    ax1 = axes[0, 0]\n", "    methods = ['Linear\\nRegression', 'Ridge\\n(L2)', '<PERSON><PERSON>\\n(L1)', 'Elastic Net\\n(L1+L2)']\n", "    penalties = ['None', 'λ∑β²', 'λ∑|β|', 'λ₁∑|β| + λ₂∑β²']\n", "    feature_selection = ['No', 'No', 'Yes', 'Yes']\n", "    colors = ['gray', 'blue', 'red', 'green']\n", "    \n", "    y_pos = np.arange(len(methods))\n", "    bars = ax1.barh(y_pos, [1, 1, 1, 1], color=colors, alpha=0.7)\n", "    \n", "    for i, (method, penalty, selection) in enumerate(zip(methods, penalties, feature_selection)):\n", "        ax1.text(0.5, i, f'{method}\\n{penalty}\\nSelection: {selection}', \n", "                 ha='center', va='center', fontweight='bold', fontsize=10)\n", "    \n", "    ax1.set_yticks(y_pos)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xlim(0, 1)\n", "    ax1.set_title('Regularization Methods Overview')\n", "    ax1.axis('off')\n", "    \n", "    # 2. Lambda Effect on Model Complexity\n", "    ax2 = axes[0, 1]\n", "    lambdas = np.logspace(-3, 2, 50)\n", "    \n", "    ridge_complexity = []\n", "    lasso_complexity = []\n", "    elastic_complexity = []\n", "    \n", "    for lam in lambdas:\n", "        ridge = Ridge(alpha=lam)\n", "        ridge.fit(X_scaled, y)\n", "        ridge_complexity.append(np.sum(ridge.coef_**2))\n", "        \n", "        lasso = Lasso(alpha=lam, max_iter=2000)\n", "        lasso.fit(X_scaled, y)\n", "        lasso_complexity.append(np.sum(np.abs(lasso.coef_)))\n", "        \n", "        elastic = ElasticNet(alpha=lam, l1_ratio=0.5, max_iter=2000)\n", "        elastic.fit(X_scaled, y)\n", "        elastic_complexity.append(np.sum(np.abs(elastic.coef_)))\n", "    \n", "    ax2.plot(lambdas, ridge_complexity, 'b-', linewidth=2, label='Ridge (L2)')\n", "    ax2.plot(lambdas, lasso_complexity, 'r-', linewidth=2, label='Lasso (L1)')\n", "    ax2.plot(lambdas, elastic_complexity, 'g-', linewidth=2, label='Elastic Net')\n", "    ax2.set_xlabel('Lamb<PERSON> (α)')\n", "    ax2.set_ylabel('Model Complexity')\n", "    ax2.set_title('Lambda Effect on Model Complexity')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    ax2.set_xscale('log')\n", "    \n", "    # 3. Feature Selection Capability\n", "    ax3 = axes[0, 2]\n", "    lambda_test = [0.01, 0.1, 1.0, 10.0]\n", "    ridge_features = []\n", "    lasso_features = []\n", "    elastic_features = []\n", "    \n", "    for lam in lambda_test:\n", "        ridge = Ridge(alpha=lam)\n", "        ridge.fit(X_scaled, y)\n", "        ridge_features.append(np.sum(np.abs(ridge.coef_) > 1e-6))\n", "        \n", "        lasso = Lasso(alpha=lam, max_iter=2000)\n", "        lasso.fit(X_scaled, y)\n", "        lasso_features.append(np.sum(np.abs(lasso.coef_) > 1e-6))\n", "        \n", "        elastic = ElasticNet(alpha=lam, l1_ratio=0.5, max_iter=2000)\n", "        elastic.fit(X_scaled, y)\n", "        elastic_features.append(np.sum(np.abs(elastic.coef_) > 1e-6))\n", "    \n", "    x_pos = np.arange(len(lambda_test))\n", "    width = 0.25\n", "    \n", "    ax3.bar(x_pos - width, ridge_features, width, label='Ridge', alpha=0.7, color='blue')\n", "    ax3.bar(x_pos, lasso_features, width, label='Lasso', alpha=0.7, color='red')\n", "    ax3.bar(x_pos + width, elastic_features, width, label='Elastic Net', alpha=0.7, color='green')\n", "    \n", "    ax3.set_xlabel('Lamb<PERSON> (α)')\n", "    ax3.set_ylabel('Number of Selected Features')\n", "    ax3.set_title('Feature Selection Capability')\n", "    ax3.set_xticks(x_pos)\n", "    ax3.set_xticklabels([f'{lam:.2f}' for lam in lambda_test])\n", "    ax3.legend()\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # 4. <PERSON><PERSON>-<PERSON><PERSON><PERSON>\n", "    ax4 = axes[1, 0]\n", "    lambdas_tradeoff = np.logspace(-2, 2, 30)\n", "    bias_squared = 0.1 * np.exp(-lambdas_tradeoff/5) + 0.05\n", "    variance = 0.3 * np.exp(-lambdas_tradeoff/10) + 0.1\n", "    total_error = bias_squared + variance\n", "    \n", "    ax4.plot(lambdas_tradeoff, bias_squared, 'b-', linewidth=2, label='Bias²')\n", "    ax4.plot(lambdas_tradeoff, variance, 'r-', linewidth=2, label='Variance')\n", "    ax4.plot(lambdas_tradeoff, total_error, 'g-', linewidth=3, label='Total Error')\n", "    ax4.axvline(x=lambdas_tradeoff[np.argmin(total_error)], color='black', \n", "               linestyle='--', alpha=0.7, label='Optimal λ')\n", "    \n", "    ax4.set_xlabel('Lamb<PERSON> (α)')\n", "    ax4.set_ylabel('Error')\n", "    ax4.set_title('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    ax4.set_xscale('log')\n", "    \n", "    # 5. When to Use Each Method\n", "    ax5 = axes[1, 1]\n", "    scenarios = [\n", "        'Many correlated\\nfeatures',\n", "        'High-dimensional\\ndata (p > n)',\n", "        'Feature selection\\nneeded',\n", "        'Interpretable\\nmodel needed',\n", "        'Stable predictions\\nneeded'\n", "    ]\n", "    \n", "    ridge_scores = [9, 8, 3, 4, 9]\n", "    lasso_scores = [4, 7, 9, 9, 6]\n", "    elastic_scores = [9, 8, 8, 7, 8]\n", "    \n", "    x_pos = np.arange(len(scenarios))\n", "    width = 0.25\n", "    \n", "    ax5.bar(x_pos - width, ridge_scores, width, label='Ridge', alpha=0.7, color='blue')\n", "    ax5.bar(x_pos, lasso_scores, width, label='Lasso', alpha=0.7, color='red')\n", "    ax5.bar(x_pos + width, elastic_scores, width, label='Elastic Net', alpha=0.7, color='green')\n", "    \n", "    ax5.set_xlabel('Scenarios')\n", "    ax5.set_ylabel('Suitability Score (1-10)')\n", "    ax5.set_title('When to Use Each Method')\n", "    ax5.set_xticks(x_pos)\n", "    ax5.set_xticklabels(scenarios, rotation=45, ha='right')\n", "    ax5.legend()\n", "    ax5.grid(True, alpha=0.3)\n", "    ax5.set_ylim(0, 10)\n", "    \n", "    # 6. Performance Comparison\n", "    ax6 = axes[1, 2]\n", "    models = {\n", "        'Linear\\nRegression': Ridge(alpha=0),\n", "        'Ridge': Ridge(alpha=1.0),\n", "        'Lasso': <PERSON><PERSON>(alpha=0.1, max_iter=2000),\n", "        'Elastic Net': ElasticNet(alpha=0.1, l1_ratio=0.5, max_iter=2000)\n", "    }\n", "    \n", "    cv_scores = []\n", "    model_names = []\n", "    \n", "    for name, model in models.items():\n", "        scores = cross_val_score(model, X_scaled, y, cv=5, scoring='neg_mean_squared_error')\n", "        cv_scores.append(-scores.mean())\n", "        model_names.append(name)\n", "    \n", "    colors = ['gray', 'blue', 'red', 'green']\n", "    bars = ax6.bar(model_names, cv_scores, color=colors, alpha=0.7)\n", "    \n", "    # Add value labels\n", "    for bar, score in zip(bars, cv_scores):\n", "        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,\n", "                 f'{score:.3f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    ax6.set_ylabel('Cross-Validation MSE')\n", "    ax6.set_title('Performance Comparison')\n", "    ax6.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"=\"*60)\n", "    print(\"SUMMARY: Regularization Methods Comparison\")\n", "    print(\"=\"*60)\n", "    print()\n", "    print(\"RIDGE REGRESSION (L2):\")\n", "    print(\"• Penalty: λ∑β²\")\n", "    print(\"• Effect: Shrinks coefficients proportionally\")\n", "    print(\"• Feature Selection: No (coefficients never reach zero)\")\n", "    print(\"• Best for: Multicollinearity, stable predictions\")\n", "    print(\"• Constraint: Circular (smooth)\")\n", "    print()\n", "    print(\"LASSO REGRESSION (L1):\")\n", "    print(\"• Penalty: λ∑|β|\")\n", "    print(\"• Effect: Can zero out coefficients\")\n", "    print(\"• Feature Selection: Yes (automatic feature selection)\")\n", "    print(\"• Best for: High-dimensional data, interpretable models\")\n", "    print(\"• Constraint: Diamond (sharp corners)\")\n", "    print()\n", "    print(\"ELASTIC NET (L1 + L2):\")\n", "    print(\"• Penalty: λ₁∑|β| + λ₂∑β²\")\n", "    print(\"• Effect: Combines Ridge and Lasso benefits\")\n", "    print(\"• Feature Selection: Yes (but more stable than <PERSON><PERSON>)\")\n", "    print(\"• Best for: Correlated features, balanced approach\")\n", "    print(\"• Constraint: Hybrid shape\")\n", "    print()\n", "    print(\"KEY TAKEAWAYS:\")\n", "    print(\"1. Regularization prevents overfitting by adding penalty terms\")\n", "    print(\"2. <PERSON> handles multicollinearity well but doesn't select features\")\n", "    print(\"3. <PERSON><PERSON> performs feature selection but struggles with correlated features\")\n", "    print(\"4. Elastic Net combines the best of both worlds\")\n", "    print(\"5. Optimal parameters found through cross-validation\")\n", "    print(\"6. The choice depends on your specific problem and data characteristics\")\n", "\n", "print(\"=\"*80)\n", "print(\"REGULARIZATION VISUALIZATION FUNCTIONS LOADED\")\n", "print(\"=\"*80)\n", "print(\"Available functions:\")\n", "print(\"• visualize_ridge_regression() - Ridge regression effects\")\n", "print(\"• visualize_lasso_regression() - Lasso feature selection\")\n", "print(\"• visualize_geometric_comparison() - Ridge vs Lasso geometry\")\n", "print(\"• visualize_elastic_net() - Elastic Net comprehensive analysis\")\n", "print(\"• visualize_summary() - Complete comparison summary\")\n", "print(\"=\"*80)\n"]}, {"cell_type": "markdown", "id": "e7594808", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "15f49025", "metadata": {}, "source": ["## <PERSON> (L2) Regression"]}, {"cell_type": "code", "execution_count": null, "id": "7919ad9f", "metadata": {}, "outputs": [], "source": ["# Run this to visualize Ridge regression effects\n", "visualize_ridge_regression()\n"]}, {"cell_type": "markdown", "id": "9f9279c9", "metadata": {}, "source": ["* Introduces small amount of bias to achieve significant reduction in variance\n", "\n", "* Formula: $\\text{Sum of squared residuals}+\\lambda\\cdot \\text{slope}^2$\n", "\n", "* The $\\text{slope}^2$ term adds a penalty that prevents coefficients from becoming too large\n", "\n", "* $\\lambda$ (lambda) controls the strength of regularization\n", "\n", "### Lambda Effects\n", "\n", "* When λ = 0: No regularization (equivalent to standard linear regression)\n", "\n", "* As λ increases: Model becomes simpler and less sensitive to weight, but less fitted to training data\n", "\n", "* Optimal λ is typically found using cross-validation\n", "\n", "### When to Use Ridge Regression\n", "\n", "* Dealing with multicollinearity (highly correlated features)\n", "\n", "* When you have more features than observations\n", "\n", "* To prevent overfitting in complex models\n"]}, {"cell_type": "markdown", "id": "bc5425dd", "metadata": {}, "source": ["## <PERSON>so Regression (L1)"]}, {"cell_type": "code", "execution_count": null, "id": "aba42c27", "metadata": {}, "outputs": [], "source": ["# Run this to visualize Lasso regression feature selection\n", "visualize_lasso_regression()\n"]}, {"cell_type": "markdown", "id": "81211431", "metadata": {}, "source": ["* Similar to Ridge Regression, adds a penalty term to the cost function\n", "\n", "* Formula: $\\text{Sum of squared residuals}+\\lambda\\cdot |\\text{slope}|$\n", "\n", "* Uses absolute value of coefficients (|slope|) instead of squared values\n", "\n", "* The key difference: <PERSON><PERSON> can reduce coefficients to exactly zero\n", "\n", "### Feature Selection\n", "\n", "* Lasso performs automatic feature selection by eliminating less important variables\n", "\n", "* Creates sparse models with fewer features\n", "\n", "* Useful when you suspect many features are irrelevant\n", "\n", "### Lambda Effects\n", "\n", "* When λ = 0: No regularization (standard linear regression)\n", "\n", "* As λ increases: More coefficients are pushed to zero, therefore removing useless variables from the equation\n", "\n", "* Optimal λ is typically found using cross-validation\n", "\n", "### When to Use Lasso Regression\n", "\n", "* When you want a simpler, more interpretable model\n", "\n", "* When feature selection is desired\n", "\n", "* When dealing with high-dimensional data with many irrelevant features\n"]}, {"cell_type": "markdown", "id": "e6e6074a", "metadata": {}, "source": ["## <PERSON> vs <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "42541f8b", "metadata": {}, "outputs": [], "source": ["# Run this to visualize geometric differences between Ridge and Lasso\n", "visualize_geometric_comparison()"]}, {"cell_type": "markdown", "id": "21986d53", "metadata": {}, "source": ["### Geometric Interpretation\n", "\n", "* Ridge and Lasso can be visualized using contour plots of error function and constraint regions\n", "\n", "* Ridge constraint region: Circle (in 2D) or hypersphere (higher dimensions)\n", "\n", "* Lasso constraint region: Diamond (in 2D) or hyperrhombus (higher dimensions)\n", "\n", "### Why <PERSON>so Can Zero Out Coefficients\n", "\n", "* The diamond shape of Lasso's constraint region has corners that touch axes\n", "\n", "* When the error function contour meets the constraint at a corner, one coefficient becomes exactly zero\n", "\n", "* Ridge's circular constraint has no corners, so coefficients approach but never reach zero\n", "\n", "### Key Differences\n", "\n", "* Ridge: Shrinks all coefficients proportionally, none become exactly zero\n", "\n", "* Lasso: Can completely eliminate variables by making their coefficients zero\n", "\n", "* Visual explanation: The diamond shape of <PERSON><PERSON>'s constraint is more likely to intersect axes\n"]}, {"cell_type": "markdown", "id": "d957b7a5", "metadata": {}, "source": ["## Elastic Net Regression"]}, {"cell_type": "code", "execution_count": null, "id": "f4586fe3", "metadata": {}, "outputs": [], "source": ["# Run this\n", "visualize_elastic_net()"]}, {"cell_type": "markdown", "id": "7f802b0a", "metadata": {}, "source": ["* Combines Ridge Regression and Lasso Regression\n", "\n", "* Formula: $\\text{Sum of squared residuals} + \\lambda_1 \\cdot |\\text{slope}| + \\lambda_2 \\cdot \\text{slope}^2$\n", "\n", "* Includes both L1 (absolute value) and L2 (squared) penalty terms\n", "\n", "* Can be rewritten with a mixing parameter α that controls the balance between Ridge and Lasso\n", "\n", "### Advantages\n", "\n", "* Performs feature selection like <PERSON><PERSON> (can zero out coefficients)\n", "\n", "* Handles correlated variables better than <PERSON><PERSON> alone\n", "\n", "* Overcomes limitations of both Ridge and Lasso\n", "\n", "* More flexible due to having two tuning parameters\n", "\n", "### When to Use Elastic Net\n", "\n", "* When you have many correlated features\n", "\n", "* When the number of features is much larger than the number of observations\n", "\n", "* When you want some feature selection but don't want to eliminate too many variables\n", "\n", "* When neither <PERSON> nor <PERSON><PERSON> alone gives satisfactory results\n", "\n", "### Parameter Selection\n", "\n", "* Two parameters to tune: λ (overall regularization strength) and α (mixing ratio)\n", "\n", "* When α = 0: Pure Ridge Regression\n", "\n", "* When α = 1: Pure Lasso Regression\n", "\n", "* Values between 0 and 1 balance between Ridge and Lasso properties\n", "\n", "* Cross-validation is typically used to find optimal values for both parameters\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}