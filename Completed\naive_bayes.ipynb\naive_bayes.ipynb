{"cells": [{"cell_type": "markdown", "id": "c9f56385", "metadata": {}, "source": ["# <a id='toc1_'></a>[Naive Bayes - Spam Classification](#toc0_)"]}, {"cell_type": "markdown", "id": "toc_cell", "metadata": {}, "source": ["**Table of contents**<a id='toc0_'></a>    \n", "- [Na<PERSON> - Spam Classification](#toc1_)    \n", "- [Theory](#toc2_)    \n", "- [Problem Statement](#toc3_)    \n", "- [Introduction](#toc4_)    \n", "- [Importing Libraries](#toc5_)    \n", "- [Loading and Preparing the Dataset](#toc6_)    \n", "  - [Loading the Dataset](#toc6_1_)    \n", "  - [Dataset Information](#toc6_2_)    \n", "  - [Exploratory Data Analysis (EDA)](#toc6_3_)    \n", "  - [Separating Features and Target](#toc6_4_)    \n", "- [Train/Test Split](#toc7_)    \n", "- [Text Preprocessing Pipeline](#toc8_)    \n", "  - [Text Vectorization](#toc8_1_)    \n", "  - [Feature Exploration](#toc8_2_)    \n", "- [Model Implementation](#toc9_)    \n", "  - [Model Initialization](#toc9_1_)    \n", "  - [Training](#toc9_2_)    \n", "  - [Making Predictions](#toc9_3_)    \n", "- [Model Evaluation](#toc10_)    \n", "  - [Evaluation Helper Function](#toc10_1_)    \n", "  - [Performance Metrics](#toc10_2_)    \n", "  - [Classification Report](#toc10_3_)    \n", "  - [Confusion Matrix Analysis](#toc10_4_)    \n", "  - [Baseline Model Comparison](#toc10_5_)    \n", "- [Complete Pipeline Implementation](#toc11_)    \n", "  - [Creating the Pipeline](#toc11_1_)    \n", "  - [Testing with New Examples](#toc11_2_)    \n", "- [Hyperparameter Tuning](#toc12_)    \n", "  - [Alpha Parameter Optimization](#toc12_1_)    \n", "  - [Retrain with Optimal Parameters](#toc12_2_)    \n", "- [Saving and Loading the Model](#toc13_)    \n", "  - [Saving the Model Package](#toc13_1_)    \n", "  - [Loading the Model Package](#toc13_2_)    \n", "  - [Using the Loaded Model](#toc13_3_)    \n", "- [Summary](#toc14_)    \n", "  - [Assumptions of Naive <PERSON>](#toc14_1_)    \n", "  - [Limitations](#toc14_2_)    \n", "  - [Best Use Cases](#toc14_3_)    \n", "\n", "<!-- vscode-jupyter-toc-config\n", "\tnumbering=false\n", "\tanchor=true\n", "\tflat=false\n", "\tminLevel=1\n", "\tmaxLevel=6\n", "\t/vscode-jupyter-toc-config -->\n", "<!-- THIS CELL WILL BE REPLACED ON TOC UPDATE. DO NOT WRITE YOUR TEXT IN THIS CELL -->"]}, {"cell_type": "markdown", "id": "separator1", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "theory_section", "metadata": {}, "source": ["# <a id='toc2_'></a>[Theory](#toc0_)"]}, {"cell_type": "markdown", "id": "theory_video", "metadata": {}, "source": ["[Theory video - Naive <PERSON> Clearly Explained](https://www.youtube.com/watch?v=O2L2Uv9pdDA&list=PLblh5JKOoLUICTaGLRoHQDuF_7q2GfuJF&index=44)\n"]}, {"cell_type": "markdown", "id": "separator2", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "problem_section", "metadata": {}, "source": ["# <a id='toc3_'></a>[Problem Statement](#toc0_)\n", "\n", "A telecommunications company is experiencing a surge in customer complaints about spam messages, leading to decreased user satisfaction and potential customer churn. The manual filtering process is overwhelmed, with moderators only able to review a fraction of reported messages.\n", "\n", "**Your task**: Develop an automated spam detection system using machine learning that can:\n", "- Accurately classify SMS messages as spam or legitimate (ham)\n", "- Process thousands of messages in real-time\n", "- Minimize false positives (legitimate messages marked as spam)\n", "- Adapt to evolving spam patterns\n", "\n", "**Success criteria**: \n", "- Achieve at least 95% accuracy on test data\n", "- Maintain precision above 90% to avoid blocking legitimate messages\n", "- Provide probability scores for human review of borderline cases\n", "\n", "**Business impact**: \n", "This automated system will improve customer experience, reduce operational costs, and protect users from fraudulent messages while ensuring important communications are delivered."]}, {"cell_type": "markdown", "id": "separator3", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "8f7ac21a", "metadata": {}, "source": ["# <a id='toc4_'></a>[Introduction](#toc0_)\n", "\n", "## <PERSON><PERSON>\n", "\n", "Naive <PERSON> is a **probabilistic machine learning algorithm** based on **<PERSON><PERSON>' Theorem**, particularly effective for text classification tasks. Despite its \"naive\" assumption of feature independence, it often performs remarkably well in practice.\n", "\n", "### Mathematical Foundation\n", "\n", "#### <PERSON><PERSON>' Theorem\n", "\n", "The fundamental equation:\n", "\n", "$$P(y|X) = \\frac{P(X|y) \\cdot P(y)}{P(X)}$$\n", "\n", "Where:\n", "- $P(y|X)$ is the **posterior probability** - probability of class $y$ given features $X$\n", "- $P(X|y)$ is the **likelihood** - probability of observing features $X$ given class $y$\n", "- $P(y)$ is the **prior probability** - initial probability of class $y$\n", "- $P(X)$ is the **evidence** - probability of observing features $X$\n", "\n", "#### Naive <PERSON>es Classification\n", "\n", "For classification, we apply the \"naive\" assumption that features are conditionally independent:\n", "\n", "$$\\hat{y} = \\arg\\max_y P(y) \\prod_{i=1}^{n} P(x_i|y)$$\n", "\n", "This simplification allows us to:\n", "1. Estimate probabilities from training data easily\n", "2. Handle high-dimensional data efficiently\n", "3. Train models with relatively small datasets\n", "\n", "### Variants of <PERSON><PERSON>es\n", "\n", "| Variant | Distribution | Use Case | Formula |\n", "|---------|-------------|----------|----------|\n", "| **Gaussian** | Normal | Continuous features | $P(x_i\\|y) = \\frac{1}{\\sqrt{2\\pi\\sigma_y^2}} e^{-\\frac{(x_i-\\mu_y)^2}{2\\sigma_y^2}}$ |\n", "| **Multinomial** | Multinomial | Count data, text | $P(x\\|y) = \\frac{(\\sum_i x_i)!}{\\prod_i x_i!} \\prod_i p_{yi}^{x_i}$ |\n", "| **<PERSON><PERSON><PERSON>** | <PERSON><PERSON><PERSON> | Binary features | $P(x\\|y) = \\prod_i p_{yi}^{x_i}(1-p_{yi})^{1-x_i}$ |\n", "\n", "For spam classification with word counts, **Multinomial Naive Bayes** is most appropriate.\n", "\n", "### <PERSON><PERSON>moothing\n", "\n", "To handle words not seen in training, we apply Laplace smoothing (additive smoothing):\n", "\n", "$$P(x_i|y) = \\frac{count(x_i, y) + \\alpha}{count(y) + \\alpha \\cdot |V|}$$\n", "\n", "Where $\\alpha$ is the smoothing parameter and $|V|$ is the vocabulary size."]}, {"cell_type": "markdown", "id": "separator4", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "imports_section", "metadata": {}, "source": ["# <a id='toc5_'></a>[Importing Libraries](#toc0_)"]}, {"cell_type": "code", "execution_count": 1, "id": "23680518", "metadata": {}, "outputs": [], "source": ["# Import core data analysis and visualization libraries\n", "# - pandas for data manipulation and analysis\n", "# - numpy for numerical operations and array handling\n", "# - matplotlib.pyplot for creating plots and visualizations\n", "# - seaborn for enhanced statistical visualizations\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Import utility for saving/loading Python objects\n", "# - joblib for efficient serialization of scikit-learn objects\n", "import joblib\n", "\n", "# Import scikit-learn modules for machine learning:\n", "# Data splitting and preprocessing\n", "from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score\n", "from sklearn.feature_extraction.text import CountVectorizer, TfidfVectorizer\n", "from sklearn.pipeline import Pipeline\n", "\n", "# Models\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.dummy import DummyClassifier\n", "\n", "\n", "# Evaluation metrics\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report, roc_auc_score\n", "\n", "\n", "# Configure visualization settings\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', 1000)\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "separator5", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "data_section", "metadata": {}, "source": ["# <a id='toc6_'></a>[Loading and Preparing the Dataset](#toc0_)"]}, {"cell_type": "markdown", "id": "loading_subsection", "metadata": {}, "source": ["## <a id='toc6_1_'></a>[Loading the Dataset](#toc0_)"]}, {"cell_type": "code", "execution_count": 2, "id": "de9bb445", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (5572, 2)\n", "Columns: ['label', 'text']\n", "\n", "First 5 rows:\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "label", "rawType": "object", "type": "string"}, {"name": "text", "rawType": "object", "type": "string"}], "ref": "0a501722-b5ad-4468-af6b-cc910f9b8178", "rows": [["0", "ham", "Go until jurong point, crazy.. Available only in bugis n great world la e buffet... Cine there got amore wat..."], ["1", "ham", "Ok lar... Joking wif u oni..."], ["2", "spam", "Free entry in 2 a wkly comp to win FA Cup final tkts 21st May 2005. Text FA to 87121 to receive entry question(std txt rate)T&C's apply 08452810075over18's"], ["3", "ham", "U dun say so early hor... U c already then say..."], ["4", "ham", "Nah I don't think he goes to usf, he lives around here though"]], "shape": {"columns": 2, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>label</th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ham</td>\n", "      <td>Go until jurong point, crazy.. Available only ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ham</td>\n", "      <td>Ok lar... Joking wif u oni...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>spam</td>\n", "      <td>Free entry in 2 a wkly comp to win FA Cup fina...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ham</td>\n", "      <td>U dun say so early hor... U c already then say...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ham</td>\n", "      <td>Nah I don't think he goes to usf, he lives aro...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  label                                               text\n", "0   ham  Go until jurong point, crazy.. Available only ...\n", "1   ham                      Ok lar... Joking wif u oni...\n", "2  spam  Free entry in 2 a wkly comp to win FA Cup fina...\n", "3   ham  U dun say so early hor... U c already then say...\n", "4   ham  Nah I don't think he goes to usf, he lives aro..."]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Define the URL of the spam dataset on GitHub and assign to 'url'\n", "# - URL points to a CSV file containing SMS messages labeled as spam/ham\n", "# - Dataset source: SMS Spam Collection from UCI Machine Learning Repository\n", "url = 'https://raw.githubusercontent.com/henrylahteenmaki/Machine_Learning_Methods/refs/heads/main/datasets/spam_data_for_naive_bayes.csv'\n", "\n", "# Load the dataset from the URL into a pandas DataFrame and assign to 'df'\n", "# - Function: pd.read_csv() reads CSV data from URL or local file\n", "# - Parameters: url specifies the data source location\n", "# - Returns: Pandas DataFrame containing the SMS spam dataset\n", "df = pd.read_csv(url)\n", "\n", "# Display basic information about the dataset\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "print(f\"\\nFirst 5 rows:\")\n", "df.head()\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "last_rows", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "label", "rawType": "object", "type": "string"}, {"name": "text", "rawType": "object", "type": "string"}], "ref": "f8035fe6-9216-417a-a73e-7cc6c7bf0c7c", "rows": [["5567", "spam", "This is the 2nd time we have tried 2 contact u. U have won the å£750 Pound prize. 2 claim is easy, call 087187272008 NOW1! Only 10p per minute. BT-national-rate."], ["5568", "ham", "Will Ì_ b going to esplanade fr home?"], ["5569", "ham", "Pity, * was in mood for that. So...any other suggestions?"], ["5570", "ham", "The guy did some bitching but I acted like i'd be interested in buying something else next week and he gave it to us for free"], ["5571", "ham", "Rofl. Its true to its name"]], "shape": {"columns": 2, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>label</th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5567</th>\n", "      <td>spam</td>\n", "      <td>This is the 2nd time we have tried 2 contact u...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5568</th>\n", "      <td>ham</td>\n", "      <td>Will Ì_ b going to esplanade fr home?</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5569</th>\n", "      <td>ham</td>\n", "      <td><PERSON><PERSON>, * was in mood for that. So...any other s...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5570</th>\n", "      <td>ham</td>\n", "      <td>The guy did some bitching but I acted like i'd...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5571</th>\n", "      <td>ham</td>\n", "      <td>Rofl. Its true to its name</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     label                                               text\n", "5567  spam  This is the 2nd time we have tried 2 contact u...\n", "5568   ham              Will Ì_ b going to esplanade fr home?\n", "5569   ham  Pity, * was in mood for that. So...any other s...\n", "5570   ham  The guy did some bitching but I acted like i'd...\n", "5571   ham                         Rofl. Its true to its name"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display the last rows to verify data consistency\n", "df.tail()\n"]}, {"cell_type": "markdown", "id": "data_info", "metadata": {}, "source": ["## <a id='toc6_2_'></a>[Dataset Information](#toc0_)\n", "\n", "### SMS Spam Collection Dataset\n", "\n", "This dataset is a collection of SMS messages that have been manually classified for spam detection research.\n", "\n", "**Dataset Characteristics:**\n", "- **Source**: SMS messages collected from various sources\n", "- **Language**: English\n", "- **Time Period**: Messages collected over several years\n", "- **Labeling**: Human-annotated by multiple reviewers\n", "\n", "| Column | Description | Type | Values |\n", "|--------|-------------|------|---------|\n", "| label | Message classification | Categorical | 'ham' (legitimate), 'spam' |\n", "| text | SMS message content | Text | Variable length strings |\n", "\n", "**Spam Characteristics:**\n", "- Promotional offers (\"FREE\", \"WINNER\")\n", "- Urgency indicators (\"URGENT\", \"NOW\")\n", "- Call-to-action (\"CALL\", \"CLICK\", \"REPLY\")\n", "- Monetary references (\"£\", \"$\", \"prize\")"]}, {"cell_type": "code", "execution_count": 4, "id": "4f0fe8ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 5572 entries, 0 to 5571\n", "Data columns (total 2 columns):\n", " #   Column  Non-Null Count  Dtype \n", "---  ------  --------------  ----- \n", " 0   label   5572 non-null   object\n", " 1   text    5572 non-null   object\n", "dtypes: object(2)\n", "memory usage: 87.2+ KB\n"]}], "source": ["# Get comprehensive information about the dataset structure\n", "# - Method: info() provides data types, non-null counts, and memory usage\n", "# - Shows: number of entries, column names, data types, memory usage\n", "# - Helps identify missing values and appropriate data types\n", "df.info()\n"]}, {"cell_type": "code", "execution_count": 5, "id": "23b5b2ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["label    0\n", "text     0\n", "dtype: int64\n"]}], "source": ["# Check for missing values in each column\n", "# - Method: isnull().sum() counts null values per column\n", "# - Important for determining if imputation is needed\n", "missing_values = df.isnull().sum()\n", "print(missing_values)"]}, {"cell_type": "code", "execution_count": 6, "id": "11fea0d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  ham  : 4825 samples (86.6%)\n", "  spam :  747 samples (13.4%)\n"]}], "source": ["# Display class distribution\n", "# - Method: value_counts() shows frequency of each label\n", "# - Parameter: normalize=True shows proportions\n", "# - Helps identify class imbalance\n", "class_counts = df['label'].value_counts()\n", "class_props = df['label'].value_counts(normalize=True)\n", "\n", "\n", "for label in class_counts.index:\n", "    count = class_counts[label]\n", "    prop = class_props[label]\n", "    print(f\"  {label:4} : {count:4} samples ({prop*100:.1f}%)\")\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "id": "2f0d0ee2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Class imbalance ratio: 6.46:1\n", "⚠ Warning: Significant class imbalance detected\n"]}], "source": ["\n", "# Check for class imbalance\n", "imbalance_ratio = class_counts.max() / class_counts.min()\n", "print(f\"\\nClass imbalance ratio: {imbalance_ratio:.2f}:1\")\n", "if imbalance_ratio > 3:\n", "    print(\"⚠ Warning: Significant class imbalance detected\")\n", "else:\n", "    print(\"✓ Class distribution is reasonably balanced\")\n", "\n"]}, {"cell_type": "markdown", "id": "separator6", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "eda_section", "metadata": {}, "source": ["## <a id='toc6_3_'></a>[Exploratory Data Analysis (EDA)](#toc0_)\n", "\n", "We analyze text characteristics to understand patterns that distinguish spam from legitimate messages."]}, {"cell_type": "code", "execution_count": 8, "id": "eda_analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Message characteristics by label:\n", "================================================================================\n", "      message_length                word_count                capital_ratio               digit_ratio               special_char_ratio              \n", "                mean median     std       mean median     std          mean median    std        mean median    std               mean median    std\n", "label                                                                                                                                               \n", "ham           71.024   52.0  58.016     14.201   11.0  11.425         0.058  0.034  0.111       0.004  0.000  0.020              0.008  0.000  0.015\n", "spam         138.866  149.0  29.183     23.851   25.0   5.812         0.111  0.098  0.085       0.116  0.116  0.069              0.010  0.007  0.009\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1800x1000 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "KEY OBSERVATIONS:\n", "================================================================================\n", "1. Spam messages tend to be longer than ham messages\n", "2. Spam messages use more capital letters (urgency/emphasis)\n", "3. Spam messages contain more digits (phone numbers, prices)\n", "4. Spam messages use more special characters ($, £, !)\n", "5. Dataset shows class imbalance with more ham than spam\n"]}], "source": ["\n", "# Add text feature engineering for analysis\n", "# - Create new columns with message characteristics\n", "# - Helps identify patterns between spam and ham messages\n", "df['message_length'] = df['text'].str.len()\n", "df['word_count'] = df['text'].str.split().str.len()\n", "df['capital_ratio'] = df['text'].str.count('[A-Z]') / df['message_length']\n", "df['digit_ratio'] = df['text'].str.count('[0-9]') / df['message_length']\n", "df['special_char_ratio'] = df['text'].str.count('[!?$£]') / df['message_length']\n", "\n", "# Statistical summary by label\n", "print(\"Message characteristics by label:\")\n", "print(\"=\" * 80)\n", "feature_cols = ['message_length', 'word_count', 'capital_ratio', 'digit_ratio', 'special_char_ratio']\n", "summary_stats = df.groupby('label')[feature_cols].agg(['mean', 'median', 'std'])\n", "print(summary_stats.round(3))\n", "\n", "# Create comprehensive visualizations\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 10))\n", "\n", "# Plot 1: Message length distribution\n", "sns.boxplot(data=df, x='label', y='message_length', ax=axes[0,0])\n", "axes[0,0].set_title('Message Length Distribution', fontsize=14, fontweight='bold')\n", "axes[0,0].set_xlabel('Label', fontsize=12)\n", "axes[0,0].set_ylabel('Character Count', fontsize=12)\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Word count distribution\n", "sns.boxplot(data=df, x='label', y='word_count', ax=axes[0,1])\n", "axes[0,1].set_title('Word Count Distribution', fontsize=14, fontweight='bold')\n", "axes[0,1].set_xlabel('Label', fontsize=12)\n", "axes[0,1].set_ylabel('Word Count', fontsize=12)\n", "axes[0,1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Capital letter ratio\n", "sns.violinplot(data=df, x='label', y='capital_ratio', ax=axes[0,2])\n", "axes[0,2].set_title('Capital Letter Usage', fontsize=14, fontweight='bold')\n", "axes[0,2].set_xlabel('Label', fontsize=12)\n", "axes[0,2].set_ylabel('Capital Letter Ratio', fontsize=12)\n", "axes[0,2].grid(True, alpha=0.3)\n", "\n", "# Plot 4: Digit ratio\n", "sns.violinplot(data=df, x='label', y='digit_ratio', ax=axes[1,0])\n", "axes[1,0].set_title('Digit Usage', fontsize=14, fontweight='bold')\n", "axes[1,0].set_xlabel('Label', fontsize=12)\n", "axes[1,0].set_ylabel('Digit Ratio', fontsize=12)\n", "axes[1,0].grid(True, alpha=0.3)\n", "\n", "# Plot 5: Special character ratio\n", "sns.violinplot(data=df, x='label', y='special_char_ratio', ax=axes[1,1])\n", "axes[1,1].set_title('Special Character Usage', fontsize=14, fontweight='bold')\n", "axes[1,1].set_xlabel('Label', fontsize=12)\n", "axes[1,1].set_ylabel('Special Character Ratio', fontsize=12)\n", "axes[1,1].grid(True, alpha=0.3)\n", "\n", "# Plot 6: Class distribution pie chart\n", "class_counts.plot(kind='pie', ax=axes[1,2], autopct='%1.1f%%', \n", "                  colors=['lightgreen', 'lightcoral'])\n", "axes[1,2].set_title('Class Distribution', fontsize=14, fontweight='bold')\n", "axes[1,2].set_ylabel('')\n", "\n", "plt.suptitle('SMS Spam Dataset - Exploratory Data Analysis', fontsize=16, fontweight='bold', y=1.02)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Key observations\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"KEY OBSERVATIONS:\")\n", "print(\"=\" * 80)\n", "print(\"1. Spam messages tend to be longer than ham messages\")\n", "print(\"2. Spam messages use more capital letters (urgency/emphasis)\")\n", "print(\"3. Spam messages contain more digits (phone numbers, prices)\")\n", "print(\"4. Spam messages use more special characters ($, £, !)\")\n", "print(\"5. Dataset shows class imbalance with more ham than spam\")\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "word_analysis", "metadata": {}, "source": ["### Most Common Words Analysis"]}, {"cell_type": "code", "execution_count": 9, "id": "word_frequency", "metadata": {}, "outputs": [{"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# Analyze most common words in spam vs ham\n", "from collections import Counter\n", "import re\n", "\n", "def get_top_words(texts, n=15):\n", "    \"\"\"Extract top n most common words from texts\"\"\"\n", "    all_words = []\n", "    for text in texts:\n", "        # Convert to lowercase and extract words\n", "        words = re.findall(r'\\b[a-z]+\\b', text.lower())\n", "        all_words.extend(words)\n", "    return Counter(all_words).most_common(n)\n", "\n", "# Get top words for each class\n", "spam_texts = df[df['label'] == 'spam']['text']\n", "ham_texts = df[df['label'] == 'ham']['text']\n", "\n", "top_spam_words = get_top_words(spam_texts)\n", "top_ham_words = get_top_words(ham_texts)\n", "\n", "# Display results\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Spam words\n", "words, counts = zip(*top_spam_words)\n", "axes[0].barh(words, counts, color='lightcoral')\n", "axes[0].set_title('Top 15 Words in Spam Messages', fontsize=14, fontweight='bold')\n", "axes[0].set_xlabel('Frequency', fontsize=12)\n", "axes[0].invert_yaxis()\n", "\n", "# Ham words\n", "words, counts = zip(*top_ham_words)\n", "axes[1].barh(words, counts, color='lightgreen')\n", "axes[1].set_title('Top 15 Words in Ham Messages', fontsize=14, fontweight='bold')\n", "axes[1].set_xlabel('Frequency', fontsize=12)\n", "axes[1].invert_yaxis()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "feature_target_sep", "metadata": {}, "source": ["## <a id='toc6_4_'></a>[Separating Features and Target](#toc0_)\n", "\n", "We separate the text messages (features) from their labels (target) for supervised learning."]}, {"cell_type": "code", "execution_count": 10, "id": "87519373", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features and Target Information:\n", "==================================================\n", "Features (X) shape: (5572,)\n", "Target (y) shape: (5572,)\n", "Feature data type: object\n", "Target data type: object\n", "Unique target values: ['ham' 'spam']\n", "\n", "Sample messages:\n", "==================================================\n", "Label: ham  | Message: Go until jurong point, crazy.. Available only in bugis n gre...\n", "Label: ham  | Message: Ok lar... Joking wif u oni......\n", "Label: spam | Message: Free entry in 2 a wkly comp to win FA Cup final tkts 21st Ma...\n"]}], "source": ["# Separate features (X) and target variable (y) for supervised learning\n", "# - X contains the text messages (independent variable)\n", "# - y contains the labels (dependent variable: spam/ham)\n", "\n", "\n", "# Extract the text column as features\n", "# - Series containing all SMS message texts\n", "X = df['text']\n", "\n", "# Extract the label column as target\n", "# - Series containing corresponding labels (spam/ham)\n", "y = df['label']\n", "\n", "\n", "# Display the structure of features and target\n", "print(\"Features and Target Information:\")\n", "print(\"=\" * 50)\n", "print(f\"Features (X) shape: {X.shape}\")\n", "print(f\"Target (y) shape: {y.shape}\")\n", "print(f\"Feature data type: {X.dtype}\")\n", "print(f\"Target data type: {y.dtype}\")\n", "print(f\"Unique target values: {y.unique()}\")\n", "\n", "print(\"\\nSample messages:\")\n", "print(\"=\" * 50)\n", "for i in range(3):\n", "    print(f\"Label: {y.iloc[i]:4} | Message: {X.iloc[i][:60]}...\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "id": "95143111", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of Unique target values: 2\n"]}], "source": ["print(f\"Number of Unique target values: {y.nunique()}\")"]}, {"cell_type": "markdown", "id": "separator7", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "train_test_split_section", "metadata": {}, "source": ["# <a id='toc7_'></a>[Train/Test Split](#toc0_)\n", "\n", "We split the dataset into training and test sets. The training set will be used for model fitting and hyperparameter tuning (via cross-validation), while the test set provides an unbiased final evaluation."]}, {"cell_type": "code", "execution_count": 12, "id": "split_data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Split Summary:\n", "==================================================\n", "Total samples: 5572\n", "Training set: 4457 samples (80.0%)\n", "Test set:     1115 samples (20.0%)\n", "\n", "Class Distribution per Set:\n", "==================================================\n", "\n", "Training:\n", "  ham : 3859 samples (86.6%)\n", "  spam:  598 samples (13.4%)\n", "\n", "Test:\n", "  ham :  966 samples (86.6%)\n", "  spam:  149 samples (13.4%)\n", "\n", "Stratification Check:\n", "Spam ratio in training: 0.134\n", "Spam ratio in test:     0.134\n", "Difference: 0.001\n", "✓ Stratification successful - class distributions maintained\n"]}], "source": ["# Split the dataset into training and testing sets\n", "# - Function: train_test_split() randomly divides data into train/test portions\n", "# - Parameters:\n", "#   - X, y: Feature and target arrays to split\n", "#   - test_size=0.2: Reserve 20% of data for testing, 80% for training\n", "#   - random_state=42: Ensures reproducible results across runs\n", "#   - stratify=y: Maintains class distribution in both sets (important for imbalanced data)\n", "# - Returns: X_train, X_test, y_train, y_test arrays\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "\n", "# Display split information\n", "print(\"Dataset Split Summary:\")\n", "print(\"=\" * 50)\n", "print(f\"Total samples: {len(X)}\")\n", "print(f\"Training set: {len(X_train)} samples ({len(X_train)/len(X)*100:.1f}%)\")\n", "print(f\"Test set:     {len(X_test)} samples ({len(X_test)/len(X)*100:.1f}%)\")\n", "\n", "print(\"\\nClass Distribution per Set:\")\n", "print(\"=\" * 50)\n", "\n", "for set_name, y_set in [('Training', y_train), ('Test', y_test)]:\n", "    print(f\"\\n{set_name}:\")\n", "    value_counts = y_set.value_counts()\n", "    value_props = y_set.value_counts(normalize=True)\n", "    for label in value_counts.index:\n", "        count = value_counts[label]\n", "        prop = value_props[label]\n", "        print(f\"  {label:4}: {count:4} samples ({prop*100:.1f}%)\")\n", "\n", "# Verify stratification worked\n", "train_spam_ratio = (y_train == 'spam').mean()\n", "test_spam_ratio = (y_test == 'spam').mean()\n", "print(f\"\\nStratification Check:\")\n", "print(f\"Spam ratio in training: {train_spam_ratio:.3f}\")\n", "print(f\"Spam ratio in test:     {test_spam_ratio:.3f}\")\n", "print(f\"Difference: {abs(train_spam_ratio - test_spam_ratio):.3f}\")\n", "if abs(train_spam_ratio - test_spam_ratio) < 0.01:\n", "    print(\"✓ Stratification successful - class distributions maintained\")\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "separator8", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "preprocessing_section", "metadata": {}, "source": ["# <a id='toc8_'></a>[Text Preprocessing Pipeline](#toc0_)\n", "\n", "We convert text data into numerical features using Count Vectorization (Bag of Words approach)."]}, {"cell_type": "markdown", "id": "vectorization_section", "metadata": {}, "source": ["## <a id='toc8_1_'></a>[Text Vectorization](#toc0_)\n", "\n", "### Count Vectorization (Bag of Words)\n", "\n", "Count Vectorization transforms text into a matrix where:\n", "- Each row represents a document (message)\n", "- Each column represents a unique word in the vocabulary\n", "- Each cell contains the count of word occurrences\n", "\n", "Example:\n", "- Message 1: \"free money now\"\n", "- Message 2: \"call now\"\n", "\n", "Becomes:\n", "```\n", "        free  money  now  call\n", "Msg 1:    1     1     1    0\n", "Msg 2:    0     0     1    1\n", "```"]}, {"cell_type": "code", "execution_count": 13, "id": "9871cb1c", "metadata": {}, "outputs": [], "source": ["# Create a CountVectorizer for text-to-numeric conversion\n", "# - Class: CountVectorizer converts text documents to token count matrix\n", "# - Parameters:\n", "#   - stop_words='english': Remove common English words (the, is, at, etc.)\n", "#   - lowercase=True: Convert all text to lowercase before processing\n", "#   - max_features=5000: Keep only top 5000 most frequent words\n", "#   - min_df=2: Ignore words appearing in fewer than 2 documents\n", "#   - max_df=0.95: Ignore words appearing in more than 95% of documents\n", "#   - token_pattern=r'\\b\\w+\\b': Extract whole words only\n", "# - Returns: Vectorizer object for transforming text\n", "vectorizer = CountVectorizer(\n", "    stop_words='english',\n", "    lowercase=True,\n", "    max_features=5000,\n", "    min_df=2,\n", "    max_df=0.95,\n", "    token_pattern=r'\\b\\w+\\b'\n", ")\n", "\n", "# Fit and transform the training data\n", "# - Method: fit_transform() learns vocabulary and transforms text\n", "# - Process: \n", "#   1. Builds vocabulary from training data\n", "#   2. Creates document-term matrix\n", "# - Returns: Sparse matrix of token counts\n", "X_train_vectorized = vectorizer.fit_transform(X_train)\n", "\n", "# Transform the test data using fitted vectorizer\n", "# - Method: transform() applies learned vocabulary to test data\n", "# - Important: Uses same vocabulary as training (no new words added)\n", "# - Prevents data leakage from test set\n", "X_test_vectorized = vectorizer.transform(X_test)\n"]}, {"cell_type": "markdown", "id": "feature_exploration_section", "metadata": {}, "source": ["## <a id='toc8_2_'></a>[Feature Exploration](#toc0_)\n", "\n", "Let's explore the vocabulary created by the vectorizer."]}, {"cell_type": "code", "execution_count": 14, "id": "vocabulary_exploration", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['0' '00' '000' '01223585334' '02' '0207' '02073162414' '03' '04' '05'\n", " '050703' '0578' '06' '07' '07123456789' '07742676969' '07781482378'\n", " '07821230901' '07xxxxxxxxx' '0800']\n", "['ym' 'yo' 'yoga' 'yogasana' 'yor' 'youre' 'yr' 'yrs' 'yummy' 'yun' 'yuo'\n", " 'yup' 'zed' 'å' 'ì' 'ì_' 'ìï' 'û' 'û_' 'ûò']\n"]}], "source": ["# Get feature names (vocabulary)\n", "feature_names = vectorizer.get_feature_names_out()\n", "\n", "# Display sample vocabulary\n", "print(feature_names[:20])\n", "print(feature_names[-20:])"]}, {"cell_type": "code", "execution_count": 15, "id": "d102a05b", "metadata": {}, "outputs": [{"data": {"text/plain": ["['5free',\n", " 'free',\n", " 'free2day',\n", " 'freedom',\n", " 'freefone',\n", " 'freemsg',\n", " 'freephone',\n", " 'freezing']"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find most informative words for spam detection\n", "# This will be more meaningful after training the model\n", "\n", "free_words = [word for word in feature_names if 'free' in word]\n", "free_words"]}, {"cell_type": "code", "execution_count": 16, "id": "68296a8f", "metadata": {}, "outputs": [{"data": {"text/plain": ["['1winaweek',\n", " 'com1win150ppmx3age16',\n", " 'flowing',\n", " 'following',\n", " 'knowing',\n", " 'showing',\n", " 'swing',\n", " 'tscs087147403231winawk',\n", " 'txttowin',\n", " 'u<PERSON><PERSON><PERSON>',\n", " 'win',\n", " 'wind',\n", " 'wine',\n", " 'winner',\n", " 'winning',\n", " 'wins']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["win_words = [word for word in feature_names if 'win' in word]\n", "win_words"]}, {"cell_type": "markdown", "id": "separator9", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "f9651bb5", "metadata": {}, "source": ["# <a id='toc9_'></a>[Model Implementation](#toc0_)\n", "\n", "We implement Multinomial Naive Bayes classifier optimized for text classification."]}, {"cell_type": "markdown", "id": "model_init_section", "metadata": {}, "source": ["## <a id='toc9_1_'></a>[Model Initialization](#toc0_)"]}, {"cell_type": "code", "execution_count": 17, "id": "0a2a0e24", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>MultinomialNB()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator  sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label  sk-toggleable__label-arrow \">&nbsp;&nbsp;MultinomialNB<a class=\"sk-estimator-doc-link \" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.naive_bayes.MultinomialNB.html\">?<span>Documentation for MultinomialNB</span></a><span class=\"sk-estimator-doc-link \">i<span>Not fitted</span></span></label><div class=\"sk-toggleable__content \"><pre>MultinomialNB()</pre></div> </div></div></div></div>"], "text/plain": ["MultinomialNB()"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initialize the Multinomial Naive Bayes classifier and assign to 'nb_model'\n", "# - Class: MultinomialNB implements Naive Bayes for discrete features\n", "# - Parameters:\n", "#   - alpha=1.0: Laplace smoothing parameter (additive smoothing)\n", "#     - Prevents zero probabilities for unseen words\n", "#     - alpha=1.0 is Laplace smoothing, alpha<1 is Lidstone smoothing\n", "#   - fit_prior=True: Learn class prior probabilities from data\n", "#   - class_prior=None: Use empirical priors from training data\n", "# - Returns: Unfitted MultinomialNB instance\n", "nb_model = MultinomialNB(\n", "    alpha=1.0,\n", "    fit_prior=True,\n", "    class_prior=None\n", ")\n", "\n", "# Display the initialized model with its parameters\n", "nb_model\n"]}, {"cell_type": "markdown", "id": "training_section", "metadata": {}, "source": ["## <a id='toc9_2_'></a>[Training](#toc0_)\n", "\n", "We train the Na<PERSON> model by calculating class probabilities and feature likelihoods."]}, {"cell_type": "code", "execution_count": 26, "id": "model_training", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'alpha': 1.0, 'class_prior': None, 'fit_prior': True, 'force_alpha': True}"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([3859.,  598.])"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["['ham' 'spam']\n"]}, {"data": {"text/plain": ["array([[ 0.,  0.,  0., ..., 28., 11.,  9.],\n", "       [ 4., 10., 21., ...,  0.,  0.,  0.]])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Train the Na<PERSON> model on vectorized training data\n", "# - Method: fit() learns class probabilities and feature likelihoods\n", "# - Parameters:\n", "#   - X_train_vectorized: Vectorized training features (document-term matrix)\n", "#   - y_train: Training labels (spam/ham)\n", "# - Process:\n", "#   1. Calculates prior probabilities P(class)\n", "#   2. Calculates likelihood P(word|class) for each word and class\n", "#   3. Applies Laplace smoothing to handle zero counts\n", "# - Returns: Fitted model (modifies nb_model in-place)\n", "nb_model.fit(X_train_vectorized, y_train)\n", "\n", "# Display learned parameters\n", "display(nb_model.get_params())\n", "\n", "# Display learned class priors\n", "display(nb_model.class_count_)\n", "\n", "# Display feature statistics\n", "print(nb_model.classes_)\n", "\n", "# Display sample counts per class\n", "display(nb_model.feature_count_)\n"]}, {"cell_type": "markdown", "id": "prediction_section", "metadata": {}, "source": ["## <a id='toc9_3_'></a>[Making Predictions](#toc0_)\n", "\n", "We use the trained model to classify test messages and obtain probability estimates."]}, {"cell_type": "code", "execution_count": 29, "id": "model_prediction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ham' 'ham' 'ham' ... 'ham' 'ham' 'ham']\n", "[[1.00000000e+00 4.35050685e-15]\n", " [9.97853366e-01 2.14663388e-03]\n", " [9.99522031e-01 4.77969073e-04]\n", " ...\n", " [9.99995946e-01 4.05382467e-06]\n", " [9.94713241e-01 5.28675909e-03]\n", " [9.99348975e-01 6.51025222e-04]]\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "message", "rawType": "object", "type": "string"}, {"name": "true_label", "rawType": "object", "type": "string"}, {"name": "predicted_label", "rawType": "object", "type": "string"}, {"name": "spam_probability", "rawType": "float64", "type": "float"}, {"name": "ham_probability", "rawType": "float64", "type": "float"}, {"name": "correct", "rawType": "bool", "type": "boolean"}], "ref": "395f552b-71b5-4d87-b7dc-9424c1b7d01f", "rows": [["2826", "Oh right, ok. I'll make sure that i do loads of work during the day!  got a really nasty cough today and is dry n shot so that should really help it!", "ham", "ham", "4.350506850379103e-15", "1.0", "True"], ["3695", "I am in tirupur.  call you da.", "ham", "ham", "0.0021466338775591506", "0.9978533661224404", "True"], ["3906", "No that just means you have a fat head", "ham", "ham", "0.00047796907311868745", "0.9995220309268802", "True"], ["575", "You have won ?1,000 cash or a ?2,000 prize! To claim, call09050000327", "spam", "spam", "0.999999999902478", "9.752302319189066e-11", "True"], ["2899", "Come aftr  &lt;DECIMAL&gt; ..now i m cleaning the house", "ham", "ham", "4.1870024890240116e-10", "0.9999999995812985", "True"], ["3456", "Friendship poem: Dear O Dear U R Not Near But I Can Hear Dont Get Fear Live With Cheer No More Tear U R Always my Dear. Gud ni8", "ham", "ham", "3.14704357458747e-08", "0.9999999685295653", "True"], ["5128", "Wot about on wed nite I am 3 then but only til 9!", "ham", "ham", "0.0002705442209821382", "0.9997294557790211", "True"], ["919", "Dont talk to him ever ok its my word.", "ham", "ham", "0.0018311860837581545", "0.9981688139162407", "True"], ["2505", "Congrats kano..whr s the treat maga?", "ham", "ham", "0.0157900748958667", "0.9842099251041305", "True"], ["17", "Eh u remember how 2 spell his name... Yes i did. He v naughty make until i v wet.", "ham", "ham", "2.0206154180786284e-06", "0.9999979793845832", "True"]], "shape": {"columns": 6, "rows": 10}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>message</th>\n", "      <th>true_label</th>\n", "      <th>predicted_label</th>\n", "      <th>spam_probability</th>\n", "      <th>ham_probability</th>\n", "      <th>correct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2826</th>\n", "      <td>Oh right, ok. I'll make sure that i do loads o...</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>4.350507e-15</td>\n", "      <td>1.000000e+00</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3695</th>\n", "      <td>I am in tirupur.  call you da.</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>2.146634e-03</td>\n", "      <td>9.978534e-01</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3906</th>\n", "      <td>No that just means you have a fat head</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>4.779691e-04</td>\n", "      <td>9.995220e-01</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>575</th>\n", "      <td>You have won ?1,000 cash or a ?2,000 prize! To...</td>\n", "      <td>spam</td>\n", "      <td>spam</td>\n", "      <td>1.000000e+00</td>\n", "      <td>9.752302e-11</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2899</th>\n", "      <td>Come aftr  &amp;lt;DECIMAL&amp;gt; ..now i m cleaning ...</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>4.187002e-10</td>\n", "      <td>1.000000e+00</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3456</th>\n", "      <td>Friendship poem: Dear O Dear U R Not Near But ...</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>3.147044e-08</td>\n", "      <td>1.000000e+00</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5128</th>\n", "      <td>Wot about on wed nite I am 3 then but only til 9!</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>2.705442e-04</td>\n", "      <td>9.997295e-01</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>919</th>\n", "      <td>Dont talk to him ever ok its my word.</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>1.831186e-03</td>\n", "      <td>9.981688e-01</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2505</th>\n", "      <td>Congrats kano..whr s the treat maga?</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>1.579007e-02</td>\n", "      <td>9.842099e-01</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Eh u remember how 2 spell his name... Yes i di...</td>\n", "      <td>ham</td>\n", "      <td>ham</td>\n", "      <td>2.020615e-06</td>\n", "      <td>9.999980e-01</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                message true_label predicted_label  spam_probability  ham_probability  correct\n", "2826  Oh right, ok. I'll make sure that i do loads o...        ham             ham      4.350507e-15     1.000000e+00     True\n", "3695                     I am in tirupur.  call you da.        ham             ham      2.146634e-03     9.978534e-01     True\n", "3906             No that just means you have a fat head        ham             ham      4.779691e-04     9.995220e-01     True\n", "575   You have won ?1,000 cash or a ?2,000 prize! To...       spam            spam      1.000000e+00     9.752302e-11     True\n", "2899  Come aftr  &lt;DECIMAL&gt; ..now i m cleaning ...        ham             ham      4.187002e-10     1.000000e+00     True\n", "3456  Friendship poem: Dear O Dear U R Not Near But ...        ham             ham      3.147044e-08     1.000000e+00     True\n", "5128  Wot about on wed nite I am 3 then but only til 9!        ham             ham      2.705442e-04     9.997295e-01     True\n", "919               Dont talk to him ever ok its my word.        ham             ham      1.831186e-03     9.981688e-01     True\n", "2505               Congrats kano..whr s the treat maga?        ham             ham      1.579007e-02     9.842099e-01     True\n", "17    Eh u remember how 2 spell his name... Yes i di...        ham             ham      2.020615e-06     9.999980e-01     True"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Make predictions on the test set\n", "# - Method: predict() classifies messages based on learned probabilities\n", "# - Process: For each message, calculates P(class|words) using <PERSON><PERSON>' theorem\n", "# - Returns: NumPy array of predicted labels\n", "y_pred = nb_model.predict(X_test_vectorized)\n", "\n", "# Get prediction probabilities for each class\n", "# - Method: predict_proba() returns probability estimates\n", "# - Returns: Array of shape (n_samples, n_classes) with probabilities\n", "y_pred_prob = nb_model.predict_proba(X_test_vectorized)\n", "\n", "# Display prediction summary\n", "print(y_pred)\n", "print(y_pred_prob)\n", "\n", "# Display sample predictions with probabilities\n", "\n", "# Add visual indicator for correct/incorrect predictions\n", "display(pd.DataFrame({\n", "    'message': X_test.head(10),\n", "    'true_label': y_test.head(10),\n", "    'predicted_label': y_pred[:10],\n", "    'spam_probability': y_pred_prob[:10, 1],\n", "    'ham_probability': y_pred_prob[:10, 0],\n", "    'correct': y_test.head(10) == y_pred[:10]\n", "}))\n"]}, {"cell_type": "markdown", "id": "separator10", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "1646c715", "metadata": {}, "source": ["# <a id='toc10_'></a>[Model Evaluation](#toc0_)\n", "\n", "We comprehensively evaluate model performance using multiple metrics to understand strengths and weaknesses."]}, {"cell_type": "markdown", "id": "helper_function_section", "metadata": {}, "source": ["## <a id='toc10_1_'></a>[Evaluation Helper Function](#toc0_)\n", "\n", "We create a reusable function for consistent evaluation across datasets."]}, {"cell_type": "code", "execution_count": 30, "id": "evaluation_helper", "metadata": {}, "outputs": [], "source": ["# Helper function for comprehensive model evaluation\n", "def evaluate_model(y_true, y_pred, y_pred_proba=None, dataset_name=''):\n", "    \"\"\"\n", "    Comprehensive evaluation of classification model performance\n", "    \n", "    Parameters:\n", "    - y_true: True labels\n", "    - y_pred: Predicted labels  \n", "    - y_pred_proba: Prediction probabilities (optional)\n", "    - dataset_name: Name for display (e.g., 'Training', 'Test')\n", "    \n", "    Returns:\n", "    - dict: Dictionary containing all metrics\n", "    \"\"\"\n", "    # Calculate metrics\n", "    accuracy = accuracy_score(y_true, y_pred)\n", "    precision = precision_score(y_true, y_pred, pos_label='spam')\n", "    recall = recall_score(y_true, y_pred, pos_label='spam')\n", "    f1 = f1_score(y_true, y_pred, pos_label='spam')\n", "    \n", "    print(f\"\\n{dataset_name} Set Performance:\")\n", "    print(\"=\" * 50)\n", "    print(f\"Accuracy:  {accuracy:.4f} ({accuracy*100:.2f}%)\")\n", "    print(f\"Precision: {precision:.4f} ({precision*100:.2f}%)\")\n", "    print(f\"Recall:    {recall:.4f} ({recall*100:.2f}%)\")\n", "    print(f\"F1-Score:  {f1:.4f} ({f1*100:.2f}%)\")\n", "    \n", "    # Calculate ROC-AUC if probabilities provided\n", "    if y_pred_proba is not None:\n", "        # Convert labels to binary (spam=1, ham=0)\n", "        y_true_binary = (y_true == 'spam').astype(int)\n", "        # Get spam probability (column 1)\n", "        spam_proba = y_pred_proba[:, 1]\n", "        roc_auc = roc_auc_score(y_true_binary, spam_proba)\n", "        print(f\"ROC-AUC:   {roc_auc:.4f} ({roc_auc*100:.2f}%)\")\n", "    else:\n", "        roc_auc = None\n", "    \n", "    return {\n", "        'accuracy': accuracy,\n", "        'precision': precision,\n", "        'recall': recall,\n", "        'f1': f1,\n", "        'roc_auc': roc_auc\n", "    }"]}, {"cell_type": "markdown", "id": "performance_section", "metadata": {}, "source": ["## <a id='toc10_2_'></a>[Performance Metrics](#toc0_)\n", "\n", "### Understanding the Metrics:\n", "\n", "- **Accuracy**: Overall correctness of predictions\n", "- **Precision**: Of messages predicted as spam, how many are actually spam?\n", "- **Recall**: Of actual spam messages, how many did we catch?\n", "- **F1-Score**: Harmonic mean balancing precision and recall\n", "- **ROC-AUC**: Area under the ROC curve (probability calibration)"]}, {"cell_type": "code", "execution_count": 31, "id": "performance_evaluation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "COMPREHENSIVE MODEL EVALUATION\n", "============================================================\n", "\n", "Training Set Performance:\n", "==================================================\n", "Accuracy:  0.9928 (99.28%)\n", "Precision: 0.9748 (97.48%)\n", "Recall:    0.9716 (97.16%)\n", "F1-Score:  0.9732 (97.32%)\n", "ROC-AUC:   0.9953 (99.53%)\n", "\n", "Test Set Performance:\n", "==================================================\n", "Accuracy:  0.9865 (98.65%)\n", "Precision: 0.9653 (96.53%)\n", "Recall:    0.9329 (93.29%)\n", "F1-Score:  0.9488 (94.88%)\n", "ROC-AUC:   0.9877 (98.77%)\n", "\n", "==================================================\n", "OVERFITTING ANALYSIS\n", "==================================================\n", "Training - Test accuracy difference: 0.0063\n", "✓ Excellent generalization - minimal overfitting\n"]}], "source": ["print(\"=\" * 60)\n", "print(\"COMPREHENSIVE MODEL EVALUATION\")\n", "print(\"=\" * 60)\n", "\n", "# Evaluate on Training Set\n", "y_train_pred = nb_model.predict(X_train_vectorized)\n", "y_train_pred_proba = nb_model.predict_proba(X_train_vectorized)\n", "train_metrics = evaluate_model(y_train, y_train_pred, y_train_pred_proba, 'Training')\n", "\n", "# Evaluate on Test Set\n", "test_metrics = evaluate_model(y_test, y_pred, y_pred_prob, 'Test')\n", "\n", "# Check for overfitting\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"OVERFITTING ANALYSIS\")\n", "print(\"=\" * 50)\n", "train_test_diff = train_metrics['accuracy'] - test_metrics['accuracy']\n", "print(f\"Training - Test accuracy difference: {train_test_diff:.4f}\")\n", "\n", "if train_test_diff > 0.05:\n", "    print(\"⚠ Warning: Potential overfitting detected\")\n", "elif train_test_diff < 0.01:\n", "    print(\"✓ Excellent generalization - minimal overfitting\")\n", "else:\n", "    print(\"✓ Good generalization - acceptable difference\")"]}, {"cell_type": "markdown", "id": "classification_report_section", "metadata": {}, "source": ["## <a id='toc10_3_'></a>[Classification Report](#toc0_)\n", "\n", "Detailed per-class metrics provide insights into model performance for each category."]}, {"cell_type": "code", "execution_count": 32, "id": "8b49ddaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "         ham     0.9897    0.9948    0.9923       966\n", "        spam     0.9653    0.9329    0.9488       149\n", "\n", "    accuracy                         0.9865      1115\n", "   macro avg     0.9775    0.9639    0.9705      1115\n", "weighted avg     0.9864    0.9865    0.9864      1115\n", "\n"]}], "source": ["# Generate detailed classification report\n", "# - Function: classification_report() provides comprehensive per-class metrics\n", "# - Parameters:\n", "#   - y_test: True labels\n", "#   - y_pred: Predicted labels\n", "#   - target_names: Display names for classes\n", "#   - digits: Number of decimal places\n", "# - Returns: String with formatted metrics table\n", "report = classification_report(\n", "    y_test,\n", "    y_pred,\n", "    target_names=['ham', 'spam'],\n", "    digits=4\n", ")\n", "print(report)"]}, {"cell_type": "markdown", "id": "confusion_matrix_section", "metadata": {}, "source": ["## <a id='toc10_4_'></a>[Confusion Matrix Analysis](#toc0_)\n", "\n", "The confusion matrix shows exactly where the model makes mistakes."]}, {"cell_type": "code", "execution_count": 33, "id": "d891c2f3", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABasAAAJoCAYAAABoXL1oAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACxpUlEQVR4nOzdd3gUZdfH8d+mkQaEJkhXqhQl9F6CFOkdpAjIIwoEAak+oFIFpIqAIBaq9N6lRpAOofcOAek9gbR9/8ibebIkgV1IsiH5frhyXdmZ2XvvnS05nDlzxmQ2m80CAAAAAAAAAMCOHOw9AQAAAAAAAAAASFYDAAAAAAAAAOyOZDUAAAAAAAAAwO5IVgMAAAAAAAAA7I5kNQAAAAAAAADA7khWAwAAAAAAAADsjmQ1AAAAAAAAAMDuSFYDAAAAAAAAAOyOZDUAAACQzISGhtp7CgAAAEA0TvaeAAAAidmSJUv09ddfS5Lc3d21Zs0avf3229G2a9Omjfbs2SNJmjlzpkqVKvVKj3f16lVVrVrVuH3q1KlXGieu+fj4KCAgINpyk8kkZ2dnpUyZUu+++67q16+vJk2ayGQy2WGW9vXs2TPNnTtXGzdu1Pnz5/XgwQO5uroqS5YsKl26tFq3bq3s2bPbe5oJ5tmzZ1q4cKE2btyo06dP6+HDh/Lw8FDevHlVrVo1NW3aVG5ubhb32b17tz755BNJUpYsWbR582Z7TN1qUT/3w4cPV6NGjYx1p0+f1rhx43TgwAE9efJEadOm1YcffqhPP/3U7p/xNWvWaM6cOZozZ47F8nz58hm/b9q0SVmzZk3oqcWrjRs3auXKlTp06JDu3LkjFxcXZcuWTRUqVFDr1q2VMWNGe09RCxcu1Jw5c3ThwgU5ODgoa9as+uqrr1SlSpUEefyof/NKliypWbNmJcjjvoply5apb9++xu1vv/1WrVq1suOMIkT9e/k68cCreNF3EgAAbwqS1QAAWCkwMFDDhw/XhAkT7D2VRMNsNis4OFh37tzRnTt3tHfvXh04cEDDhw+399QS1I0bN9SmTRtdunTJYvnjx4916tQpnTp1SvPnz9fo0aNVrVo1O80y4Rw/fly+vr7RDnDcv39fe/bs0Z49ezRr1ixNmjRJefPmtdMs409wcLDat2+v27dvG8tu3Lih69ev23FW0s2bN9WjRw/t27dPWbJksetcEtK9e/fUrVs37d6922J5cHCwTpw4oRMnTmj27Nn6/vvv9dFHH9lplhHJ9AEDBlgsO336NFXwsVi0aJHF7QULFiSKZDUAAHg9JKsBALDB+vXrtW3bNlWoUCFexk+ZMqU+++yzeBk7rlSqVMlIMJrNZoWEhOjAgQM6cuSIpIjKvLp166ps2bL2nGaC6tOnj5Godnd3V6VKlfTWW2/pzp072rx5swIDA/X06VP17t1b69evTxQVnPHl3LlzatWqlQIDAyVJqVOnVsWKFeXl5aVTp04ZVX+XL1/WF198oaVLlyp16tT2nPIrq1Onjj744ANJski6nz171iJRXbduXaVJk0be3t52/YxfuHBB+/bti3V91HmlTJkyIaYU74KCgvTJJ5/o9OnTkiRnZ2eVL19e2bNn1/Xr17VlyxaFhIQoMDBQvXv3VubMmY3XNKHt2rXL+D19+vSqXbu2nj59qvfffz/B5pA3b17jfZAtW7YEe1xbXbx4UXv37rVYdvLkSR0+fDhB91diE9t3EgAAbxKS1QAA2GjIkCFatWqVXFxc4nzs1KlTq1evXnE+blyqWbNmtFOLzWaz2rRpYyQP/Pz8kk2y+tq1a0aSycXFJVqrmIsXL6phw4YKDAxUUFCQVq1apQ4dOthruvGud+/eRqK6RIkSmjhxory8vIz1GzduVLdu3RQaGqqAgADNmTNHnTt3ttNsX0/z5s1jXP748WPj94wZM2r06NEW6xPrZzyxzut1jB8/3khUZ8iQQb/++qvy589vrL948aLatGmjmzdvKiQkROPGjdP06dPtMtcnT54Yvzdp0kQ9evRI8DkUKlRIhQoVSvDHtVXUqurUqVPrwYMHkqT58+cn62R1bN9JAAC8SbjAIgAANrp06ZJ++eUXq7e/efOmBg0apGrVqqlw4cIqUKCAypUrpy+++EL+/v4W2169elX58uUzfqSIarHI20WKFDESgVF9+umnxjbz5883loeFhWnu3Llq3LixihQpoqJFi6pp06aaM2dOnJ5abjKZLKoRzWZztG22b9+uzz77TGXKlFGBAgX0/vvvq0aNGvr+++/18OFDY7vmzZsbz2XGjBnRxvn999+N9c8nOY8cOSJfX1+VLl1ahQoVUtWqVTVw4ED9+++/Mc575cqV+uSTT1SqVCkVKFBA3t7eatCggSZPnqynT59a9dxv3rxp/O7o6BitD3POnDnVoUMHlS9fXuXLl1eKFCmMdbt37zaey5dffqmHDx9q8ODBKl++vD744AM1adJEa9asifFxL1y4oD59+qhKlSpGgqlixYrq0aOHzp07Z7HtTz/9ZDzOkiVLdPLkSXXs2FFFixZVqVKl1Lt3b929e1dSRDK5adOmev/991W+fHkNGDBA9+7ds2pf7NixQ8eOHZMUUWE+fvx4i0S1JH344Ydq0aKFXFxcVKxYMXl6elo1ttls1pw5c9S8eXMVK1ZM7733nooWLapGjRpp5syZCgsLs9g+LCxMc+bMUYsWLVS8eHEVKFBAxYsXV/PmzTVr1qxo20sRVeF9+/aVj4+PsU99fHzUu3dvnT17Ntr2bdq0sdivUkS/2jZt2hjb3Lhxw9jm6tWrMX7Go3r06JEmTJigunXrqkiRIipZsqQaNmyo3377LcbP/pEjR9S1a1eVL19eBQsWNN73/fv3140bN4zt+vXrZ/QDl6SAgADly5dPPj4+xrKo87p69Wq0x9q2bZu6dOmiihUrqlChQqpQoYK++uorHT58ONq2Ud/bPXr0UFBQkMaMGSMfHx8VLlxYNWrU0C+//BLj6xBXHj16pAULFhi3hw4dapGoliI+nwMGDJDJZFLevHn1zjvvKCQkxGKb0NBQLViwQC1btlTp0qWN+Q8bNizG75aon7e5c+cqICBAvXr1UunSpfXBBx+oWbNm2rhxo7F95L6KfA9J0pQpU5QvXz7jvdSvXz9jzJ9++sni8aLu66ivpyQ9ffpUU6ZMUaNGjeTt7a0CBQqoVKlSatOmjZYtWxZt7kuWLDHGivo+jhQYGKg//vhDTZo0UYkSJVSkSBHVrVtX48eP1/3796NtH3Xef//9t06ePKlOnTqpRIkS8vb2Vtu2baNVSL9MaGioxdxHjBhh/L5mzRqLg0VRRf28njt3Tnv27FHbtm3l7e2t4sWLq1OnTjH2kA8ODtbUqVPVoEEDeXt767333lOJEiXUokWLGPfh88LCwlSxYkXjsaO+9pGGDBlirB82bJixPC6+kyL5+fmpY8eOKlu2rAoWLKgiRYqodu3a+uGHH2J87QAAsBcqqwEAsJKnp6fxn+BffvlF9evXf+lp0nfv3lXr1q2j9TK+ffu2tmzZor///lu//vrrC6uQ8+fPr4IFC+rYsWMKCgrSxo0bVa9ePWP9rVu3jMpeV1dX1apVS1LEf+h9fX21ZcsWi/EOHz6sw4cPa9OmTZoyZUqcVIiHhIRYnMJesmRJi/Xr169X9+7dFR4ebiwLCwvTxYsXdfHiRW3fvl2LFy+Wm5ubmjRpooMHD0qSVq9erbZt21qMtXbtWuP3hg0bGr8vX75c//3vfy2S8FevXtXcuXO1du1a/frrrypcuLCxbvTo0Zo2bZrF2IGBgUYP271792rq1Kkv3T85cuSQo6OjwsLCFBQUpEaNGqlly5by8fHRu+++K0ny9fV94RiS9PDhQzVr1kwXLlwwlh05ckQ9evTQ+fPnLca4cOGCWrZsaSSYI924cUNr1qzRli1btHDhQuXJkyfa4+zevVvfffedgoODjWUrVqzQ6dOnVbNmTY0fP95YfuvWLS1cuFBHjx7VokWL5OT04tBx69atxu+VKlVS+vTpY9yuR48e6tu3r03vvWHDhkW72NuTJ0907Ngx42fkyJHGul69ekVL9D969EgHDx7UwYMHdfToUYvtjx49qjZt2kRLCAcEBCggIEBbtmzRzJkzVaBAAavnbKtr166pbdu2unz5srEsKChIDx480PHjx7V+/XpNnz5d7u7ukqQDBw6offv20Q6sXL16VYsWLdLWrVu1bNkyZciQ4bXmZTabNWjQIM2dO9di+c2bN7V69WqtWbNGPXv2jLW9SWBgoFq1amUcyJAiKprHjBmjGzdu6Jtvvnmt+cVm9+7dxuuZIUMGVapUKcbtqlatqt27d8fYjubevXvq3LmzDhw4YLE88rtr6dKl+umnn1SmTJkYxz537ly0RO6hQ4fUpUsXTZgwQTVq1HjFZ/dyoaGh+vTTT7V//36L5VF7x1++fFlffvmlVeNduXJFn3/+ebSDYadPn9bp06e1ZMkS/fLLL9EOCETas2ePZs6cqWfPnhnLdu3apf3792v27NkqUqSIVfPw8/PTrVu3JEWcveHj46MPPvhAhw4dUmBgoFauXKmPP/74hWOsXLlSU6dOtfibtHnzZu3evVvLly83/raHh4frq6++0oYNGyzu//DhQ/n7+8vf3/+l+9DR0VENGzbUlClTJEX8Xfvwww+N9eHh4Vq/fr1xO/LvWlx+J82dO1cDBw60WBYaGqqzZ8/q7Nmz2r59u2bPnq1UqVK9dCwAAOIbldUAAFipatWqRrLz2bNnGjp06EvvM3XqVCNRnS1bNrVp00bNmzc3kkdhYWGaOXPmS8dp0qSJ8fuqVass1q1evdqoTqxevbrRa/bnn382EtXOzs6qU6eOmjVrZiRk/vnnH02cOPGlj/28devWafTo0Ro9erRGjRqlwYMHq27dujp69KgkqWLFihbVfSEhIRo8eLCRFKhQoYLatWunGjVqyNHRUVJEQufvv/+WJH300UdGMu7QoUMWFZ4BAQFGFaeXl5eRfDp//rwGDBhgJKqLFCmi1q1bq2DBgpIikjPdu3c3kiQ3b97Ub7/9ZuybGjVqqH379qpVq5acnZ0lRVQJr1ix4qX7I02aNBavT0BAgEaNGqWPPvpIFSpUMJKmURM0Mdm5c6cuXLigEiVK6OOPP1bOnDmNdRMnTjQS+JI0ZswYI1GdL18+tW3bVo0bNzYSDUFBQZo3b16Mj7Ns2TK5ubmpefPmFgdJTp48qfHjxytt2rT6+OOPVaJECWPdiRMn9M8//7x0Xxw/ftz4/UUJFE9PT5sS1adOnTIS1S4uLqpfv77atm2r4sWLG9ssX77c2CeHDx82EtXu7u6qW7eu2rVrJx8fH5lMJkkR+2Hnzp3G/X/88UcjKVSoUCG1bt1aLVu2VObMmSVFJLqjVjzGpkWLFqpTp47Fc/3ss8/02WefvbQPdM+ePY1EdcqUKdWwYUM1btzY4vMQ9TM7dOhQI1FdtGhRtWvXTnXr1pWrq6ukiINiK1eulBRx8CCmebVo0eKlz2n69OkWierixYurZcuWxufLbDZr9OjRsZ4FsHXrVh07dkwVKlRQ69atlS5dOmPd/PnzLdpfxKWo78f8+fMbr/3znJycYu2b/vXXXxuJaicnJ1WvXl3NmjUzWv08evRIXbp0sTjAENWsWbMUFBSkevXqqVGjRsb3iyT98ccfkqTMmTPrs88+s+gxXLRoUX322WcWr5mt/vrrLyNRnSZNGjVq1Ejt2rWzSKxPnjw52sHUmISEhKhbt25Gotrd3V116tRRo0aNlCZNGkkRB8s+//xzoyXH86ZNmyYXFxc1bdrU4kKWISEh0Q5EvUjUFiCRLakaN25sLItaTR+bn3/+WenSpVPLli1VsWJFY/mTJ0+0cOFC4/aWLVuMRLWnp6eaNm2qNm3a6L333jO2mTFjRoxnE0XVpEkT4/23ZcsWiwT0/v37jeR73rx5je/OuPpOCgkJMVoRmUwmVa5cWe3atVODBg3k4eEhKeI7NvL9CACAvVFZDQCAlRwcHDRw4EA1bdpU4eHh2rp1qzZu3GhRIfW8nDlzqk6dOrpw4YJ+/fVXpU2bVpJUr149tWrVSlJEtdrL1K1bVyNHjtTTp0/1zz//6O7du8ZYUROqkf9hDw4OtkiCT5s2zUhQdOzYUfXq1VNgYKDmzJmjTp06RWtd8SJ+fn7y8/OLcV2+fPk0evRoOTj873j4gwcP1KBBA508eVJZs2bVoEGDjHX//e9/tXjxYkn/2w+enp6qWbOmcQrzmjVr1LFjR0kRifJIderUMRKeM2fONCqFGzRoYFTMhoeHq3PnztqyZYuuXr2qdevWqX79+goICDCS5zVq1NCYMWOMcdeuXatVq1Ypd+7cypo1q1X7ZMCAAXr48KFF1bcUkRRfuXKlVq5cqfTp02vQoEEvfL906NBBffr0kRRRjdq6dWsdO3ZMZrPZovKwcOHCcnBw0N27d/Xbb78ZrUVKliypvn37WuzP5zk5OenPP/9U7ty5JUn169fXyZMnJUUkoBYvXqzMmTPLbDarbt26OnPmjKSIAwKxVaZGitou5Pn2H6/j2bNnatmypU6ePKnGjRsbBwfMZrNq1KihS5cuyWw26+rVq0qbNq1F4vCTTz6x6P07a9Ys7d+/X7lz57ZIHkfex9nZWbNnzzY+E927d1evXr2ULVs25c6dW2FhYcZBlph07NhRu3fvNg4qPd+H/tGjRzHe78CBA0ZS1NPTU0uXLjWqOxs0aKA2bdrI0dFRBw4ckNls1rNnz1ShQgW99dZbcnR01E8//WR87iZNmqQJEyZI+t/74KOPPlLatGljnVdsnj59qkmTJhm3e/TooS+++EJSxOdr0KBBxoGR0aNHq2bNmhaf/0g9e/Y0Psd16tQxkuQhISG6dOlSvFSsR30/vspFPA8cOGAc8HN0dNSMGTOMAySPHz9Wu3btdOTIET158kQTJ07UDz/8EG0MBwcHzZgxQ97e3pIi/iaMHTtWkozPVrZs2dSrVy/dvn3b6K9dtmxZde3a1eY5RxX1O+Crr75Ss2bNjNvjxo1TQECAcufObVFdHJt169YZlfGenp5asGCBcuXKJSnie65FixYKCAjQv//+q+nTp6tbt27RxnB3d9eSJUuUPXt2SVKqVKmMtlWR++Jlbt26ZRzYdHd3NyrTa9eureHDhysoKEjHjx/XkSNHLM6ked7bb7+tJUuWGH9HO3TooO3bt0ebi6urq5o2bapTp06pW7duKl++vKSIz0WZMmUUGBiox48f6969e8ZYMcmWLZtKlCihPXv2KCgoSFu2bFHt2rUlxX62UFx9J927d884K8zb21tTp0411jVt2lTTpk1Trly5jINPAADYG8lqAABsUKhQIX388ceaM2eOpIjWBOXKlYt1+48//tjidOT79+/r0KFDFqcUW9MbOWXKlKpevbpWrFih0NBQrVu3Ti1bttT58+eNBELWrFlVqlQpSdKxY8eMPtDZs2e3qKTLli2bSpYsqa1bt+rx48c6fPiwcb/XderUKdWsWVM///yzkVhNnz69evfubWxjNpt16dIl7du3TydOnDCWR90PjRs3NpLVq1evNpJcUf9T36BBA+P3qBWyTZs2NX53cHBQgwYNjITTzp07Vb9+feXKlUuurq56+vSpVq1apevXr6t8+fIqVqyYqlSpYlH1Zw0XFxeNHz9erVu31sKFC+Xn5xetz/Pt27fVtWtXTZ061aKSL+pco/bgdnd313/+8x8j0Rq1DcHnn39ucd9bt27J399fmzZtMpbF9r4qXLiwkaiWpHfffddIVleoUMGo2jOZTMqdO7eRuLGm+jVqCxZrEmDWev/99y0umhYSEqJTp05p165dFsnfyOr19957TyaTSWazWdOmTdPRo0dVtmxZFStWTC1atIixF2/BggV18eJFhYSEqGbNmqpataqKFy+uYsWKRWsXEx92795t/F6lShWLFkMlS5bU0qVLlStXLuPAhKura7QL8F27dk379++3GMva3uux2b59u7GPs2TJYtHqw8HBQb1799by5csVFBSkgIAAnThxIlrSy9HR0aJftre3t1KlSmV8R8XUizsuRO2H/bLK15hE/b6pXbu2RSW/p6enevbsqXbt2kmKaCERHh4eLVH/wQcfGIlqKaLCPTJZHV/PO1LUAwBDhgyRn5+fypQpo6JFi6pbt24xHlSITdR90aZNGyNRLUlvvfWWOnXqpAEDBkj630VUn+fj42MkqqWIs3Aik9XW7oulS5ca3zM1a9Y0KoM9PT1Vo0YNo4f0ggULXpisrl+/vkVyuWLFikayOupcypUrZ/E3/tmzZzp69Kh27txp8Z6y5nPWpEkT7dmzR1LE37XatWsrPDxcf/31l6SIz0ndunWN7ePqOyl9+vTKmDGjbty4oQMHDqhhw4aqWLGiihcvHi15DQBAYkCyGgAAG/Xo0UN//fWXbt26pWvXrmny5Mkv3P7gwYNatGiR9uzZE+Pp1tYmUZo0aWJUUa9atUotW7a0qKpu2LChcZrx9evXjeWXL1+O8UJukc6dO2dTsnr48OHGqdfh4eEKDAzU5cuXNXbsWG3btk13795Vp06dtGHDBuPieSEhIVq2bJn++usvHTx40OKCipGi7ofixYvrnXfe0YULF3Ty5EmdO3dOrq6uOnLkiCQpd+7cFomIqBc5i6xYj+25ShEVfYMHD1b//v0VEhKi/fv3G6fLu7i4qEyZMmrVqtVLK4mfV7x4cRUvXlxms9lIpm7YsEH79u0z9tfYsWNjTFa/9dZb0S42GNnzWopIdke1fft2LV26VPv377d4vSPF9r7KlCmTxe2o7TierySPbCfxovGiSpMmjS5evChJcX7Brrt372r+/Pny8/PTsWPHLHpuR4pMkOfKlUtfffWVxo4dq7CwMG3fvt1IRLm7u6tixYpq166dRRKxd+/eOnbsmC5evKh///1Xc+bMMQ5K5cmTR/Xq1VPr1q2NlhxxLep7OEuWLNHWx1R5bDabtW7dOq1evVoHDhzQnTt3YtzmdUT9zsqXL1+0Ck5PT09lz57duDDd5cuXoyWr06RJY/FekiQPDw/je8Caiyz+8ssvMX5vfPbZZ7FWTUet7n+V92PUCv2Yqk6jviaPHj3S/fv3o1XXRrYLiRSZXJXi7oBObONUqFBBH3/8sebOnavg4GBt3LjRuLhf6tSpVbVqVX366acx9rZ/3sv2RdRlsZ3VERf7IvJMHCmip3OHDh2M21Evdrtq1Sr169fP4jGiev578EVzCQgI0Lx58/TPP//o1KlTMV6c2Jr516hRQ0OGDNGjR4+0bds2PXr0SCdOnDBagJQvX96iv3xcfSc5ODhoxIgR8vX11ZMnT3T8+HGjRY6jo6OKFSumZs2aqU6dOrG2ygEAICGRrAYAwEYpU6ZU3759jVPo//jjj1gvYDZ9+nSNGDFCZrNZ7u7u8vHxkbe3t7JmzRqtKvJlSpYsqezZs+vy5cs6cOCAbty4YVS7mUwmI4EsWf7H2dnZ+YWnwFuTKIqNg4ODPD09VaBAAY0fP16lS5dWSEiI7t69q82bN6tevXp6+vSp2rVrJ39/f0kRCdEaNWqoSJEiOnz4sFFZ97zGjRsbfTbXrFlj0aokalX1888hTZo0sZ4SHfUCgfXr11fx4sW1bNkyo6duWFiYgoODjVYnUVsXxObQoUO6cOGCbt68qeLFi6to0aIymUzKnz+/8ufPr3bt2mnGjBn6/vvvJUX0fw4KCorWeiWmntZR+9tGTSIMHz5c06dPlxSRdKpZs6a8vb3l5OSkIUOGvHC+zycNo477/Dpb5c2b13ido/YLft7GjRu1dOlSVa9eXVWqVHnpRb0uXryoli1bGsnYQoUKqWTJkipSpIimTZtmHMSIqmPHjqpataqWLVsmPz8/nT59WmazWYGBgVq3bp3Wr1+vMWPGGKfiv/3221q5cqXWrl2rDRs2WFRtnzlzRmPGjNHatWs1b948o7o5LkVNKlvzmTSbzfryyy+NqswMGTKofv36KlKkiG7duvXSg2jWetlFNSPnEimmZFdM+8uWql5JmjdvngICAqItb9GiRazfb1EP0p08eVJmsznG+d2+fVvdunVTtWrVVK1aNeNgwcueuzUHAp5/7nGRDHw+MRoSEhLrtgMHDlSjRo20YsUKbdu2zTiY9ODBAy1ZskQrV67U9OnTLarGY2LLvojtOT6/L2x9D+zdu9eYv/S/CzvGJDAwUKtWrVLz5s1fay779+/Xf/7zHwUGBsrBwUHFihVTsWLF5O3trW+++cYiQf4yrq6uqlOnjnHwYMOGDRbfXc//XYvL76SyZctq48aNWr58uTZv3ix/f3+FhIQoLCzMuNjmvn37LNp0AQBgLySrAQB4BXXr1tWiRYu0a9cuhYSE6Nq1a9G2efDggcaOHSuz2SxnZ2etWrXKSIKcPXvW5sc0mUxq3Lixxo0bJ7PZrClTphj/cS9TpozRvkGSMmbMaPyeNWtWi17Pkl7a4/JVeHp6KmXKlMZF7iIrRZcsWWIkMCtWrKhffvnFSGacP38+1vEaNGig8ePHKzQ0VKtXrzYq3xwcHFSvXj2LbTNmzGhciHHKlClGCxLpxc81Xbp0+uSTT9SlSxc9fvxYhw4d0urVq43qvcmTJ6tDhw4v3FezZ882KtyrV6+uokWLRtumZMmSFrefPXsWLVl979493bp1y+LAR9TETORreu7cOSNRnSFDBq1evdpI1sXWS9xar5tIq1y5snHwIbLKPqY+rgsWLJCfn582btyo4sWLG9WCsRk/fryRqH7+AMKLTofPmDGjOnXqpJ49e+r+/fvy9/fXwoULtWnTJpnNZk2YMMFIVksRBwfKly+v+vXrKzw8XKdPn9bu3bs1ceJEPXz4UMePH9eGDRte66J3sYladRr1dY80fvx4eXl5KVeuXCpRooR2795tJKrz58+vhQsXGlXyL9uftoha5X3q1Klon6fHjx9bVNJGvTCovZUpU0YuLi4KDg7WnTt39Pfff8d4tsSyZcu0b98+7du3T2PHjtWuXbvk7u5u8Z0a08GXqG2MvLy8jAsNxoeoydTnW04833LoeTly5FCfPn00YMAA3bx5U/v379fs2bO1b98+hYSE6OeffzYuOBubzJkzG8/3+PHjqlatmsX6qPsnR44cVj0nW0W98KE15s+fH2uy2lojRoww2oKMHj3a4vsipgrrl2ncuLFxsdKVK1cabZZSpUqlqlWrRts+Lr+TPD091bBhQ7Vv315Pnz7V0aNHtWXLFv3666+SIg4IdenSRW+99ZbNzwsAgLhk2+FsAABg+O677ywqX5934cIFo1rW0dHR4mJuUdt32HIqeMOGDY1EUeRFzaT/XVgxUqFChYxTgy9cuGCRxLx586ZKliypmjVrqmvXrsYpyK9rz549RqJaktGbNLIfshTxH/LIhOjjx4+NXtJS9P2QIUMGo13GhQsXdPToUUkRFWJRk/GSZTJ45syZFmN1795d5cqVU9u2bY2k/bx58/Thhx/K29tbvXr1ktlslqenp8qVK6fu3bsb9w0KCnpp+4DKlSsbv2/YsCHaRRZDQ0P1+++/G7czZswY68UHIw9ESBEXyZwxY4axrlixYpIs96eLi4vxOpvN5ld+X8WVKlWqGC0FAgMD1bNnz2gXFJwxY4bF+zHyYokvEvU5R62iPX36tMW6yOf8008/qXLlyipWrJhx0TsvLy9VqVLFoudy5AGVa9euqVGjRvL29paPj4+uXr0qBwcH5c+fX23btrXolx1Ty5W4EPU9/Pfff1tc5O3w4cP6+eefNXz4cHXq1Mno2R3Jzc3NSFSHhIRYHJyK+j6ImmR+UTVuVGXKlDEOrAQEBFi8l81ms8aMGaOgoCBJEUnKF7Uceh2bN2/WqVOnov286CKoadOmtfhu/O6774xWQJH8/f31008/Gbdr165tfKZ8fHyM5atXrzZaBUkR31+RvaclqWrVqvHaQiHq+z7yuzDS+vXrY7zPN998o/Lly6tkyZLGd8lbb72ljz76yOJaCta8p6Pui1mzZunChQvG7Vu3bmnKlCnG7ecT2XHh8ePHxsEZKeJvaEzvh6h9+48dO2Zc0+FVxfbds337dou/d9a22ylcuLDxGdmxY4fx9/ejjz6yqI6Oy++kTZs2GWfftG/fXsHBwXJ1dVXx4sXVrVs3ixYoUdsRAQBgL1RWAwDwit5991116NDB4j/pUUWtkH369KmaN2+ucuXK6fjx4xZJD1sugJYxY0ZVqFBBW7duNZJQqVKlipYccHNzU7NmzYwK3E6dOqlq1arKmDGjNm3apMePH+vx48dKnTp1rC1MYrNu3TqLiuiwsDDdvHnT6IUqRSSJIisYo1ZprVq1SkFBQcqQIYM2bdpkkSiPaT80adJEmzdvtljWsGHDaNu1adNGy5YtU3h4uFavXq3z58+rePHiOnv2rHHxxbt37+rbb7+VJBUpUkRXr16V2WzW1q1b1bhxYxUtWlTBwcH6+++/jXHfeecdpUuX7oX7o3r16sqTJ4/OnDkjs9ms7t27a86cOcqXL5+CgoK0Z88ei8rTTz/9NNaxFi9erDNnzqhQoULas2ePUYHv4OBg9OKO+noFBASoRYsW8vb2fuEFKxOKyWTS6NGj1aJFCwUFBWnHjh2qVq2aKleuLA8PDx06dMjitHdvb+9oVfIxyZAhg5Ec+/7773X48GE9e/ZMGzZssEi6Rh4cKlSokCZOnChJmjt3rs6dO6f33ntPjx49sjhAEtn6ILKCNjLp2rhxY1WtWlUeHh46efKkcVG0qPeJa5GtBfz9/fXs2TM1a9ZM1apVU4oUKbRmzRpjuwYNGihlypQW7wN/f3+1bdtWuXPn1rZt2yz6TEd9H0TtiX7z5k317dtXkjRy5MhY5+Xh4aG2bdsa33OjR4/W33//rTx58ujgwYMWycA+ffokup63vXr10u7du3X+/Hldv35d9evXV+XKlZU5c2ZdvHhR27ZtM75Lvby8LC4MWKpUKRUvXlz79u1TaGio2rZtKx8fH3l5eWnbtm3GGTWpUqVSly5d4vV55M+f3/h9z549+uabb1SoUCFt2rQp1jMq8uXLpwULFkiSxo4dq7179+qdd97RnTt3LJK61ryna9WqpWnTpunixYt69OiRGjVqJB8fH7m4uGjr1q1G4jZLlixq27bt6zzVGK1cudL4fObMmTPWgyJZs2ZVkSJFdPDgQUkR1dWDBw9+5cfNkCGD0X7mq6++Uq1atXTnzp1of5ds+b5t0qSJhg0bZrHs+b9rcfmdVKRIEV2/fl2hoaE6fvy46tWrp7Jly8psNmvXrl3GxXNTpUplVf9yAADiG8lqAABeQ6dOnbRq1SqjBUVUWbJkUc2aNY0qx/PnzxtJXmdnZzk5ORmVu48fP452cb3YNGnSRFu3bjVu165dO8Z+ld27d9fx48e1Z88ehYWFWVSlSREXmBo1apS1T9UQ2c85Ns7Ozho6dKhRjdmkSRPNnDlTDx48kCSLJEnKlCmNytuY+tFWqlRJGTJkMJLanp6e+vDDD6NtV6BAAfXv319Dhw6V2WzWiRMnLBK3JpNJ3377rXLlyiUpIvEzaNAgfffddzKbzTFW4Lm7uxt9pl/E2dnZaBcSeRGyvXv3au/evdG2bdSoUayJnHTp0ilTpkw6fPiwDh8+bLGuR48eKlSokKSIxETUZMzRo0eNSkt3d3c9ffpU4eHhCggIiLVHb3zKnz+/pk+fLl9fX926dUv37t3T0qVLo233wQcfaPLkyVa1o+nQoYP27t0rs9msp0+fatGiRca6qO+hyM9hlSpV9MUXXxgJ1sierFGlT59eAwYMMG7/+OOPat26tf7991/dv3/f4kJukZ6/KGNcGz16tNq2baurV68qMDBQy5cvt1ifL18+9enTR1LExdomTpxoPOddu3Zp165dkmLeJ1LEATYvLy/jbIFly5bJwcFBw4YNe2FP4i+//FLXrl0zKvef358ODg7q169fjJ9Ne/P09NSsWbPk6+tr9OndsGFDtO0yZMigyZMnW1x4z8HBQWPHjlWnTp107NgxhYSERKtiTp06tSZNmhTjRTHjUvXq1TVp0iTjQMSCBQuMRHTLli31559/RrtPy5YtdfjwYS1fvlzh4eExfnfnzJnT4myS2Li7u2vSpEn64osvdOXKFaMndFSZM2fW1KlTrf5bZouon/maNWu+cNvatWsb34+RF1p81QujdujQwUh2P3jwwGjhIUX/nFmb6K1Xr55GjRplXCQ2Z86cMX6vxNV3Urp06fTjjz/K19dXISEhunDhgkVlvBTRkzzq320AAOyJNiAAALwGV1dXo1o3JqNGjVKPHj307rvvKkWKFMqcObOqVKmiOXPmGNXQZrM5WpXWi1SuXFnp06c3bj/fAiSSm5ub/vjjD3377bfy9vZWypQp5erqalSEL1682GjV8TocHBzk6uqqt99+W7Vq1dK8efMsem9mypRJixYtUs2aNZUhQwa5u7srT548ateunVavXm20L9ixY4dRRRbJycnJSNJKEadKx3YRwNatW2vu3LnG4zg7OytTpkzy8fHR7NmzLU57l6TmzZtryZIlql+/vt555x2jlULOnDnVokULrVixIsb+0zHJnj27VqxYof79+6tUqVJKmzatnJyc5OHhoezZs6tevXr6448/NHz48FiTx66urpo5c6Zat26tdOnSKUWKFHr//ff1008/WfRodnBw0G+//aZPP/1U2bNnV4oUKZQ9e3bVqlVLixYtMhIX9+7d04EDB6yaf1wrUqSI/vrrL/Xp00fe3t5KnTq1nJyclDZtWpUvX14jRozQ3LlzY+xnHZPKlSvrjz/+UIkSJZQqVSp5eXmpcOHC6t+/v8aPH29sF/VASI8ePTRz5kxVq1bN2E9ubm7KkyePPv30U61YsULvvPOOsX22bNm0YsUK9ejRQwULFlSaNGnk5OSkdOnSqWLFipo0aZK+/vrrONtHMcmaNauWLFmiL774Qrly5VKKFCmUIkUK5c2bV127dtW8efOMi1G6u7tr/vz5atKkiTJnzixXV1e98847atKkiVavXm0kXU+cOGFUALu4uOinn35S4cKFjQuvlipV6qVVoY6Ojho1apSmTp2qatWqKWPGjHJ2dlaGDBlUp04dLVy4MF6qaeNK+vTpNXfuXI0dO1ZVqlRR+vTp5eTkJE9PT33wwQfq0aOH1q5da9FaIVLGjBk1f/58DRw4UCVKlJCXl5fxPdG2bVutXLlSJUqUiPfn4ObmptmzZ6tevXpKkyaN3Nzc9MEHH2jMmDHq169fjPdxcHDQyJEjNWnSJFWoUEFZsmSRs7Oz3N3dVbBgQXXt2lWLFy+2+nOYO3duLV++XL1791bhwoWVKlUqubq6Kk+ePOrcubOWL18eL5W5J0+etGh9UqNGjRdu/9FHHxkHwZ48eaLVq1e/8mO3atVK48ePV+HCheXh4aF06dKpWLFiGjVqlHFmgmT53fMyXl5eFr3dn7+wYqS4/E6qXLmyVq1apY8//lh58uSRp6ennJ2dlSVLFtWvX1+LFi166X4FACChmMzWNtgCAABIYBcuXFC9evWMCrQ5c+bEWxsGe9i9e7c++eQTSRGV+LYctAAAvHn27t2r1q1bS4o462fz5s0WF/MEACC5ow0IAABIVAICAjR79myFh4dr5cqVRqI6X758SSpRDQBIHo4dO6a1a9cqMDDQoi1SlSpVSFQDAPAcktUAACBRcXBw0O+//26xzNnZWf3797fTjAAAeHVBQUGaNm2axbKUKVOqV69edpoRAACJFz2rAQBAovLWW28pR44cSpEihdKkSaNy5crpt99+U6lSpew9NQAAbJY1a1ZlzpxZLi4uSp8+vT788EPNmjXLuOgvAAD4H3pWAwAAAAAAAADsjspqAAAAAAAAAIDdkawGAAAAAAAAANgdyWoAAAAAAAAAgN2RrAYAAAAAAAAA2B3JagAAAAAAAACA3ZGsBgAAAAAAAADYHclqAAAAAAAAAIDdkawGAAAAAAAAANgdyWoAAAAAAAAAgN2RrAYAAAAAAAAA2B3JagAAAAAAAACA3ZGsBgAAAAAAAADYHclqAAAAAAAAAIDdkawGAAAAAAAAANgdyWoAAAAAAAAAgN2RrAYAAAAAAAAA2B3JagAAAAAAAACA3ZGsBgAAAAAAAADYHclqAAAAAAAAAIDdkawGAAAAAAAAANgdyWoAAAAAAAAAgN2RrAYAAAAAAAAA2B3JagAAAAAAAACA3ZGsBgAAAAAAAADYHclqAAAAAAAAAIDdkawGAAAAAAAAANgdyWoAAAAAAAAAgN2RrAYAAAAAAAAA2B3JagAAAAAAAACA3ZGsBgAAAAAAAADYHclqAAAAAAAAAIDdkawGAAAAAAAAANgdyWoAAAAAAAAAgN2RrAYAAAAAAAAA2B3JagAAAAAAAACA3ZGsBgAAAAAAAADYHclqAAAAAAAAAIDdkawGAAAAAAAAANgdyWoAAAAAAAAAgN2RrAYAAAAAAAAA2B3JagAAAAAAAACA3ZGsRpIWGhqqBQsWqE2bNipVqpQKFSqk8uXLy9fXVzt37rTLnObOnasaNWqoUKFCKl26tIYPHx6vj/fTTz8pX758ypcvn/bt2xevj/UyPj4+xlzy5cunqVOnRtvmk08+sdimX79+r/WYp0+ftmn7fv36GY/977//vtZjv46o+yDqT8GCBVWqVCm1aNFC8+fPV3h4uN3mGJvE+Ll7Fba+dwAAQMwSY2xATE5Mbo3IfVWgQAGdOHEi2vqor+vVq1ftMMO4t3v3buM5TZ482Vjepk0bY1/Yw7///htn70cAiRvJaiRZN2/eVLNmzfTNN99oz549un//vkJCQnTr1i1t2LBB7dq108iRIxN0Tvv379fAgQN18eJFhYSE6N69ewoJCUnQOSQm//zzj8XtZ8+eyd/fP07GvnHjhv773/+qfv36cTJeYhEaGqr79+/L399f3377bYK/h18mMX7ubHXhwgX5+vrqiy++sPdUAAB44yXG2ICY3BIx+cuFhYVp0KBBMpvN9p4KACR5TvaeABAfgoOD1alTJx07dkyS1Lx5czVp0kQeHh7atWuXxo0bp0ePHun3339X4cKFVatWrQSZ18GDB43fO3furPr168vNzS1eH7N9+/Zq2rSpJClt2rTx+li28vf319OnT+Xq6iop4j8OwcHBcTJ2v379tGPHDpvv9/XXX6t79+6SpAwZMsTJXF5HsWLFNHbsWEmS2WxWaGioDh8+rAEDBigwMFCzZs1Su3bt9Pbbb9t5pon3c2erzz//XJcuXVKWLFnsPRUAAN5oiTU2ICa3RExuHX9/fy1ZskSNGze291Ts4scff1RwcLBMJpO9pwIgiSNZjSRp6dKlOnr0qCSpXbt2+vrrr411uXLlUtasWdWxY0dJ0uLFixMsMA4KCjJ+L1eunHLmzBnvj+np6SlPT894fxxbZM2aVVevXlVwcLD27dun8uXLS5JxGmjk+tfxqlUPqVOnVurUqV/rseOSi4uLMmXKZLEsW7Zs2rNnj+bNm6ewsDAdPXo0USSrE+vnzlZUzAAAEDcSa2xATB6BmNx2o0ePVrVq1ZQqVSp7TyXBJbaDLACSLtqAIElaunSpJMnBwUGff/55tPWVKlXSyJEjtWbNGv32228W64KDg/Xbb7+pYcOG8vb2lre3t5o3b65FixZFC7Yi+3Y1atRIN27cUO/evVWqVCl98MEHatu2rY4cOWJsmy9fPv3000/G7VatWhm9zZYsWWL031q+fLnFY0T2SatWrZrF8mXLlqlFixYqVqyYChQooFKlSqlDhw7av3+/xXYv6o938eJFffPNN6pSpYoKFSqkcuXKqUePHjp58qTFdlevXjXGmD17tvz8/NS8eXO9//77KleunAYPHqwnT57E+FrEJHPmzEbVatTTDiMD45IlS8Z6361bt6pt27YqW7asChUqpFKlSql9+/by8/Oz2GdR+x/my5dPPj4+kmSxrzdt2qQWLVqoUKFCqly5su7fvx+tP15QUJBq1aplLFu5cqUxbo8ePYzl06ZNs/r5xwUnp/8da4ysgpEiTtucOHGiateuraJFi+r999+Xj4+PBgwYoBs3bkiy7EO3ceNG477r1683lkd9r549e9ZY/scff8Q6p4T+3D3fL+9l/fWs/ZxevnxZkhQQEKB8+fKpTZs2xnprP3cAAICYPCpi8jc7Jo+sJr57967GjRtn1X2sfV2jxrBLlizRZ599pkKFCqls2bI6c+aMsS8qVqyoGzduqGfPnipevLiKFy+uvn376sGDB7p8+bK6du0qb29vlShRQt26ddPNmzctHuf+/fsaMWKEqlevriJFiqhIkSKqXr26hg8frocPH770+cQUgz/f//z5n6iftRs3bqh///4qX768ChUqpA8//FAjR47Uo0ePoj3W4cOH1aFDB+P5fP3117p3755V+x3Am4/KaiQ5oaGhRgVH9uzZYz0C3KBBg2jLAgMD1aZNG+P+kQ4ePKiDBw/q77//1rhx4+To6Gix/v79+2revLmuX79uLNu1a5c+/fRTbd68WSlTpnzNZ2VpxowZ+v7776PNYfv27dq7d69mzJghb2/vF46xc+dOde7cWYGBgcay27dva82aNdqwYYN++OGHGKtb1qxZowMHDhj/SXj27JnmzJmjoKAgmy5MU6pUKS1ZssQIYB8+fKjjx49brHvexo0b1bVrV4uLCt6/f187duzQzp07NWvWLJUoUcLqOfTr188IzDJmzCgvL69o27i5uWns2LFq2rSpgoODNWzYMJUrV067d+/WmjVrJEllypTRf/7zH6sf91WFh4crMDBQu3btMv7z5+HhoUKFChnb9OzZUxs2bLC4X0BAgBYuXKi9e/dqzZo1Kl68uNKmTau7d+/Kz89PH374oSRZnKK5a9cude3aVVLEf0YiPf8ftEj2+NzZ6nU/p3HxuQMAILkgJicmt9abEJMXKVJE9+/f14ULFzRv3jw1adJEBQsWjHX7V31doyaOnZyclCtXLmNdYGBgtPf3smXLdOnSJV28eNEimbtu3TpdvXpVixcvlhTRc7tDhw7RPlOXLl3S9OnTdfLkSc2YMcPGvfJykZ/RK1eu6OOPP9atW7eMdVeuXNHvv/+ubdu2ad68ecaZB/v27VP79u0tWtEsWbJE27dvj/P5AUicqKxGkvPw4UPjAilp0qSx6b4//PCD8Qe8Tp06WrJkiebNm6dy5cpJiqg8fb7qQ4pIBnp5eWnOnDlauHCh8uXLZ8wlsnLVz89P7dq1M+4zceJE+fn5vVL7hkWLFkmS8ufPr/nz52vjxo2aMGGCPDw85ODgYARssXny5Il69uypwMBAubu769tvv9WaNWs0ZswYpU2bViEhIerXr1+Mp/3t379fbdq00Zo1azRy5EgjAFm5cqVNF6YpVaqUJOnkyZO6e/eu9uzZo7CwMEmxV3HMnj1b4eHhypEjh+bNm6cNGzaof//+kiJOMdy8ebMkaf78+SpWrJhxPz8/P82fPz/aeCEhIZo6daqWLVumPn36xDrX/Pnzq2fPnpKke/fu6b///a8GDhwoKeI99sMPP8Rb77adO3calQnvvfeeihUrpi5duujJkydydnbWgAEDjPf5xYsX9ffff0uSmjVrpnXr1mn58uXGKZ0XL17U+fPn5ejoaFS1RG4f+ViRDh06ZJwiG1khU7BgQWXNmjXGedrjc2craz+nkRVGmTJlkp+fn3788UdJr/+5AwAgOSEmJyZPSjG5s7OzvvvuO0kRBSSDBw+OtcXJ67yujx8/1siRI7V69WoNHz5cDg7/S9k8evRIKVOm1Ny5czV79myjFYm/v788PT01c+ZMLVy40IjXjx49qitXrkiS9uzZo1OnTkmSunTpog0bNmjRokVGwn337t02VeVHmj9/vvz8/IyfyLY+kpQzZ061bNlSkjRkyBDdunVLrq6uGjlypNavX6/Ro0fL3d1dZ86c0cSJE437DRs2zEhUd+zYUatWrdKECRMUGhpq8/wAvJmorEaSExlcSbb1SHvy5IlRrZovXz6NGjXKCA4mTpyo6tWr69atW5o1a5bFH+FIQ4cONSpcv/jiC/Xo0UOSjKPHmTJlsuhTlyZNmmi9iK0VWW1w69Yt+fv7q2zZsqpWrZpKliwpLy+vlwZpa9as0Z07dyRJX375pVq1aiUponegs7OzvvzySz179kwLFizQV199ZXHfvHnzGsForly5tGrVKm3btk0hISF68OCB0qdPb9VziAyMzWazdu7cqQMHDkiK6MecOXPmGO8zffp03blzR6GhocqYMaOePXtmUW3w4MEDSREXYXFxcTGWx7afa9eurcqVK1s137Zt2+qff/7R33//rS1bthjLhw8frrfeeuuF9w0LC7OoIoiUIUOGV6oWdnNzU5MmTdS4cWO99957xvKcOXPK39/fuDhgihQpdPPmTeXIkcOoRIjcR9WqVdOiRYv077//6uTJk0qZMqUuXbokSXJ3d1dgYKD279+vDz74wHhtatSo8cLnGCkhP3e2suZzGvmaODo6Wrx3XvdzBwBAckJMTkye1GLyMmXKqFatWlqzZo0OHjxoHKx43uu8rqVKlTLONsidO3e0sb/66isVLVrUmM/69eslRbRCiXwtP/zwQ02fPl1SRNuSbNmyqUyZMvL399fFixeVO3dumUwmXb16VVmyZNGxY8dkNpv18OFDeXh4vHQ/RBX14pd//fWXfv31V0kRn42pU6fKy8tLDx8+1LZt24y5lS5dWpJUokQJVatWTcuXL9eyZcvUr18/3bx506jsL1mypHFwIk+ePHrw4IG++eYbm+YH4M1EshpJTurUqeXg4KDw8HAjSIiJ2Wy2CCAvXLigp0+fSor4wx/1KLa7u7uKFCmiDRs26ObNm7p79260Uxnz5s1r/B61euR1jwDHFNz37t1bHTt21J07dzRixAhJUsqUKVW8eHHVrl1btWvXtpj/86L2SStTpozFusiKFUnG0feooj5P6dWf69tvv21ctGXHjh3y9/eX9OLeeJJ04sQJ/fXXX/L399eFCxcsKkeinopojTx58li9rclk0vDhw1W7dm3dv39fktSoUSNVqVLlpfe9fv26qlatGm35pk2bYq1UjlSsWDGNGjVKN27c0Lhx47Rnzx4FBQXpwYMHMQawQUFB2rlzp3bs2KEjR44YfaojRf7HsWzZsvLw8NCTJ0/0999/G69jnjx59O6772r9+vXatWuXHj58aLyusbUAkez3uXt+7Jd5nc/p637uAABITojJicmt9SbE5JH69esnPz8/PXnyRGPGjFGdOnWibfM6r+vL9sU777xj/B41sRz1/RB1edTX5cGDB/Lz8zPOXLh7967F2FEPMNnq8OHD6t27t8LDw+Xs7KyffvrJuHDpxYsXjffEqlWrtGrVqmj3v3fvngICAiwOJnzwwQcW2zx/G0DSxf+qkeS4uLgY1aZXr16N8eh5SEiIfHx85Ovrq2XLlkmyvGBdTFUQUQPU59c7OjpaVA28TsLq+eDu2bNn0bZ5//33tXHjRg0bNkw1atRQ+vTp9ejRI23ZskW9evUyKkhiE7Vy4EUVHzGti3oxv+fHslVkELxx40adO3fOYllMvv32W3Xo0EELFy5UlixZ9NVXX2nOnDmv/Pi29i28fPmyxQVAdu7cadXFSF6Hi4uLsmTJoqJFi2rq1Kl69913JUkrVqzQoEGDLLa9deuW6tatq8GDB+vAgQOqUqWKRo4cafSefn7cSpUqSYo4JTPyojplypQxguhdu3YZLUAik9gvmmdCf+6s+axE9bqf09f93AEAkJwQkxOTW+tNiMkjZcyYUV9++aWkiATrwoULo23zOq/ry/ZF1Nc96vvbzc3theOeOXNGtWrV0qhRo3T27FnVrVtX48aN08cff/zCx7NGQECAOnXqZBxkGjJkiMX7J+pn+kXu3r1rMffnP4Ove/0aAG8OktVIkurXry8pIpiN6YrQCxcu1LVr17Rhwwajl1zWrFmN4Hbnzp0WfxwDAwN16NAhSRGnr9nad+9logbVjx8/tnjcyIqBSKGhoTp16pS2bdum7Nmza8KECfrnn3+0ceNG48j9unXrolXURhX1NL2ofYolyyuB58+f/5Wej7UiT1WL+hwjlz3vypUrRo+7Fi1aaMqUKfr0009jTaBGDXRiq7i1NnCSIl6XPn36KCwszBj7+vXrRp+8F8maNatOnToV7cfaCo5I7u7uGj16tDHvhQsXGv0XJWnu3Lm6du2aJGnq1KkaNGiQGjRoEO0/M5EiK6UPHjxoXFyxbNmyKlu2rCTp+PHjxsUVq1ev/tL5JdTnLnJ7s9ls8XmJerGZ1xH5+kZ938TF5w4AgOSGmJyYPCnG5G3atDFek8gEbVSv87rasi9s8euvvxrtWRYvXqz//ve/qlWr1mu3sXv8+LE+//xz3b59W5LUuXNnNWzY0GKb7NmzG7+3aNHCYt+vXLlSfn5+OnXqlAoXLqxs2bIZ2x45csRinMgWNQCSPpLVSJJatGhhXFBlxowZGjhwoI4dO6YzZ85o6tSpxhWyHR0d1aVLF0mSp6enatasKSni1K2+ffvq+PHjOnjwoLp27WpUg7Ru3TrO55sxY0bj98WLF+v+/ft6+PChhgwZEu10rGfPnql169bq3r27unXrpo0bN+rKlSu6ceOGUVFgMpleeOS5Zs2axulhP/74o+bNm6dz585p9erVRrVuihQp1KxZs7h+qhYi+5VFyp49e6wXt4l6wY89e/boyJEjOnTokHr16mUsj3rKY4oUKYzf9+7dq8OHD7/WXIcOHWpcoGTAgAHGaWirV682KoESQsGCBS36Mw4aNMj4z1TUfbRixQpdvHhRq1evNnrHSZan91WqVEkpUqRQaGioHjx4ICcnJ5UoUULZsmVT9uzZFRYWZvynxZpkdUJ97qL2I5w+fbrCw8N14sQJ/fLLL1bswZeLfO/cv39fJ06c0PHjx+PkcwcAQHJDTE5MnhRjckdHR3333XexJnoTy+saVdTXbdGiRbp48aLmzp2rJUuWGMttbQMSGhqqbt266cyZM5KkKlWqqHnz5vr333+Nn7t378rT09No07JkyRLNnz9fFy5c0Nq1a9W8eXNVqlRJjRs3ltlsVtq0aVWiRAlJEe+vkSNH6vTp01q3bp3Gjh37ursBwBuCntVIklKkSKGpU6eqY8eOOn36tObOnau5c+dabBMZZETtfTVgwACdPHlSp0+f1ooVK7RixQqL+9SoUUOffvppnM/3gw8+MHrFnThxQqVLl5bZbJaHh4dy586ts2fPGtt6eHjom2++Ud++fXX37l0jsI/q448/fuFFVVKnTq3Ro0ere/fuCgwMNK5sHcnZ2VkjR46M9aIqcSVTpkzKli2bEXC+6HTD3LlzK1euXDp37pzOnj2rJk2aRNsm8oi+FFGpEHnRlTZt2sjNzU0HDx58pXmuXbvWuNBPmTJl1KpVK5UpU0YNGjRQcHCwhgwZomLFillUAsSnzp07a9OmTTp16pRu3rypcePG6ZtvvlG1atU0c+ZMhYeHa9asWZo1a1a0+0Y9BdfDw0Nly5Y19lPhwoWNCw6VLVtWly9fliTlyJHDqoqehPrc1alTxwisf/rpJ02cOFFms1lFihRRQEDAS+f5Mvnz59fp06cVGBioBg0aKHfu3Fq9evVrf+4AAEhuiMmJyZNqTF60aFE1bNjQItkbKbG8rlFVr15dGzZskCSNGTNGY8aMibbNrVu3lCNHDqvHPHDggHERd0nasmWLxUUvpYj30qxZs9S7d2/5+/vr/v37+vbbby22cXV1Ve/evY3k/4ABA9SyZUs9efJEv//+u37//XdJEf8nCQkJsTjrAUDSRGU1kqy3335bixcvVv/+/eXt7S1PT085OTnp7bffVr169bRo0SI1b97c4j6pU6fW4sWL1adPHxUsWFDu7u5yc3PTBx98oKFDh+rHH3+Ml8pJFxcX/f7776pSpYo8PDzk6emp6tWra+HChRankUWqV6+e5syZo+rVqytz5sxydnaWh4eHihQposGDB1t1lWQfHx8tX75czZo1U5YsWeTs7Ky0adPqo48+0sKFC/XRRx/F+fOMSdRTDF8UGDs5OWnatGmqWbOm0qZNK3d3d+XLl09ff/21ESTv37/fOL3tk08+UbVq1eTl5SV3d3flzp07xtP0XubGjRtGgOnh4aFhw4bJZDIpV65c8vX1lRRx+lvv3r1f66IktnB2dtbw4cON0wT//PNPHT16VMWLF9eECRNUsGBBubm5KX369Cpfvrxmz55tVHds3rzZYqyoF02MbP8hWV785UUXVnxeQnzuypUrp1GjRilv3rxycXFR5syZ1aVLF/38889Wz/NFunbtqnLlysnT01Oenp7GfyTi4nMHAEByQ0z+YsTk1kmMMXnv3r3l5eUV47rE8rpGqlevnoYMGaLcuXMrRYoUypgxo6pVq6bp06cbSeLn/5/wMtZc3DxSrly5tGjRIjVq1EiZMmWSs7OzMmTIoBo1aujPP/+0qO7Pnz+/5s+fr8qVK8vd3V1eXl5q1KiR/vzzz1jbGwJIWkxmW75hAAAAAAAAAACIB1RWAwAAAAAAAADsjmQ1AAAAAAAAAMDuSFYDAAAAAAAAAOyOZDUAAAAAAAAAwO5IVgMAAAAAAAAA7I5kNQAAAAAAAADA7pzsPYHX4ebta+8pAEiC7u2daO8pAEhiXBNJxBXfsVOQP9+fyQVxOIC4RgwOIK4llhhcit/YKanF4FRWAwAAAAAAAADsLhEdYwAAAEC8MlGnAAAAACQ44nCrsacAAAAAAAAAAHZHZTUAAEByYTLZewYAAABA8kMcbjUqqwEAAAAAAAAAdkdlNQAAQHJBrzwAAAAg4RGHW409BQAAAAAAAACwOyqrAQAAkgt65QEAAAAJjzjcalRWAwAAAAAAAADsjspqAACA5IJeeQAAAEDCIw63GnsKAAAAAAAAAGB3VFYDAAAkF/TKAwAAABIecbjVSFYDAAAkF5x+CAAAACQ84nCrsacAAAAAAAAAAHZHZTUAAEBywemHAAAAQMIjDrcaldUAAAAAAAAAALujshoAACC5oFceAAAAkPCIw63GngIAAAAAAAAA2B2V1QAAAMkFvfIAAACAhEccbjUqqwEAAAAAAAAAdkdlNQAAQHJBrzwAAAAg4RGHW409BQAAAAAAAACwOyqrAQAAkgt65QEAAAAJjzjcalRWAwAAAAAAAADsjspqAACA5IJeeQAAAEDCIw63GslqAACA5IIgGQAAAEh4xOFWY08BAAAAAAAAAOyOymoAAIDkwoELuwAAAAAJjjjcalRWAwAAAAAAAADsjspqAACA5IJeeQAAAEDCIw63GnsKAAAAAAAAAGB3VFYDAAAkFyZ65QEAAAAJjjjcalRWAwAAAAAAAADsjspqAACA5IJeeQAAAEDCIw63GnsKAAAAAAAAAGB3VFYDAAAkF/TKAwAAABIecbjVqKwGAAAAAAAAANgdldUAAADJBb3yAAAAgIRHHG41ktUAAADJBacfAgAAAAmPONxqpPUBAAAAAAAAAHZHZTUAAEBywemHAAAAQMIjDrcaewoAAAAAAAAAYHdUVgMAACQX9MoDAAAAEh5xuNWorAYAAAAAAAAA2B2V1QAAAMkFvfIAAACAhEccbjX2FAAAAAAAAADA7qisBgAASC7olQcAAAAkPOJwq1FZDQAAAAAAAACwOyqrAQAAkgt65QEAAAAJjzjcauwpAAAAAAAAAIDdUVkNAACQXFDRAQAAACQ84nCrkawGAABILriwCwAAAJDwiMOtRlofAAAAAAAAAGB3VFYDAAAkF5x+CAAAACQ84nCrsacAAAAAAAAAAHZHZTUAAEByQa88AAAAIOERh1uNymoAAAAAAAAAgN1RWQ0AAJBc0CsPAAAASHjE4VZjTwEAAAAAAAAA7I7KagAAgOSCXnkAAABAwiMOtxqV1QAAAAAAAAAAu6OyGgAAIJkwUdEBAAAAJDjicOtRWQ0AAAAAAAAAsDsqqwEAAJIJKjoAAACAhEccbj2S1QAAAMkFMTIAAACQ8IjDrUYbEAAAAAAAAACA3VFZDQAAkExw+iEAAACQ8IjDrUdlNQAAAAAAAADA7qisBgAASCao6AAAAAASHnG49aisBgAAAAAAAADYHZXVAAAAyQQVHQAAAEDCIw63HpXVAAAAAAAAAAC7o7IaAAAgmaCiAwAAAEh4xOHWo7IaAAAAAAAAAGB3VFYDAAAkFxR0AAAAAAmPONxqVFYDAAAAAAAAAOyOZDUAAEAyYTKZ4vXnRY4dO6ZWrVqpePHiKl++vIYOHarg4GBJ0qFDh9S0aVN5e3vLx8dHCxcutLjv0qVLVa1aNRUpUkSNGjWSv79/vO0jAAAAIK7ZKwZ/E5GsBgAASCbslawODw/X559/rho1amjPnj1atGiRtm/frmnTpunBgwfq2LGjGjRooL1792rYsGEaPny4Dh8+LEnavXu3hgwZohEjRmjv3r2qV6+eOnXqpKCgoITabQAAAMBrIVltPZLVAAAAiFcPHjzQrVu3FB4eLrPZLElycHCQm5ub/vrrL3l5ealVq1ZycnJSmTJlVLduXc2ZM0eStHDhQtWuXVvFihWTs7Oz2rVrpzRp0mjNmjX2fEoAAAAA4gHJagAAgGTCXpXVadKkUbt27TRy5EgVLlxYlSpVUs6cOdWuXTudOXNGefPmtdg+d+7cOnnypCTp7NmzL1wPAAAAJHZUVluPZDUAAADiVXh4uFxdXfXNN9/o4MGDWrVqlc6dO6cJEyboyZMncnNzs9je1dVVgYGBkvTS9QAAAACSDpLVAAAAyYS9Kqs3bNig9evXq2XLlnJxcVGePHnUpUsXzZ07V25ubnr69KnF9k+fPpWHh4ckvXQ9AAAAkNhRWW09ktUAAACIV9evX1dwcLDFMicnJzk7Oytv3rw6c+aMxbqzZ88qT548kqQ8efK8cD0AAACApINkNQAAQHJhiuefWJQvX163bt3SlClTFBYWpitXrujnn39W3bp1Va1aNd2+fVvTp09XSEiIdu3apZUrV6px48aSpCZNmmjlypXatWuXQkJCNH36dN25c0fVqlWL670DAAAAxA87xOBvKid7TwAAAABJW+7cuTV16lSNHz9ev/76q1KmTKl69eqpS5cucnFx0e+//65hw4ZpwoQJSps2rQYMGKDSpUtLksqUKaPvvvtOAwcO1I0bN5Q7d25NmzZNXl5e9n1SAAAAAOIcyWoAAIBkwp497cqWLauyZcvGuK5w4cKaN29erPetX7++6tevH19TAwAAAOJVUuwtHV9oAwIAAAAAAAAAsDsqqwEAAJIJKjoAAACAhEccbj0qqwEAAAAAAAAAdkdlNQAAQDJBRQcAAACQ8IjDrUdlNQAAQHJhiucfAAAAANHZKQa/c+eOOnfurOLFi6tUqVIaNmyYQkNDY9x2xowZ8vHxUdGiRVW3bl2tX7/+tZ7yqyJZDQAAAAAAAABJTPfu3eXu7q5t27Zp0aJF2rlzp6ZPnx5tOz8/P02dOlW//vqrDhw4IF9fX3Xv3l1Xr15N8DmTrAYAAEgmTCZTvP4AAAAAiM4eMfilS5e0Z88e9e7dW25ubsqWLZs6d+6sOXPmRNv2/PnzMpvNxo+jo6OcnZ3l5JTwHaTpWQ0AAAAAAAAASciZM2fk5eWljBkzGsty5cqla9eu6eHDh0qVKpWxvHbt2lqyZIlq1aolR0dHmUwmjRo1SpkyZUrweZOsBgAASCaofgYAAAASnj3i8CdPnsjNzc1iWeTtwMBAi2R1SEiI8ufPr2HDhil//vxauXKl+vfvr1y5cilfvnwJOm/agAAAAAAAAABAEuLu7q6goCCLZZG3PTw8LJYPGTJEefLk0fvvvy8XFxc1btxYRYoU0dKlSxNsvpFIVgMAACQT9KwGAAAAEp49YvA8efLo/v37un37trHs3LlzypQpk1KmTGmx7bVr1xQcHGyxzMnJSc7OznG7I6xAshoAAAAAAAAAkpCcOXOqWLFi+v777/X48WNduXJFkydPVpMmTaJt6+Pjo9mzZ+vYsWMKDw/XunXrtHv3btWqVSvB503PagAAgGSC6mcAAAAg4dkrDp8wYYIGDx6sqlWrysHBQQ0aNFDnzp0lSd7e3ho0aJDq1asnX19fOTo6qmvXrnrw4IFy5MihSZMm6b333kvwOZOsBgAAAAAAAIAkJn369JowYUKM6/z9/Y3fnZyc1LVrV3Xt2jWhphYrktUAAADJBYXVAAAAQMIjDrcaPasBAAAAAAAAAHZHZTUAAEAyQc9qAAAAIOERh1uPZDUAAEAyQZAMAAAAJDzicOvRBgQAAAAAAAAAYHdUVgMAACQTVHQAAAAACY843HpUVgMAAAAAAAAA7I7KagAAgOSCgg4AAAAg4RGHWy3RJKv37dungIAAmc1mi+UNGjSwz4QAAACAJI4YHAAAAIlJokhWf/fdd1q0aJHeeustix4uJpOJQBkAACCO0CsPURGDAwAAJAzicOslimT1mjVrNH/+fBUqVMjeUwEAAACSBWJwAAAAJDaJIlmdMmVK5c2b197TAAAASNKo6EBUxOAAAAAJgzjceokiWd2pUyf1799fHTp0UKpUqSzWZc6c2U6zAgAAAJIuYnAAAAAkNokiWf3s2TOtWbNGq1atMpaZzWaZTCadOHHCjjNDYpc3Z0aN7t1EJQrn0KPHT/Xr4n806ve/ZDabVShPZv3Qs7GKF8qhwKfBmr92n/47fpnCwsItxviytY9qVyqsGp/9aKdnAeBNsm7tGv23by+5uKQwlvl8+KG+HzHKjrMCrENFB6IiBkdUL4qra5QvoIFd6ipXtgy6EHBbw6as0Yoth186ZruGZfTzt63k5u1rLMuZJZ3G9WumkoVzKjQsXBt2HFfPkYv04HGQHBxM+mVQa9Wt/L6u33qgzoP/1I6D5437zR/7mSq0Hq3gkNB42w8A4s+dO3c0ZOA32rd3jxwdHVW7Tj191buvnJyip6Y6f/4f7d2zW46O/1s3ZtyPKlehop49e6Yfx47Whg3rFfjkiXK+86669eipkqVKS5LmzJ6pX36eJEdHJ33RxVfNmn8sSQoJCVHrj5tp5KgxyvnOuwnzpIH/RxxuvUSRrJ48ebIGDBig8uXLy8HBwd7TwRvCw81FKyd30cadJ9Si5zSlT+OhReO/kJOjg6Yu+FtrpnbVhNlbVM93kjJn8NKqn311/eYDjZ+1SZLk7uqibzvXVrc2VfX3vjN2fjYA3hTHjh5R7br1NWTYcHtPBQBeCzE4Ir0orl7z9xEtGNtR3YbP16wVu1X6/Xe0ZMIXutf9F23bH3sM/d67mfRDz8bRls8c3l7bD5xV0x5TldLdVfPHfqYRPRuq06A/Va3seypbJJfyfvStWtUtqeE9GqpS2zGSpDF9murrsctIVANvsD49u+utjBm1Ycs23bl9W1/6dtLsmdPV7tP/RNv2+LGj+vmX31S8RMlo634cO1oH/Q9o1pz5yvDWW1q2ZLG6dvlCy1asUarUqTTmhxGav2ipzGazWjRtpDp168vd3V1zZs1Q+QoVSVQDiVyiiErDwsL08ccfK1u2bMqSJYvFDxCbst65lCGNp7oPX6DAp8G6fP2efvhtvT5rWkGt65bS2Us3Nfr3vxQaGq7L1++qTqeftHjDAeP+e+Z/rUzpU2vqgr/t+CwAvGmOHT2iggW5GBneTCaTKV5/8GYhBkekF8XVTaoX0w7/c5q+dKfCwsL1j/85zVu7Tx2blo91PDdXZ80c8akm/bk12rp872SUg4NJDiYHmUwmhYebFfQ0RJIUGhpxBmTk10lYeMTtupXfV2DQM23efTJunziABHP50iXt27tHPXr2lpubm7Jmy6aOX3TWvD/nRNv26tUrevDggd4rUCDGsZ4+e6bOXb9UprfflqOjoxo3bSYXZxcdP35MDg6OkiLOFDKb/xf73Pj3X61csVyffd4pXp8nEBticOslimR1o0aNNHPmTHtPA28YRwcHBYeEKSQ0zFgWHm5WpvSpVLX0ezp29rom9G+hCxu+17EV36lFrZK6euO+sW2Nz35Uu/9O1627j+0wewBvovDwcJ04fkzb/t6qmh9WUTWfihr83Td6+OCBvacGWMcUzz94oxCDI9KL4uo0qd31JCjYYvvwcLPyvpMx1vHG92uutduOavPuU9HWDZu6Vp1aVNKdHWMUsHWkXFM4q/+PyyRJm3ef0qZdJ3V4+bf6pH4Z9R61WG6uzvq2cx31GbMkbp4sALs4d+6MUqf20ltv/e+7I1euXLp+/ZoePnxose2xo0fk4eGhPj17qHL50mpUv46WLllkrP924GCVr1DJuL171049fvxI+fLnl5ubm74e8K18O3+ubr6d9N2goXJzc9Ookd+ra7cecnV1jf8nC8SEGNxqiSJZffjwYX3//fcqVqyYfHx8VLVqVeMHiM3Og+cV9CxEQ76sJzdXZ2V/O416tI14zzg4mPRJ/dLad/Si8nw0QC16/ar/NC6nbq19jPsH3Lxvp5kDeFPdu3tX+d8roA+r19DSlWs0c/Y8Xbp0Uf/t19veUwMAmxGDI9KL4upZK3bpwzL51aBqETk6OqjMB++qaY1ickvhEuNYLWqVUP53MmrQ5FUxrg8PD9fwaeuUsUJv5av1jSRpYv+IfrJms1ldh81Tdp+vVbrFCO0/fln9/lNTM5fvVDovD234rbv+mdNH/2kSe1U3gMTpyZMncnNzs1jm6hpxOygw0GJ5cHCw3v+giHy79dCGLdvUq08//TB8mP5avzbauIcPHVTvr7rri86+ypo1mySpabMW+muTn9Zt3KJ6DRpqxz/bFRoaqqJFi6lXjy/VrFF9fT9kkEJCQuLp2QJ4HYmiZ3WTJk3UpEkTe08Db5gHj4PUwHeyRvZspDNrh+r8lVuas2qPihfKKUcHk/YdvaSZy3dJko6cDtDP8/zUuLq30bMaAGyVLn16/THzf6cqurm5qUfP3mr9cTM9efJYHh6edpwd8HJJ8TRBvDpicER6UVx96NRVdRgwU/0/r6WJAz7WDv+zmrVil8p554o2Tp4cb2nol/VU9dPx0S5qLkne72XTd13qKlPF3goLC9fl68H6euxSbfy9u7qPWKBHT55GG69q6fyq1HaM/Gb01ITZm7Vu+zEdXvatth84q5Pn/423fQIgbrm5uevp0yCLZZG33T08LJbXrddAdes1MG6XLVdedes10Pq1a1W9xkfG8iWLFuqHEd+rs++X+qRd+xgfNzg4WOPHjNL4iZP167QpSpXaS/MX/6jOn/9HS5csMi6+CMQ34nDrJYpkdcOGDWNcHhrKxTMQO2cnRzk5OqhmxwnGss+altfxc9d14PhlVSqR12J7R0cHvhwAvJbTp05qzepV6tajp/F9EhwcLAcHBzk7x1xhBgCJFTE4Ir0ornZ1cdbxc9dVotn3xrpZI9pr//HL0cZp+KG3vFK5a9e8vpIkJ8eI3rHX//5B3b9foKBnwXJ0MMnRwcFIZoeEhslslkLDwqKNN7ZvU/UetVhhYeEqkOttHTh+RQ8fP9WFq7dV4N23SVYDb5DcefLo/v37unP7ttKlTy9JOnfunDJmyqSUKVNabLt0ySJ5eHhYJKaDQ4KVwjWFpIhrLgwbMkibN/6l8T9NUukyZWN93Om//6oaH9VS5sxZdPbMGflUrSaTyaT3ChTU2dOn4+GZAnhdiSJZffnyZU2aNEk3btxQ+P9fRCMkJEQXLlzQrl277Dw7JFYmk0krf/bV1+OWasaynfJ+L5v6dqihYVPX6h//c+r8cWV91fZDjZ+1Se+9+7a+aF5RY2dstPe0AbzBUqf20rw/5yh16tRq07a9bt28qXFjRqleg4ZycSFZjcSPg7aIihgckV4UV+fOnkFrpn4pn/ZjdfzcdTWsWkS1KhZW+dY/RBvnh9/W64ff1hu3KxTLo79+7aa3K/aRJKVP46nApyH6oVcj9R2zRKk8XDW4az0t33zQuMhipCbVi+rqjXvaeei8JOns5Vsq/cE7uvvgiXJnf0vnrtyKxz0CIK7lyJFT3kWL6YcR3+vbQYN17949/TJlsho2in6Gz+NHjzVh/Fhly55D+fLl1/Ztf2vt6lX6+ZffJEmjRg7XP9v+1p8LFitz5tgvChwQcFWbNm7Q7D/nS5Ky58ipQ4cOqn7DRjp65LAqVKwcL88ViAlxuPUSRc/q/v37KyAgQClTplRoaKjy5s2rM2fOqHXr1vaeGhKx4JBQNe0xVR2bVtDN7aM1e+SnGjN9o/5YukOnL95Q9f+M10cVC+nqlpFaMamzpi3arslz/ew9bQBvsIyZMmniz1O1ZfMmVSxbUh83b6yChQrr6/7f2ntqAGAzYnBEelFcvffoJX09bqkWjP1M1/1+UPdPqqpx9yk68f9VzeW8c+nWP2OULVOalz7O7XuPVbfzROXJ8ZbO/zVMu+b105nLN/XFoD8ttvN0T6E+HWpowI/LjWU9RixQ3//U0KGl3+jneX46dOpq3O4EAPFuzLgJCgsLVa3qVdX642YqV76COn7RWZJUuri3Vq9aIUlq/Ulbfdyytb760ldlSnhr/NjRGvr9SBUtVlz37t3V/LlzdPv2bTWqV0eli3sbP5H3jzTy+6H6qlcfOf9/UUmHzzrq6pXLqly+tDw9U6pp8xYJuwMAWMVkNpvN9p6Et7e3tm7dqmvXrmn8+PGaOnWq/v77b02dOlVz5syJ9X5u3r4JOEsAycW9vRPtPQUASYxrojiXTcrdK/qFieLS2dEfvXwjJBqvGoNLxOEA4h4xOIC4llhicCl+4/CkFoMnispqNzc3pU6dWtmzZ9fp/+8ZVLFiRZ0/f97OMwMAAACSJmJwAAAAJDaJIlmdPXt2+fn5ycPDQ+Hh4bpy5Ypu3LjBxV0AAADikMlkitcfvFmIwQEAABIGMbj1EkVBfMeOHdW1a1etXr1azZs3V4sWLeTo6CgfHx97Tw0AAABIkojBAQAAkNgkimS1j4+PNmzYoHTp0qlz587KmTOnnjx5ogYNGth7agAAAElGEiy8wGsgBgcAAEgYxOHWs2uy2sfH54Xl6lOmTNGmTZsScEYAAABA0kYMDgAAgMTKrsnqrl27SpLMZrMGDx6s7777zp7TAQAASNKSYk872I4YHAAAIGERh1vPrsnqhg0bGr+PGDHC4jYAAADiFjEyJGJwAACAhEYcbj0He08AAAAAAAAAAIBEcYFFAAAAxD8HB0o6AAAAgIRGHG49KqsBAAAAAAAAAHZn18rqZcuWGb+HhIRY3I7UoEGDBJsPAABAUkavPEjE4AAAAAmNONx6dk1WT5gwwfg9TZo0FreliCtlEigDAAAAcYcYHAAAAImVXZPVmzdvtufDAwAAJCsmSjogYnAAAICERhxuPXpWAwAAAAAAAADszq6V1QAAAEg4FHQAAAAACY843HpUVgMAAAAAAAAA7I7KagAAgGSCXnkAAABAwiMOtx6V1QAAAAAAAAAAu6OyGgAAIJmgogMAAABIeMTh1iNZDQAAkEwQIwMAAAAJjzjcerQBAQAAAAAAAADYHZXVAAAAyQSnHwIAAAAJjzjcelRWAwAAAAAAAADsjspqAACAZIKCDgAAACDhEYdbj8pqAAAAAAAAAIDdUVkNAACQTNArDwAAAEh4xOHWo7IaAAAAAAAAAGB3VFYDAAAkExR0AAAAAAmPONx6VFYDAAAAAAAAAOyOymoAAIBkgl55AAAAQMIjDrceldUAAAAAAAAAALujshoAACCZoKADAAAASHjE4dYjWQ0AAJBMcPohAAAAkPCIw61HGxAAAAAAAAAAgN1RWQ0AAJBMUNABAAAAJDzicOtRWQ0AAAAAAAAAsDsqqwEAAJIJeuUBAAAACY843HpUVgMAAAAAAAAA7I7KagAAgGSCgg4AAAAg4RGHW4/KagAAAAAAAACA3VFZDQAAkEzQKw8AAABIeMTh1qOyGgAAAAAAAABgd1RWAwAAJBMUdAAAAAAJjzjcelRWAwAAAAAAAADsjspqAACAZIJeeQAAAEDCIw63HslqAACAZIIgGQAAAEh4xOHWow0IAAAAAAAAAMDuqKwGAABIJijoAAAAABIecbj1qKwGAAAAAAAAANgdldUAAADJBL3yAAAAgIRHHG49KqsBAAAAAAAAAHZHZTUAAEAyQUEHAAAAkPCIw61HZTUAAADi3f3799WnTx+VKlVKJUqUUOfOnXXz5k1J0qFDh9S0aVN5e3vLx8dHCxcutLjv0qVLVa1aNRUpUkSNGjWSv7+/PZ4CAAAAgHhGshoAACCZMJlM8frzIl27dlVgYKA2bNigLVu2yNHRUd98840ePHigjh07qkGDBtq7d6+GDRum4cOH6/Dhw5Kk3bt3a8iQIRoxYoT27t2revXqqVOnTgoKCkqIXQYAAAC8NnvF4G8iktUAAACIV0ePHtWhQ4c0YsQIpUqVSp6enhoyZIh69eqlv/76S15eXmrVqpWcnJxUpkwZ1a1bV3PmzJEkLVy4ULVr11axYsXk7Oysdu3aKU2aNFqzZo2dnxUAAACAuEayGgAAIJkwmeL3JzaHDx9W7ty5tWDBAlWrVk3ly5fXyJEjlSFDBp05c0Z58+a12D537tw6efKkJOns2bMvXA8AAAAkdvaIwd9UXGARAAAgmXCwUzT74MEDnTp1SoUKFdLSpUv19OlT9enTR3379lX69Onl5uZmsb2rq6sCAwMlSU+ePHnhegAAACCxs1cc/iaishoAAADxysXFRZLUv39/eXp6Kn369Orevbv8/PxkNpv19OlTi+2fPn0qDw8PSZKbm9sL1wMAAABIOkhWAwAAJBP2agOSO3duhYeHKyQkxFgWHh4uSXrvvfd05swZi+3Pnj2rPHnySJLy5MnzwvUAAABAYkcbEOuRrAYAAEC8Klu2rLJly6b//ve/evLkie7evatx48bpww8/VJ06dXT79m1Nnz5dISEh2rVrl1auXKnGjRtLkpo0aaKVK1dq165dCgkJ0fTp03Xnzh1Vq1bNzs8KAAAAQFyjZzUAAEAyYbJT6YWzs7NmzZqlESNGqEaNGnr27Jl8fHzUv39/pUqVSr///ruGDRumCRMmKG3atBowYIBKly4tSSpTpoy+++47DRw4UDdu3FDu3Lk1bdo0eXl52eW5AAAAALayVxz+JiJZDQAAgHiXMWNGjRs3LsZ1hQsX1rx582K9b/369VW/fv34mhoAAACARIJkNQAAQDLhQEEHAAAAkOCIw61Hz2oAAAAAAAAASGLu3Lmjzp07q3jx4ipVqpSGDRum0NDQGLfds2ePmjZtKm9vb1WqVElTp05N4NlGIFkNAACQTJhMpnj9AQAAABCdvWLw7t27y93dXdu2bdOiRYu0c+dOTZ8+Pdp2586dU8eOHdWyZUsdOHBAU6dO1e+//65169bF0x6JHclqAAAAAAAAAEhCLl26pD179qh3795yc3NTtmzZ1LlzZ82ZMyfatn/++aeqVq2qhg0bymQyKX/+/Jo3b56KFSuW4PMmWQ0AAJBMmEzx+wMAAAAgOnvE4GfOnJGXl5cyZsxoLMuVK5euXbumhw8fWmx7+PBhZc2aVV999ZVKlSqljz76SHv27FGGDBnia5fEimQ1AAAAAAAAACQhT548kZubm8WyyNuBgYEWyx88eKCZM2eqXr16+ueffzR48GCNHDmSNiAAAACIP6Z4/gcAAAAgOnvE4O7u7goKCrJYFnnbw8PDYrmLi4uqVq2qypUry8nJSSVKlFD9+vW1du3auN8ZL+GU4I8IAAAAu3AgnwwAAAAkOHvE4Xny5NH9+/d1+/ZtpU+fXlLEhRQzZcqklClTWmybK1cuBQcHWywLCwuT2WxOsPlGorIaAAAAAAAAAJKQnDlzqlixYvr+++/1+PFjXblyRZMnT1aTJk2ibduiRQtt2rRJy5cvl9ls1t69e7Vy5UrVr18/wedNshoAACCZMJlM8foDAAAAIDp7xeATJkxQaGioqlatqmbNmqlChQrq3LmzJMnb21srVqyQJJUpU0aTJ0/WzJkzVaxYMX399dfq27evqlatGu/75nm0AQEAAAAAAACAJCZ9+vSaMGFCjOv8/f0tbleqVEmVKlVKiGm9EMlqAACAZILiZwAAACDhEYdbjzYgAAAAAAAAAAC7o7IaAAAgmXCgpAMAAABIcMTh1qOyGgAAAAAAAABgd1ZVVl+7ds2mQTNnzvxKkwEAAED8oaDjzUIMDgAAkDQQh1vPqmS1j4+PTDbs1RMnTrzyhAAAAAAQgwMAACD5sSpZ/f3339sUKAMAACDxIZ57sxCDAwAAJA3EdNazKlndqFGj+J4HAAAAgCiIwQEAAJDcWJWsfl5wcLAWLVqkHTt26NatW/r++++1Z88eFSxYUO+//35czxEAAABxgIKONxsxOAAAwJuJONx6Drbe4e7du2rcuLGGDRumS5cu6fDhw3r69Kn8/PzUpk0b+fv7x8c8AQAA8JocTKZ4/UH8IQYHAAB4cxGDW8/mZPUPP/ygJ0+eaM2aNVq6dKnMZrMk6ccff1ThwoU1YcKEOJ8kAAAAkJwRgwMAACAxu3v3rqZPn65hw4bp8ePH2rJlyyuNY3OyesuWLerWrZty5Mhh0Rw8RYoU+vTTT3Xs2LFXmggAAADilymefxB/iMEBAADeXEk9Bj927Jhq1qypdevWadGiRbp37566deumxYsX2zyWzcnqZ8+eycvLK8Z1jo6OCgkJsXkSAAAAAGJHDA4AAIDEavjw4erXr5/mzZsnJycnZcuWTZMmTdJvv/1m81g2J6sLFy6sP//8M8Z1K1euVKFChWyeBAAAAOKfyWSK1x/EH2JwAACAN1dSj8FPnz6t+vXrS5IxpwoVKujGjRs2j+Vk6x26deumdu3aqX79+qpUqZJMJpNWrVqln376Sdu3b9evv/5q8yQAAAAAxI4YHAAAAIlV2rRpdf78eeXJk8dYdv78eaVPn97msWyurC5evLj++OMPubm56ddff5XZbNb06dN169YtTZ06VaVLl7Z5EgAAAIh/Dqb4/UH8IQYHAAB4cyX1GLxly5b6/PPPtWDBAoWGhmrNmjXq1q2bmjdvbvNYNldWS1KJEiU0b948PX36VA8ePJCnp6c8PDxeZSgAAAAAViAGBwAAQGL0ySefyNHRUTNmzFB4eLh+/PFHNW/eXO3atbN5rFdKVkvSjh07tGPHDj18+FDp0qVTqVKlqOgAAABIxBJLTzu8OmJwAACAN09yiMNbtWqlVq1avfY4Nier7969K19fXx04cEBOTk7y8vLS/fv3NWXKFJUrV04TJ06Uq6vra08MAAAAQARicAAAACRWX3/9dYzLnZ2dlTZtWlWuXFlFihSxaiybe1b/8MMPOn/+vCZNmqQjR45o+/btOnz4sMaMGaNDhw5p9OjRtg4JAACABGAyxe8P4g8xOAAAwJsrqcfgzs7OWrlypYKCgpQ+fXoFBwdr1apVunHjhs6fP6/27dtrzZo1Vo1lc2X15s2b1adPH1WtWtVY5uDgoFq1aun+/fuaMGGCBgwYYOuwAAAAAGJBDA4AAIDE6vr16xo/frw+/PBDY5mfn5/mzp2rCRMmaPfu3Ro6dKhq1ar10rFsrqyWpHTp0sW4/J133lFwcPCrDAkAAIB4ZjKZ4vUH8YsYHAAA4M2U1GPwQ4cOycfHx2JZhQoVtG/fPklSqVKlFBAQYNVYNier69Wrp19++UVBQUEWy8PDwzV79mzVqVPH1iEBAACQABxM8fuD+EMMDgAA8OZK6jF42rRptW3bNotlO3fulJeXlyTpypUrSp06tVVjWdUGJGqT7NDQUB0+fFhVq1ZVpUqVlD59ej148EA7d+7U7du31axZMyufBgAAAIDYEIMDAADgTdC1a1f5+vqqevXqypo1qwICArRx40YNHDhQ58+fV9u2bdW6dWurxjKZzWbzyzZ6voz7hQOaTNq0aZPV278ON2/fBHkcAMnLvb0T7T0FAEmMq81XCYkf7ecdidfx/2hROF7HT24SawwuEYcDiHvE4ADiWmKJwaX4jcMTSwzu7++vJUuW6Pr168qcObOaNWumQoUK6eLFizp79qxFP+sXsepl27x582tNFgAAAEgsHj9+LE9PT3tP46WIwQEAAPCm8Pb2lre3t8Wy0NBQ5cyZUzlz5rR6nDg/xnDu3DnlypUrrocFAADAa0okLe0STMmSJbVnz55oyytXrmxc7CWpIAYHAABIvJJ6HH758mVNmjRJN27cUHh4uCQpJCREFy5c0K5du2way+Zk9f379zV27Fjt3btXISEhiuwiYjabFRgYqAcPHujEiRO2DgsAAAC8tkuXLunbb7+V2WzW48eP9cknn1isf/z4sVKlSmWn2b06YnAAAAAkVv3795fZbFaaNGl0584dFShQQMuWLVO7du1sHsvmZPXw4cO1atUqVaxYUefPn5ebm5ty5syp/fv36+HDhxo8eLDNkwAAAED8czAl9ZoOKUeOHKpevbru3bunAwcOqGTJkhbrXVxcbOoFnVgQgwMAALy5knocfvToUW3dulXXrl3T+PHjNWDAAFWsWFFTp06Vr69t1zqxOVm9bds2+fr6qlOnTvrjjz+0e/dujR8/Xk+ePFHr1q119uxZW4cEAAAA4kyrVq0kSVmzZlWDBg3sO5k4QgwOAACAxMrNzU2pU6eWk5OTTp8+LUmqWLGi+vbta/NYDrbe4eHDhypWrJgkKU+ePDp69KgkycPDQ59++qm2bt1q8yQAAAAQ/0ym+P1JbBo0aKDDhw9r+fLlWrZsmcXPm4YYHAAA4M2V1GPw7Nmzy8/PTx4eHgoPD9eVK1d048YNhYaG2jyWzZXVadKk0aNHjyRFnGZ5584d3bt3T2nSpFHGjBl148YNmycBAAAAxLWxY8dq2rRpypAhg5yc/hf2mkymN67imhgcAAAAiVXHjh315ZdfatWqVWrevLlatGghR0fHV2q/Z3OyukyZMpoyZYry5cunrFmzysvLS0uWLFGHDh20ZcsWpUmTxuZJAAAAIP6ZEkvpRQJZvny5pkyZokqVKtl7Kq+NGBwAAODNldTjcB8fH/31119KmzatOnfurJw5c+rx48dq2LChzWPZ3AakW7duunPnjvr16yeTyaSOHTtq1KhRKlmypKZPn67GjRvbPAkAAAAgrgUGBqpixYr2nkacIAYHAABAYtWpUydlzJhRzs7OkqRatWqpWbNmat++vc1j2VxZnSVLFq1Zs0YXL16UJLVv317p06fXgQMH9P77779SxhwAAADxL4kXdERTuXJlrVy5UvXq1bP3VF4bMTgAAMCbKynG4VevXjWuBbN9+3ZNnDjRYv3jx4916tQpm8e1OVktSa6ursqfP79xu27duqpbt66ePHmia9euKXPmzK8yLAAAAOKRQ1KMkl/g2bNn6tevn6ZMmaL06dNbrJs5c6adZvXqiMEBAADeTEkxDs+cObPOnDmju3fvKiwsTLt377ZYnyJFCn333Xc2j/tKyerYLFq0SCNGjNCJEyficlgAAADAZnnz5lXevHntPY14RwwOAACAhObg4KAff/xRkjRgwAANHTo0TsaN02Q1AAAAEq8kWNDxQr6+vvaeAgAAAJDk4/ChQ4cqODhYd+/eVXh4uMU6W8/+I1kNAACAJOnrr7+Odd3w4cMTcCYAAABA0rVu3Tp98803evz4sbHMbDbLZDLZfPYfyWoAAIBkwpTUSzpe4t69e9q1a5caN25s76kAAAAgGUnqcfiECRPUqlUrNWzYUE5Or5duJlkNAACAJCmm6ukdO3bozz//tMNsAAAAgKTp+vXr8vX1fe1EtWRlsnrv3r1WDXb58uXXmoyt7u6ZmKCPByB5CLgbZO8pAEhicr3lZu8pSJIc7D2BRKBs2bL68ssv7T0NqyTWGFySLm8bn+CPCSBpS1N9mL2nACCJCdrc395TMCT1OLxgwYI6e/as8ufP/9pjWZWsbtOmjVXl6pG9SAAAAIDEJjQ0VKtWrVLatGntPRWrEIMDAADgTVC0aFG1a9dONWvWVPr06S3W2XrRc6uS1TNnzrRpUAAAACQ+yS2hmT9//mjP2dHRUf37J54qmxchBgcAAEgaknoc7u/vrzx58ujcuXM6d+6csfxVnrdVyeqSJUvaPDAAAABgT88nex0cHJQjRw5lyJDBTjOyDTE4AAAA3gSzZs2Ks7GSessUAAAA/D8HU/z+JDYlS5ZU8eLF5erqqtu3b0uS0qVLZ+dZAQAAILlJDjH4uXPnNHToUPn6+urevXuaPXv2K43z+pdoBAAAABKhW7du6YsvvtDJkyfl5eWle/fuKWfOnPr999+VKVMme08PAAAASBL++ecfde3aVVWqVNGOHTv09OlTTZo0SYGBgerYsaNNY1FZDQAAkEwkt8rqkSNHKmfOnNqzZ4/++ecf7d69W++9956GDx9u76kBAAAgGUnqMfjYsWM1btw4jRkzRo6Ojnr77bf1yy+/aP78+TaPRWU1AABAMpHUL+zyvF27dmndunXy8PCQJKVMmVIDBw5U1apV7TwzAAAAJCdJPQ6/dOmSKlasKOl/z7Vw4cJ68OCBzWO9VmX1o0ePdO7cOQUHByssLOx1hgIAAADiVHh4eLT/GJhMJjk7O9tpRnGDGBwAAACJSebMmXXgwAGLZUeOHNHbb79t81ivlKzevXu3mjZtqpIlS6pu3bo6c+aMevbsqREjRrzKcAAAAEgAya0NSKlSpTRw4EAFBgZKkp48eaKBAweqZMmSdp7ZqyEGBwAAeDMl9Rj8888/V6dOnTRu3DiFhIRo2rRp6tKlizp06GDzWDYnq3fu3KkOHTrI1dVVvXr1ktlsliQVKFBAM2fO1B9//GHzJAAAAIC41rt3bx0+fFglS5ZU+fLlVapUKZ05c0b9+vWz99RsRgwOAACAxKp27doaNWqUTpw4ocyZM2vXrl3q37+/GjRoYPNYNvesHj9+vKpWraoff/xRoaGhGjVqlCSpY8eOevz4sRYuXKj27dvbPBEAAADEryTeKs+C2WxWaGioVq9erX379unOnTsKCAhQhw4d5OjoaO/p2YwYHAAA4M2VHOLwrFmzauzYsfL09JS/v79SpUr1SuPYXFl94sQJNW7cWFL05uDlypVTQEDAK00EAAAAiAuBgYH6+OOP9cMPP8jJyUmlS5dW6dKlNXHiRLVp08ZoC/ImIQYHAABAYrV27Vo1aNBAFy9elCQdPHhQTZs2lZ+fn81j2ZysTpkypW7duhXjuuvXrytlypQ2TwIAAADxz8FkitefxOLnn3+Ws7OzBg0aZCxLly6dtmzZotDQUE2dOtWOs3s1xOAAAABvrqQeg0+cOFGTJ09WoUKFJEnt27fXjz/+qDFjxtg8ls3J6qpVq2rcuHE6cuSIscxkMunff//VlClTVLlyZZsnAQAAAMSV9evXa+jQoUqXLp3F8nTp0mnQoEFat26dnWb26ojBAQAAkFhdv35dFSpUsFhWvnx5Xbt2zeaxbO5Z3bNnTx06dEjNmjVT+vTpJUlfffWV/v33X7399tv66quvbJ4EAAAA4p/NVQpvqDt37ihHjhwxrnvvvfdirVBOzIjBAQAA3lxJPQ7PkiWLtm3bZpGw3rlzpzJnzmzzWDYnq1OnTq2FCxdq2bJl2rVrl+7fv6+UKVOqTZs2atSokdzc3GyeBAAAABBXPD09de/ePaVJkybauvv377+R8SoxOAAAABKrjh07qkuXLqpevbqyZMmia9euacOGDRo5cqTNY9mcrJYkFxcXNWvWTM2aNXuVuwMAAMAOEklLu3hXpkwZzZkzR76+vtHW/fnnnypSpEjCTyoOEIMDAAC8mZJ6HF63bl299dZbWrZsmY4dO6a3335bv//+u4oWLWrzWDYnq5ctW/bSbRo0aGDzRAAAAIC48Pnnn6tRo0a6d++eatWqpQwZMujmzZtau3atFi9erNmzZ9t7ijYjBgcAAEBi1alTJ40aNUqlSpV67bFsTlb369cvxuUmk0mOjo5ydHQkUAYAAEiEEsvVwuPbO++8o99++03fffed5syZI5PJJLPZrLx582ratGnGVcrfJMTgAAAAb66kHof7+/vLxcUlTsayOVm9adOmaMsCAwO1f/9+/fLLL5o0aVKcTAwAAABxK4nHyBaKFi2qlStX6sqVK7p7964yZMjwShd4SSyIwQEAAN5cST0Or1Onjr788kvVrVtXGTJkkCnKEy5RooRNY9mcrM6SJUuMy/PkyaOQkBANGTJEf/75p63DAgAAAHEuW7ZsypYtm72n8dqIwQEAAJBYRbbZ27p1q8Vyk8mkEydO2DTWK11gMTZ58+bV6NGj43JIAAAAxBGHJF7RkVwRgwMAACRuST0OP3nyZJyN5RBXAwUHB2vBggVKly5dXA0JAAAA4AWIwQEAAJAYBAcHa8OGDZo+fbqCgoJeOYFtc2W1j4+PRd8RSQoPD9e9e/f07Nkz9e3b95UmAgAAgPiV1C/skpQRgwMAALy5knocfvnyZX366acKCQnRw4cPValSJTVu3FgTJ05UlSpVbBrL5mR1qVKlYlzu6empKlWqqGzZsrYOCQAAAOAFiMEBAACQWA0bNkyNGjVSp06dVLJkSb3zzjsaOnSoJkyYEP/J6rp166pIkSJyd3e39a4AAACwoyRe0JGkEYMDAAC8uZJ6HH7w4EH99NNPMplMxtmA9evX17Bhw2wey+ae1X369NGmTZtsfiAAAAAAr4YYHAAAAIlVypQpdfv2bYtlt27dUurUqW0ey+ZktYuLi1KkSGHzAwEAAMC+HEzx+4P4QwwOAADw5krqMXjdunXl6+urf/75R+Hh4Tp8+LB69eql2rVr2zyWzW1APv/8c3377bc6efKk8uTJo/Tp00fbpkSJEjZPBAAAAEDMiMEBAACQWHXu3FnPnj2Tr6+vgoKC9Mknn6hJkyby9fW1eSybk9XfffedJGny5MmSZHFVcrPZLJPJpBMnTtg8EQAAAMQvkxJJ6QVsRgwOAADw5krKcfjEiRN17NgxlS9fXv7+/rp7967SpEljEa/awuZk9cyZM1/pgQAAAAC8GmJwAAAAJDY//PCDli1bpuLFi2vChAl68uSJOnbs+FpjWpWsrlq1qiZNmqT8+fOrZMmSr/WAAAAAsI/E0tMO1iEGBwAASBqSahy+atUqzZgxQ3ny5NHu3bs1dOjQhElWBwQEKDg4+LUeCAAAAPaVVIPkpIoYHAAAIGlIqnH4o0ePlCdPHklSsWLFdOPGjdce0+G1RwAAAAAAAAAAJCsODv9LLTs52dxtOkZxMwoAAAASvVe9yAkAAACAV5dU43Cz2RznY1qdrO7SpYtcXFxeup3JZNLGjRtfa1IAAAAAiMEBAACQeIWGhmrZsmXG7ZCQEIvbktSgQQObxrQ6WV2gQAGlTZvWpsEBAACQeCTVXnlJGTE4AADAmy+pxuHp06fXhAkTjNtp0qSxuG0ymeIvWd2lSxe9//77Ng0OAAAARAoLC1O7du2UJUsWjRgxQpJ06NAhDR06VGfPnlWaNGnUqVMnNW3a1LjP0qVLNXnyZN26dUvvvvuuvvnmG3l7e9vrKSQ4YnAAAAAkVps3b47zMbnAIgAAQDJhMsXvz8tMnDhR+/btM24/ePBAHTt2VIMGDbR3714NGzZMw4cP1+HDhyVJu3fv1pAhQzRixAjt3btX9erVU6dOnRQUFBRfuwgAAACIc/aMwd80JKsBAAAQ73bu3Km//vpL1atXN5b99ddf8vLyUqtWreTk5KQyZcqobt26mjNnjiRp4cKFql27tooVKyZnZ2e1a9dOadKk0Zo1a+z1NAAAAADEI6uS1Q0bNlSaNGniey4AAACIRw4mU7z+xObOnTvq37+/xowZIzc3N2P5mTNnlDdvXottc+fOrZMnT0qSzp49+8L1SR0xOAAAQNJgjxj8TWVVz+rhw4fH9zwAAACQBIWHh6t3795q37698ufPb7HuyZMnFslrSXJ1dVVgYKBV65M6YnAAAAAkN1ZfYBEAAABvNntchXzq1KlycXFRmzZtoq1zc/u/9u49Puf68f/487psbHOaIRQlMyvnsZFTNFbJMaSDX82hHEYOSYiYGErCRkRpkUNfa2E5pXTAhxyjlIwcYsx5zDaubdfvD7k+XZ85XJW937Prce+2263r/X5f7+v1ft+68ey15/V6e+vixYtO2zIyMlS4cGHH/oyMjBz7aRsDAADgTmJGDr9TMVkNAACAXLNs2TKdPHlSwcHBkuSYfP7qq6/02muvaePGjU7H79+/XwEBAZKkgIAAJSYm5tj/8MMPGzByAAAAAEbjAYsAAABuIjefQn6j5fJWr16tHTt2aNu2bdq2bZtat26t1q1ba9u2bQoLC9Pp06cVGxsrm82mzZs3KyEhQR07dpQkderUSQkJCdq8ebNsNptiY2N15swZhYWFGXjXAAAAgH/H6Ax+zZkzZxQREaHg4GDVr19fUVFRyszMvOl79u3bp1q1aumHH364jXfAdTSrAQAA3IRVeev7hyVKlNDcuXMVFRWl6Oho+fn5aeTIkXrooYckSQ0aNNDo0aMVGRmp5ORkVa5cWXPmzJGvr6+5AwcAAAD+BrNy+MCBA1WmTBmtX79ep0+fVp8+fRQbG6sXX3zxusenp6dr8ODBOZbiMxKT1QAAADDMxIkTnV7XqFFDixcvvuHx7dq1U7t27XJ7WAAAAEC+cvjwYW3ZskXff/+9vL29VaFCBUVERGjSpEk3nKweM2aMWrRooX379hk82v9iGRAAAAA3YcYyIAAAAIC7MyODJyYmytfXV2XKlHFs8/f3V1JSki5cuJDj+KVLl+rw4cPq169fbtwCl9GsBgAAAAAAAIB85NKlS/L29nbadu11WlqaihUr5th+4MABTZkyRYsWLVKBAgUMHef/YrIaAADATVhpPwMAAACGMyOH+/j4KD093WnbtdeFCxd2bLt8+bIGDRqk119/XXfffbehY7welgEBAAAAAAAAgHwkICBA58+f1+nTpx3bDhw4oLJly6po0aKObT/99JMOHTqkESNGKDg4WMHBwZKk3r17KzIy0uhh06wGAABwF1YWlgYAAAAMZ0YOr1ixourWravx48frzTff1Llz5/Tee++pU6dOTscFBwdr9+7dTtsCAwM1a9Ys1a9f38ghS6JZDQAAAAAAAAD5TnR0tDIzM9W8eXN17txZTZo0UUREhCQpKChIy5cvN3mEOdGsBgAAcBMUqwEAAADjmZXDS5Uqpejo6Ovu27lz5w3f99tvv+XWkG6JZjUAAAAAAAAAwHQ0qwEAANwEa1YDAAAAxiOHu45mNQAAAAAAAADAdDSrAQAA3ASFDgAAAMB45HDXMVkNAADgJvhKHQAAAGA8crjruFcAAAAAAAAAANPRrAYAAHATFr5/CAAAABiOHO46mtUAAAAAAAAAANPRrAYAAHAT9DkAAAAA45HDXUezGgAAAAAAAABgOprVAAAAbsLKWnkAAACA4cjhrqNZDQAAAAAAAAAwHc1qAAAAN0GfAwAAADAeOdx1NKsBAAAAAAAAAKajWQ0AAOAmWCoPAAAAMB453HU0qwEAAAAAAAAApqNZDQAA4CYsVDoAAAAAw5HDXcdkNQAAgJvgK3UAAACA8cjhruNeAQAAAAAAAABMR7MaAADATfD1QwAAAMB45HDX0awGAAAAAAAAAJiOZjUAAICboM8BAAAAGI8c7jqa1QAAAAAAAAAA09GsBgAAcBOslQcAAAAYjxzuOprVAAAAAAAAAADT0awGAABwE7QUAAAAAOORw13HvQIAAAAAAAAAmI5mNQAAgJtgrTwAAADAeORw19GsBgAAAAAAAACYjmY1AACAm6DPAQAAABiPHO46JqsBAADcBN8+BAAAAIxHDncdy4AAAAAAAAAAAExHsxoAAMBNWPkCIgAAAGA4crjraFYDAAAAAAAAAExHsxoAAMBNsFYeAAAAYDxyuOtoVgMAAAAAAAAATEezGgAAwE1YWCsPAAAAMBw53HU0qwEAAAAAAAAApqNZDQAA4CZYKw8AAAAwHjncdTSrAQAAAAAAAACmo1kNAADgJqyslQcAAAAYjhzuOprVAAAAAAAAAADT0awGAABwE6yVBwAAABiPHO46JqsBAADcBCEZAAAAMB453HUsAwIAAAAAAAAAMB3NagAAADdh4cEuAAAAgOHI4a6jWQ0AAAAAAAAAMB3NagAAADdhpdABAAAAGI4c7jqa1QAAAAAAAAAA0+WJZvXx48d17Ngx2e12p+0hISEmjQgAACD/Ya08/BUZHAAAwBjkcNeZPlk9c+ZMTZs2Lcd2i8WiX3/91YQRAQAAAPkbGRwAAAB5kemT1bGxsZoxY4ZCQ0NlsfBbBgAAgNxC1MI1ZHAAAADjELdcZ/qa1R4eHmrWrBkhGQAAADAIGRwAAAB5kemT1V26dNGUKVOUmppq9lAAAADyNUsu/4M7BxkcAADAOGRw15m+DEilSpU0ePBgffjhhzn2sV4eAAAAcPuRwQEAAJAXmT5ZPXHiRHXv3l0NGzZUgQIFzB4OAABAvmXNf8UL/ENkcAAAAOOQw11n+mT1xYsXNXjwYLOHAQAAkO/lx68J4p8hgwMAABiHHO4609esDgsL09q1a80eBgAAAOA2yOAAAADIi0xvVmdkZGjAgAHy9/eXr6+v0xPJ582bZ+LIAAAA8hcLhQ78iQwOAABgHHK460yfrK5cubIqV65s9jCQz5w9e1bhXZ7WqDHjFFKvviTpp9279NaEcTqwf79K+JXQSz376MmOT5k8UgB5Wcq5s3qlT7gGDB2lmkEhkqSE+MVatmSBzp45Lb+SpdSuUxe16fiMJMlutytuYaxWLluiCykpCnywunr2H6KKlfh7DkDeQgbHzVxIOa9pk9/Spg3fKTvbrqA6wRo8fJRKlS7tdNzgl3tp987tTtvS09PVtsNTem1EpNP2qZMm6FLqRY0YM96xbcmiTxQ7Z6YKeBRQ95591b7T05KkTJtNPcOfVeT4Sbq34v25c5EAcl2VCiX1Tr8whTx4jy5euqwPvtipSQs3ym6XerWrq34d66lsySI6cSZVM+K3atbSbdc9TyHPAhrXM1RPPvyAingX1L4/zmjknG/0/Y+HJUnlShbRO/0eVdPa9+lKZraWrNujUR98o8u2LFmtFs1+rbXaNArU8TMXFfHOCv3n56OSpIrlfPXpm53UJOIjXbFlGXZfANyc6ZPV/fr1M3sIyGd27tiuUSOG6Y8/jji2XUhJUb8+PdWnX391eupp7di+VYP691XlKoGqUaOmiaMFkFft2b1T744fpePH/nBs+2Hjd5r/wXuKmjJLAYFVte/Xn/Vavx66935/1aoTouWfLdJniz7WqAlTFfhgda1YukTD+r+k9z+JV3HfEiZeDXAVhQ5cQwbHzYwYMlBFixXXp8tWq4C1gKIiX9fb40br7WnvOR03OeZ9p9dfLIvX3PdnqHvPvo5tKefPa9o7E/Tlqi/UsnU7x/a0S5c0fcrbmrsgTnbZ1aPLU3qsVRt5e/vo/xbNV/1GTZioBu5ghb08lfD2s/pq2+96ZvRnKlXcW3HjOsujgFU/Jp7QqG5N1XrIQu1MPKG6geW0durz+uXQKccE9F+N6xmqBtXKq1m/WCWdSVV4y1qKj+qsoG7v6+ipC1oy9imdOHdJNcNnySJp/qgnFT2opXq9/YXCQiqpYfUKqvJMjLo8WkMTerdQ036xkqTJ/R7V8FlfM1ENQ5DDXWf6ZPW5c+c0f/58JScnKzs7W5Jks9m0b98+LV++3OTR4U6zfNnnmjk9WgNfGaKhQwY5tn+19ksV9/XVM892kSTVq99AT7Rqo08XLWCyGkAOX61arvkfzlT3PgP1VuRQx/b6jZoqNm6VfHwKKyszUykp52WxWFSkSFFJ0rdrV6ltx2dVtUZtSVLbTs9qxdL/04Zv1qrVk53NuBQAuC4yOG5k7697tOfn3Ur48nsVLlJEkjR05BidPn36pu87cuigprwVpXdnzHY0sNPSLum5Dq3U/LGWatY8zOl4q/Xq45Pssstut+vqo6csOpl8Qqu/WK458xbf/osDYJiGNSqotK+PBk5bLVtmto5k2PT2go2a1O9R3d9pmgKfna7U9CsqYLWoZHEf2e1SSmrGdc/lVdBDb8Z+r6OnLkqSPlrxo8a9FKqgKuXkXchTdR+4W1WeidHZC+mSpNEffKuvpr2gITPWKjPr6t9x15a7yvrz77w2jaooLcOmddsP5vatAPA3mT5ZPXz4cB06dEh+fn5KTU3V3XffrQ0bNqhLly5mDw13oIaNGuuJVm3k4eHhNFl94ECiAgKqOB1byb+yPo+PM3qIAO4Adeo11CNhT6iAh4fTZLUk+fgU1tEjh9T7hY7KzsrSk08/L/8qD0iSsrOz5OXt7XS8xWLRH0cIwcgbrCyWhz+RwXEjv/78kyre76/ln8dpadxiZaSnq37Dxuo3aMhN3zd54li1bN1OtYLqOrYVLFhI85csk1/JUooa/brT8V7e3ho0dKReGxAhi8WiYW+MkZe3t8aNfl09+w1QIS+vXLk+AMYoYLXqii1Ltsxsx7Zsu11l/YrIt4iXzqdmKKCCn3bM7SWPAlZN+7/N2rU/+brnennKKqfXTYPuU/HChbRr/wn5eHlKki5l2Jw+p6BnAd1fzlfrth/U19sPave83ko6naq+k1fIu5CHRnVrqrZDF+XClQPXRw53ndXsAWzdulUff/yxhg0bpnvvvVezZs1SVFSUfv/9d7OHhjtQqVKl5eGR83cwaZcuyft/JpC8vLyUnpZm1NAA3EH8SpZSgev8WXJN2bvv0dKvNmvqnAX6/uvVWrLgI0lSo6YttCxukQ4k7lVmpk0rli7R0T8O68rly0YNHQBcQgbHjVy4kKIDift09I/D+mjhZ/po0Wc6dfKkxo16/Ybv2bVzu/b8tFvdevZx2u7h4SG/kqVu+L72HTsrfuXX+mzFV2rZpr22bNqorMxM1axdRyNfG6Suz3bQ5IljlWmz3fAcAPKmTT//ofQrmRr70iPyLuShe8sU06CnG0iSvAtdzdkHk86rxONvqVHvuXoqtJoGP9Pgluet9+DdWjCqg8Z9vF6HT6TotyNntOfgSb0d0ULFCxdSqeI+GhH+sONz7Park933dpiqh3p+oO2/Hdew/9dY81bvUsniPlo79XltnNVdL7apk3s3A8DfYvpktYeHh8qUKaOKFSvqt99+kyS1atVKv/zyi8kjQ37i5e2tjAznrxRlZGTIp3Bhk0YE4E7m4eEpDw9PVXmgmtp2ek7frr3a9uj4zAtq8XgbjX19kMI7ttTRIwdVN6SBihQtZvKIgassufyDOwcZHDfi6VlQktR/8DD5FC4sv5Kl1LNvf23a+L3S0i5d9z3L45coNOwxlSxV+rr7XXHlyhW9Fz1ZA4YM1/y5c1SseHF9tPAzHTv6h75YFv+PzwvAHCmXLqv9sMUKeeBuJS5+WZ+M6qAFX/4kSTr/53IfmVnZyszK1o59xzXjsy3q3LzaTc/Z9YnaWvFOF729YKMmfrJBkpSdbVenkUvkW9RbP83vo1WTuyj+u18lSecu5lxWJKCCn5rXvV/vxW/VrFdbac7y7Wo5eIFGhjfRA/fd+JdrwL9FBned6cuA3HPPPfr5559VvXp1Xbp0SWfPnpWHh0eOiUXg36gcUEWb/7PRadvvB/arcuUAk0YE4E70+afztfeXnzR8zNuObTabTUWLXZ2MPn36pB5t3V7PvxghScrKzFTXzk+oxRNtTRkvANwIGRw3cn8lf9nt2cq02VSoUCFJ/13jVfacx2dmZmr9d+s04Z3of/W5C+fNVfOwlipb7m79fiBRDz/SXBaLRYEPVNWB/fv+1bkBGM/TwyqPAlY9PniBY9tLbevol0On9GKbOqr34D16fuznjn0FPT107s81p/+X1WrRtAGPq12TQHV+Y4m+2XHIaX+JIl76f2PilXElU5L0aD1/Xbh0WfuPns1xrndffkxDZqxVVrZdVe8vrR37TujCpcs6ePy8qlYspb2Hb74+P4DcZ3qz+rnnntPzzz+vpKQktW7dWuHh4QoPD1dISIjZQ0M+0rxFmE6fOa1P5sfKZrNp65bNWrkiQe07dDR7aADuINVr19Wm9d/o+3VrlJ2drT27d2p53EI90f4pSdL3X6/R2OEDdSHlvNLT0vTR+9Hy9Cyo+g2bmjxy4E9Uq/EnMjhuJKR+A919T3lNeHOk0tIu6dy5s5ozY5qaNGt+3W8lHkjcp8sZl1W9VtA//szjScf0/bqv9Mzz4ZKkChXu057du5Rps+mXPT+pfIV7//G5AZjDYrEo4e1nFd6yliQpKKCshnZppOmfbdGGXUfUplEVdWz6oCwWqUG18urbMURzlu+47rkmRYTpsXr+atxnbo6Jakn6cHhbvfpsQ1kskv89JRTVM1QzP9+mrGzn37B1avagjp68oE17jkqS9h89q4eqlZdfMW9VLu+nA8fO3d6bAPwVGdxlFvvVRy+bavfu3XrggQdksVgUGxur1NRUde/eXcWLF7/p+9JZugw3Ubt6oObMnaeQevUlSXt+/klvT4zS/sR9KlHCTy/1jlC79h1MHiXyoqRz1/+NPtzTE01qa2L0HNUMujqB88PG7zRvzgydOH5Md5Utp85duumRR1tJkjIzbXo/epI2fLNWmZk2VatZR30GDlWZcveYeQnIA/zv8r71QQbYfOB8rp7/IX/fXD0/bq9/msEl6VRqpgEjhFlOnzqpmHff1o/bt+rylStq/HAzDRgyXEWLFlNY42ANeT1Sjz7RWpL0zVdr9O5bUUpY+/1Nz3ntAYsjxozPsW/ooL7q/OzzqlvvoT8//5RGDx+s/ft+U3D9hzRq7Fs8cNEN3Nv2LbOHgNusUc0KejsiTAHl/XTqfJqmf7ZFMz/fJkl6okGARndvqoplfXUkOUWTF/1Hi7/ec/V9NSpo6cRnVKfb+0rLsOnwZwOVlW3Xlcwsp/O//O5KLf56j6pWLK1pAx9XTf8yuph2WbErdylq3vf662xXEe+CWhf9gloOXqAzfza4G9WooFlDWsu3qJdmfr5N4+etN+bGwDDp60aYPQSH3Mzh+S2D54nJ6qysLO3YsUOnTp1SuXLlFBTk2m/lmawGkBuYrAZwu+WVyeofDqTk6vnr+996khN5xz/N4BKT1QBuPyarAdxueWmyOjdzeH7L4KavWX3gwAH17t1bx48fl6+vr86dOyd/f3/Nnj1bZcuWNXt4AAAA+YYlH35NEP8MGRwAAMA45HDXmb5mdWRkpBo2bKht27Zpw4YN2rJli2rWrKnIyEizhwYAAADkS2RwAAAA5EWmN6v37NmjDz/8UAULFpQkFS5cWCNGjFCTJk1MHhkAAED+QqED15DBAQAAjEMOd53pzeq77rpLBw8edNp2bd08AAAAALcfGRwAAAB5kenN6tatW6tnz57q0aOH7rvvPiUnJ2vu3LkKDg7W0qVLHce1b9/etDECAADkC1Q68CcyOAAAgIHI4S6z2O12u5kDCA0NveUxFotFX3/9dY7t6bbcGBEAd5d0Lt3sIQDIZ/zv8jZ7CJKkrQdz7ynkkhRyf/56Enl+9m8yuCSdSs283UMC4ObubfuW2UMAkM+krxth9hAccjOH57cMbmqzOjs7W3FxcfLz85Mkbdq0SXv37lXTpk1VqVIlM4cGAACQ71iodEBkcAAAAKORw11n2prVycnJatOmjSZNmiRJSkhIUI8ePZSQkKDOnTvrp59+MmtoAAAAQL5EBgcAAEBeZtpk9ZQpUxQYGKhXX31VkhQTE6OXXnpJ8fHxGjVqlGJiYswaGgAAQL5kseTuD/I+MjgAAIDxyOCuM22yeuPGjRo5cqRKliyppKQkHTlyRG3btpUkNW/eXD/++KNZQwMAAADyJTI4AAAA8jLT1qxOTU11rJO3a9cuFStWTP7+/pKkQoUKyWbj6YkAAAC3Uz4sXuBvIoMDAAAYjxzuOtOa1cWLF9fZs2clSVu2bFGdOnUc+37//XeVKFHCrKEBAAAA+RIZHAAAAHmZaZPVjzzyiMaOHauVK1cqISFBrVq1kiRduHBB06ZNU5MmTcwaGgAAQP5kyeUf5HlkcAAAABOQwV1m2mT1oEGDlJKSotdff12PPfaY2rRpI0lq2rSpEhMT9fLLL5s1NAAAgHzJksv/3MzevXvVrVs31atXT40aNdJrr73maPju2rVLTz31lIKCghQaGqolS5Y4vffzzz9XWFiYateurQ4dOmjnzp25do/yOzI4AACA8czK4Hcii91ut5s9iL/asGGDQkJCVKhQoVsem86SegByQdK5dLOHACCf8b/L2+whSJJ2Hr6Yq+cPuq/odbdnZGSoRYsW6ty5s3r37q1Lly5p6NChslqteuutt/Too4+qf//+evrpp7V161b17dtXsbGxqlmzpn744Qf16dNHc+bMUc2aNbVgwQLNmjVL33zzjby988Z9zQ/+TgaXpFOpmbk8IgDu5t62b5k9BAD5TPq6EWYPwSE3c/iNMvidyrRm9Y00btzY5ZAMAAAA11ksuftzI0lJSXrggQfUt29fFSxYUCVKlHBMTH/55Zfy9fVVly5d5OHhoQYNGqhNmzZasGCBJGnJkiVq1aqV6tatK09PT3Xt2lUlSpTQypUrDbpr7oEMDgAAkHvMyOB3qjw3WQ0AAID8pVKlSvrggw9UoEABx7Y1a9aoWrVqSkxMVJUqVZyOr1y5svbu3StJ2r9//033AwAAAMg/mKwGAABwE3nh+Yp2u11TpkzRN998oxEjRujSpUs5lvPw8vJSWlqaJN1yPwAAAJDXmZXBz5w5o4iICAUHB6t+/fqKiopSZub1l3NbtGiRHnvsMQUFBemxxx5zfNPRaExWAwAAwBCpqanq37+/EhIS9MknnygwMFDe3t7KyMhwOi4jI0OFCxeWpFvuBwAAAHB9AwcOlI+Pj9avX6+4uDht2rRJsbGxOY776quv9O677+qtt97Sjh07NHHiRE2dOlVr1qwxfMxMVgMAALgLE6vVR44cUceOHZWamqq4uDgFBgZKkqpUqaLExESnY/fv36+AgABJUkBAwE33AwAAAHmeCRn88OHD2rJli4YMGSJvb29VqFBBERER121MJycn66WXXlLt2rVlsVgUFBSk+vXra+vWrbfl8v8OJqsBAACQq1JSUhQeHq46deroww8/lJ+fn2NfWFiYTp8+rdjYWNlsNm3evFkJCQnq2LGjJKlTp05KSEjQ5s2bZbPZFBsbqzNnzigsLMysywEAAADyvMTERPn6+qpMmTKObf7+/kpKStKFCxecju3SpYt69uzpeH3mzBlt3bpV1atXN2y813gY/okAAAAwhcXllaVvr/j4eCUlJWnVqlVavXq1076dO3dq7ty5ioqKUnR0tPz8/DRy5Eg99NBDkqQGDRpo9OjRioyMVHJysipXrqw5c+bI19fXhCsBAAAA/j4zcvj1nv1y7XVaWpqKFSt23fedOnVKvXr1UvXq1dW6detcH+f/YrIaAAAAuapbt27q1q3bDffXqFFDixcvvuH+du3aqV27drkxNAAAACBf8vHxUXp6utO2a69v9PyXH3/8UQMGDFBwcLAmTJggDw/jp45ZBgQAAMBNWCy5+wMAAAAgJzMyeEBAgM6fP6/Tp087th04cEBly5ZV0aJFcxwfFxenrl27Kjw8XJMnT1bBggVz41bcEpPVAAAAAAAAAJCPVKxYUXXr1tX48eOVmpqqP/74Q++99546deqU49g1a9YoMjJSMTEx6t69uwmj/S8mqwEAANxEbj6EnGI1AAAAcH1mZfDo6GhlZmaqefPm6ty5s5o0aaKIiAhJUlBQkJYvXy5Jmj59urKystS/f38FBQU5fkaNGnXb7oGrWLMaAADAXTCjDAAAABjPpBxeqlQpRUdHX3ffzp07Hf+ekJBg1JBuiWY1AAAAAAAAAMB0NKsBAADchIVqNQAAAGA4crjraFYDAAAAAAAAAExHsxoAAMBNWCh0AAAAAIYjh7uOZjUAAAAAAAAAwHQ0qwEAANwEhQ4AAADAeORw19GsBgAAAAAAAACYjmY1AACAu6DSAQAAABiPHO4ymtUAAAAAAAAAANPRrAYAAHATFiodAAAAgOHI4a5jshoAAMBNWMjIAAAAgOHI4a5jGRAAAAAAAAAAgOloVgMAALgJCh0AAACA8cjhrqNZDQAAAAAAAAAwHc1qAAAAd0GlAwAAADAeOdxlNKsBAAAAAAAAAKajWQ0AAOAmLFQ6AAAAAMORw11HsxoAAAAAAAAAYDqa1QAAAG7CQqEDAAAAMBw53HU0qwEAAAAAAAAApqNZDQAA4CYodAAAAADGI4e7jmY1AAAAAAAAAMB0NKsBAADcBZUOAAAAwHjkcJcxWQ0AAOAmLKRkAAAAwHDkcNexDAgAAAAAAAAAwHQ0qwEAANyEhUIHAAAAYDhyuOtoVgMAAAAAAAAATEezGgAAwE1Q6AAAAACMRw53Hc1qAAAAAAAAAIDpaFYDAAC4CyodAAAAgPHI4S6jWQ0AAAAAAAAAMB3NagAAADdhodIBAAAAGI4c7jqa1QAAAAAAAAAA09GsBgAAcBMWCh0AAACA4cjhrqNZDQAAAAAAAAAwHc1qAAAAN0GhAwAAADAeOdx1TFYDAAC4Cb5+CAAAABiPHO46lgEBAAAAAAAAAJiOZjUAAIDboNIBAAAAGI8c7iqa1QAAAAAAAAAA09GsBgAAcBOslQcAAAAYjxzuOprVAAAAAAAAAADT0awGAABwExQ6AAAAAOORw11HsxoAAAAAAAAAYDqa1QAAAG6CtfIAAAAA45HDXUezGgAAAAAAAABgOprVAAAAbsLCankAAACA4cjhrqNZDQAAAAAAAAAwHc1qAAAAd0GhAwAAADAeOdxlTFYDAAC4CTIyAAAAYDxyuOtYBgQAAAAAAAAAYDqa1QAAAG7CQqUDAAAAMBw53HU0qwEAAAAAAAAApqNZDQAA4CYsrJYHAAAAGI4c7jqa1QAAAAAAAAAA09GsBgAAcBcUOgAAAADjkcNdRrMaAAAAAAAAAGA6mtUAAABugkIHAAAAYDxyuOtoVgMAAAAAAAAATEezGgAAwE1YqHQAAAAAhiOHu45mNQAAAAAAAADAdDSrAQAA3ISF1fIAAAAAw5HDXcdkNQAAgJvg64cAAACA8cjhrmMZEAAAAAAAAACA6ZisBgAAAAAAAACYjslqAAAAAAAAAIDpWLMaAADATbBWHgAAAGA8crjraFYDAAAAAAAAAExHsxoAAMBNWESlAwAAADAaOdx1NKsBAAAAAAAAAKajWQ0AAOAmWCsPAAAAMB453HU0qwEAAAAAAAAApqNZDQAA4CYodAAAAADGI4e7jmY1AAAAAAAAAMB0NKsBAADcBZUOAAAAwHjkcJcxWQ0AAOAmLKRkAAAAwHDkcNexDAgAAAAAAAAAwHQ0qwEAANyEhUIHAAAAYDhyuOtoVgMAAAAAAAAATEezGgAAwE1Q6AAAAACMRw53Hc1qAAAAAAAAAIDpaFYDAAC4CyodAAAAgPHI4S6jWQ0AAAAAAAAAMB3NagAAADdhodIBAAAAGI4c7jqa1QAAAAAAAAAA09GsBgAAcBMWCh0AAACA4cjhrqNZDQAAAAAAAAAwncVut9vNHgQAAAAAAAAAwL3RrAYAAAAAAAAAmI7JagAAAAAAAACA6ZisBgAAAAAAAACYjslqAAAAAAAAAIDpmKzGHSc0NFTx8fE5tsfHxys0NNSEEQG4E6WkpCgyMlJNmzZV7dq11bhxYw0dOlQnTpwwe2gAAOQ5ZHAAtwMZHMCtMFkNAHBLgwYN0rlz5xQXF6cff/xRS5cu1ZUrV9StWzdlZmaaPTwAAAAg3yGDA7gVD7MHAOSGdevWafbs2Tp8+LDS0tJUo0YNjRs3ThUrVlR8fLzi4uJUq1YtffbZZ7Jarerbt68KFSqkmTNn6sKFC2rVqpXefPNNsy8DQC7avn27oqKiVLp0aUlSqVKl9Prrr2vy5Mm6cOGCOnXqpA4dOighIUEnT55U1apVNWbMGFWuXFmSFBcXp4ULF+rYsWO6cuWK6tWrpwkTJsjPz08xMTHav3+/vLy8tHbtWhUpUkTDhw/X77//rgULFigzM1Ndu3ZVRESEmbcAAIDbigwO4FbI4ABuhWY17khjxoxRcHCw08+YMWMkSSdOnNCAAQPUs2dPbdq0Sd9++63sdrtmzJjheP/27dtVpkwZbd68Wf3799eECRP0ww8/aOXKlYqNjVVcXJy2bt1q1uUBMECrVq00evRoRUZGauXKlTp27JhKly6tiRMnys/PT5L06aefaurUqdq0aZP8/f3Vu3dv2Ww27d69W+PGjVNkZKR++OEHrVq1SocOHdK8efMc51+zZo0eeeQRbd++XW3bttXgwYOVmpqq7777TuPHj9e0adN07Ngxsy4fAIC/jQwO4N8igwO4FSarcUcaPXq0tm3b5vQzevRoSZKfn59WrFih0NBQpaam6sSJEypRooSSk5Md7/fx8VF4eLisVqsaN26srKws9ejRQ97e3qpRo4buuusu/gID8rlx48Zp1KhROn78uEaNGqXQ0FCFhYVp+fLljmN69OihBx98UF5eXho+fLiOHz+uHTt2qEqVKvriiy9Us2ZNpaSk6OTJk/Lz83P6c6Zy5cp6/PHHZbFY1KhRI2VlZal3797y9PR0rO2ZlJRk+HUDAPBPkcEB/FtkcAC3wjIgyHc8PT31xRdfaPHixbJYLKpSpYpSU1Pl4fHf/9x9fX1lsVgkSVbr1d/ZFCtWzLHfarUqOzvb2IEDMJTValW7du3Url072e12HThwQMuWLdNrr73m+Frifffd5zje29tbvr6+OnXqlKxWq+bNm6eEhAT5+PgoMDBQqampstvtjuN9fX2dPkuSihcv7vSaP2cAAPkFGRyAK8jgAG6FZjXynVWrVumTTz7R/Pnz9d1332nOnDmqWrWq0zHXQjIA97R+/XoFBQXp/Pnzkq7+mVC5cmUNHjxYVatW1S+//CJJTi2NS5cu6dy5cypXrpxiY2O1ceNGJSQk6Ouvv9Z7772ne+65x+kz+HMGAOBOyOAAboUMDsAVTFYj37l48aKsVqu8vLxkt9v1/fffa+nSpbLZbGYPDUAeERISopIlS2r48OH67bffZLPZlJqaquXLl+vQoUNq1qyZJOmjjz7S4cOHlZ6ergkTJqhSpUoKCgpyNMU8PT2VmZmpZcuWaf369fw5AwBwW2RwALdCBgfgCpYBQb7z5JNPavv27WrVqpUKFCigSpUqKTw8XAsWLNCVK1fMHh6APMDLy0sLFy7U9OnT1adPH505c0aenp6qXbu2PvroI/n7+0uS6tatq759+yopKUkhISGaPXu2rFarunfvrn379umRRx5RoUKFVLVqVT333HPavHmzyVcGAIA5yOAAboUMDsAVFvtfF/cBAACSpNDQUPXr108dOnQweygAAACAWyCDA2AZEAAAAAAAAACA6ZisBgAAAAAAAACYjmVAAAAAAAAAAACmo1kNAAAAAAAAADAdk9UAAAAAAAAAANMxWQ0AAAAAAAAAMB2T1QAAAAAAAAAA0zFZDcDt8FxZAAAAwFhkcACAK5isBvC3PP/88woMDHT6qV69upo1a6YxY8YoJSUl1z47Pj5egYGBOnr0qCQpJiZGgYGBLr//xIkT6tWrl44dO/avx3L06FEFBgYqPj7+hsf83fH9m89y1bBhwxQaGvqvzwMAAADjkMGvIoMDQP7nYfYAANx5qlatqtGjRzte22w27dmzR++++65+/fVXLVq0SBaLJdfH8dRTT6lJkyYuH/+f//xH3377rd54441cHBUAAABw+5HBAQDugMlqAH9bkSJFVLt2badtISEhunTpkqKjo7Vr164c+3ND2bJlVbZs2Vz/HAAAAMBsZHAAgDtgGRAAt0316tUlSUlJSZKufl3x1VdfVf/+/VWnTh317NlTknT58mW9/fbbatq0qapXr642bdpo5cqVTufKzs7We++9p2bNmqlWrVqKiIjI8fXG633Fb8WKFerQoYNq1aqlZs2aadKkSbpy5Yri4+M1fPhwSVLz5s01bNgwx3uWLFmiVq1aOb5KGRMTo8zMTKfzfvnll2rbtq1q1qypJ598Unv37r0Nd+yqrVu3qkePHgoJCVH16tUVGhqqmJgYZWdnOx2XnJysXr16qWbNmmratKmio6OVlZXldIwr1wIAAID8gwz+z5DBASBvYrIawG1z8OBBSVKFChUc21atWiVPT0/NmDFDL7zwgux2u/r27avFixerW7dumjlzpoKCgjRo0CAtXbrU8b5JkyZpxowZ6tixo6ZPn64SJUpo8uTJN/38xYsX65VXXtGDDz6o6dOnq1evXlq4cKEiIyPVrFkz9enTR5I0ffp0RURESJLef/99vfHGG2rQoIFmzZqlLl26aM6cORo1apTjvOvWrVP//v0VEBCg6dOnq2XLlhoyZMhtuWd79+5V165d5evrqylTpmjmzJmqU6eOpk+frhUrVjgdGxMTIz8/P8d9mTVrlqKjox37XbkWAAAA5C9k8L+PDA4AeRfLgAD42+x2u1NTICUlRVu2bNHMmTNVu3ZtR7tDkqxWq8aOHSsfHx9J0saNG7V+/XpNmTJFTzzxhCSpSZMmSk9P1zvvvKPWrVsrLS1N8+fP1wsvvKCXX37ZcUxycrLWr19/3TFlZ2crJiZGYWFhioqKcmy/fPmyPv/8cxUpUkT33nuvJOnBBx9U+fLldfHiRc2cOVNPP/20Ro4cKUlq3LixfH19NXLkSHXr1k0BAQGaMWOGqlWr5gjqDz/8sCTdMri7Yu/evWrYsKEmTZokq/Xq7w8bNWqkb7/9Vlu3blWbNm0cxzZo0EATJkxw3I/U1FTNmzdP3bt3l9VqdelaAAAAcGcig5PBAcAdMFkN4G/bunWrqlWr5rTNarWqQYMGGjt2rNODXcqXL+8IyZK0adMmWSwWNW3a1Clsh4aGavny5UpMTNSpU6dks9nUvHlzp89o2bLlDYPywYMHdfr0abVo0cJpe9euXdW1a9frvmfnzp1KT09XaGhojrFIV0N9hQoVtGfPHvXv3z/HWG5HUG7fvr3at2+vy5cv68iRIzp8+LD27NmjrKws2Ww2p2Ov/Y/FNY8++qg+/vhj/fjjj7JYLLe8FoIyAADAnYsMTgYHAHfAZDWAv61atWoaM2aMJMlisahQoUIqV66cihQpkuPYUqVKOb0+f/687Ha76tSpc91znzx5UhcuXJAk+fn5Oe0rXbr0Dcd0/vx5SVLJkiVdvo5r77m2jt/1xpKSkiK73Z5jLHfddZfLn3MzGRkZGjt2rJYtW6bMzEyVL19eQUFB8vDwkN1udzr2f+/ltTH9dR3Bm10LAAAA7lxkcDI4ALgDJqsB/G2FCxdWjRo1/tF7ixYtKh8fH82bN++6+++77z7t3r1bknTmzBlVqlTJse9asL2eYsWKSZLOnj3rtP38+fPas2fPdZ+Mfu0977zzjipWrJhjf6lSpeTr6yur1arTp0/nOO/tEBUVpTVr1mjq1Klq2LChowHToEGDHMde+x+Ia66NqWTJko4GyM2uBQAAAHcuMjgZHADcAQ9YBGCoevXqKS0tTXa7XTVq1HD8JCYmasaMGcrMzFRQUJC8vLy0evVqp/d+8803NzxvpUqVVKJECX399ddO2xMSEvTSSy/p8uXLjvXorqlVq5Y8PT2VnJzsNBZPT09NnjxZR48eVaFChRQUFKQvv/zSqWWxbt2623A3pO3bt6t+/fpq0aKFIyT//PPPOnv2bI4nkf/v1y9XrFghb29v1apVy6VrAQAAgHsigzsjgwNA3kWzGoChmjZtqpCQEEVERCgiIkL+/v7avXu3YmJi1LhxY8fX6iIiIjR16lR5e3vroYce0nfffXfToFygQAG9/PLLevPNNxUZGamwsDAdOnRIU6dO1bPPPis/Pz9Hi2Pt2rV6+OGH5e/vrxdffFHTpk1Tamqq6tevr+TkZE2bNk0Wi0UPPPCAJOmVV15ReHi4+vXrp6efflqHDh3SzJkzXb7m2NjYHNuKFCmiTp06qWbNmlq1apUWLVokf39/7d27VzNnznSsf/dXX375pcqUKaOGDRtqw4YN+vTTTzVgwADHVz9duRYAAAC4HzL4VWRwAMj7mKwGYCir1arZs2dr2rRpev/993XmzBmVKVNGXbt2Vd++fR3H9erVSz4+Pvr444/18ccfKygoSEOHDlVkZOQNz92lSxf5+Pjoww8/VFxcnMqUKaPu3bs71pCrX7++GjZsqMmTJ2vTpk2aPXu2Bg4cqNKlS2vhwoX64IMPVLx4cTVo0ECvvPKKihYtKkkKDg7WnDlz9O6776pfv34qX768xo8fr969e7t0zdeeHv5X99xzjzp16qRhw4bJZrNp6tSpunLlisqXL68+ffpo//79WrdunbKyshzvGTZsmFavXq3Y2FiVLl1aw4cPV3h4uGO/K9cCAAAA90MGv4oMDgB5n8X+v08PAAAAAAAAAADAYKxZDQAAAAAAAAAwHZPVAAAAAAAAAADTMVkNAAAAAAAAADAdk9UAAAAAAAAAANMxWQ0AAAAAAAAAMB2T1QAAAAAAAAAA0zFZDQAAAAAAAAAwHZPVAAAAAAAAAADTMVkNAAAAAAAAADAdk9UAAAAAAAAAANMxWQ0AAAAAAAAAMN3/B44Mxkg3uxO3AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1500x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Confusion Matrix Analysis:\n", "==================================================\n", "True Negatives (<PERSON> correctly classified):  961\n", "False Positives (Ham misclassified as Spam): 5\n", "False Negatives (Spam misclassified as Ham): 10\n", "True Positives (Spam correctly classified):  139\n", "\n", "Error Analysis:\n", "False Positive Rate: 0.005 (legitimate messages blocked)\n", "False Negative Rate: 0.067 (spam messages missed)\n", "\n", "Business Impact:\n", "✓ Excellent: Very few legitimate messages blocked\n"]}], "source": ["# Create and visualize confusion matrix\n", "# - Function: confusion_matrix() computes confusion matrix\n", "# - Parameters: y_test (true), y_pred (predicted)\n", "# - Returns: 2D array where element [i,j] is count of true label i predicted as j\n", "cm = confusion_matrix(y_test, y_pred, labels=['ham', 'spam'])\n", "\n", "# Create both raw and normalized confusion matrices\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Raw counts\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['Ham', 'Spam'],\n", "            yticklabels=['Ham', 'Spam'],\n", "            cbar_kws={'label': 'Count'},\n", "            ax=axes[0])\n", "axes[0].set_title('Confusion Matrix - Raw Counts', fontsize=14, fontweight='bold')\n", "axes[0].set_xlabel('Predicted Label', fontsize=12)\n", "axes[0].set_ylabel('True Label', fontsize=12)\n", "\n", "# Normalized (percentages)\n", "cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "sns.heatmap(cm_normalized, annot=True, fmt='.2%', cmap='Blues',\n", "            xticklabels=['Ham', 'Spam'],\n", "            yticklabels=['Ham', 'Spam'],\n", "            cbar_kws={'label': 'Percentage'},\n", "            ax=axes[1])\n", "axes[1].set_title('Confusion Matrix - Normalized', fontsize=14, fontweight='bold')\n", "axes[1].set_xlabel('Predicted Label', fontsize=12)\n", "axes[1].set_ylabel('True Label', fontsize=12)\n", "\n", "plt.suptitle('Naive Bayes Spam Classification - Confusion Analysis', \n", "             fontsize=16, fontweight='bold', y=1.02)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print detailed confusion matrix analysis\n", "print(\"\\nConfusion Matrix Analysis:\")\n", "print(\"=\" * 50)\n", "tn, fp, fn, tp = cm.ravel()\n", "print(f\"True Negatives (<PERSON> correctly classified):  {tn}\")\n", "print(f\"False Positives (Ham misclassified as Spam): {fp}\")\n", "print(f\"False Negatives (Spam misclassified as Ham): {fn}\")\n", "print(f\"True Positives (Spam correctly classified):  {tp}\")\n", "\n", "print(\"\\nError Analysis:\")\n", "print(f\"False Positive Rate: {fp/(fp+tn):.3f} (legitimate messages blocked)\")\n", "print(f\"False Negative Rate: {fn/(fn+tp):.3f} (spam messages missed)\")\n", "\n", "# Business impact\n", "print(\"\\nBusiness Impact:\")\n", "if fp/(fp+tn) < 0.01:\n", "    print(\"✓ Excellent: Very few legitimate messages blocked\")\n", "elif fp/(fp+tn) < 0.05:\n", "    print(\"✓ Good: Acceptable false positive rate\")\n", "else:\n", "    print(\"⚠ Warning: High false positive rate may frustrate users\")"]}, {"cell_type": "markdown", "id": "baseline_comparison_section", "metadata": {}, "source": ["## <a id='toc10_5_'></a>[Baseline Model Comparison](#toc0_)\n", "\n", "We compare our model against simple baseline strategies to validate that it's learning meaningful patterns."]}, {"cell_type": "code", "execution_count": 34, "id": "baseline_comparison", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "BASELINE MODEL COMPARISON\n", "============================================================\n", "\n", "MOST_FREQUENT Strategy:\n", "  Accuracy:  0.866 (86.6%)\n", "  F1-Score:  0.000\n", "  (Always predicts the most common class - ham)\n", "\n", "STRATIFIED Strategy:\n", "  Accuracy:  0.747 (74.7%)\n", "  F1-Score:  0.102\n", "  (Random predictions matching training distribution)\n", "\n", "UNIFORM Strategy:\n", "  Accuracy:  0.500 (50.0%)\n", "  F1-Score:  0.210\n", "  (Random predictions with equal probability)\n", "\n", "PRIOR Strategy:\n", "  Accuracy:  0.866 (86.6%)\n", "  F1-Score:  0.000\n", "  (Predicts class probabilities from training set)\n", "\n", "==================================================\n", "NAIVE BAYES Performance:\n", "  Accuracy:  0.987 (98.7%)\n", "  F1-Score:  0.949\n", "\n", "==================================================\n", "PERFORMANCE VALIDATION:\n", "Accuracy improvement over best baseline: 12.0%\n", "F1-Score improvement over best baseline: 73.9%\n", "\n", "✓ Excellent! Model significantly outperforms baselines\n"]}], "source": ["# Create baseline models for comparison\n", "baseline_strategies = ['most_frequent', 'stratified', 'uniform', 'prior']\n", "\n", "for strategy in baseline_strategies:\n", "\n", "    # Create and train baseline model\n", "    # - Class: DummyClassifier provides simple baseline predictions\n", "    # - Parameters: strategy defines the prediction approach\n", "    baseline = DummyClassifier(strategy=strategy, random_state=42)\n", "\n", "    # Make predictions\n", "    baseline.fit(X_train_vectorized, y_train)\n", "\n", "    # Calculate metrics\n", "    y_baseline_pred = baseline.predict(X_test_vectorized)\n", "    acc = accuracy_score(y_test, y_baseline_pred)\n", "    f1 = f1_score(y_test, y_baseline_pred, pos_label='spam')\n", "\n", "# Compare with <PERSON><PERSON>\n", "\n", "# Calculate improvement\n", "\n", "# Create baseline models for comparison\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"BASELINE MODEL COMPARISON\")\n", "print(\"=\" * 60)\n", "\n", "baseline_strategies = ['most_frequent', 'stratified', 'uniform', 'prior']\n", "baseline_results = {}\n", "\n", "for strategy in baseline_strategies:\n", "    # Create and train baseline model\n", "    # - Class: DummyClassifier provides simple baseline predictions\n", "    # - Parameters: strategy defines the prediction approach\n", "    baseline = DummyClassifier(strategy=strategy, random_state=42)\n", "    baseline.fit(X_train_vectorized, y_train)\n", "    \n", "    # Make predictions\n", "    y_baseline_pred = baseline.predict(X_test_vectorized)\n", "    \n", "    # Calculate metrics\n", "    acc = accuracy_score(y_test, y_baseline_pred)\n", "    prec = precision_score(y_test, y_baseline_pred, pos_label='spam', zero_division=0)\n", "    rec = recall_score(y_test, y_baseline_pred, pos_label='spam', zero_division=0)\n", "    f1 = f1_score(y_test, y_baseline_pred, pos_label='spam', zero_division=0)\n", "    \n", "    baseline_results[strategy] = {\n", "        'accuracy': acc,\n", "        'precision': prec,\n", "        'recall': rec,\n", "        'f1': f1\n", "    }\n", "    \n", "    print(f\"\\n{strategy.upper()} Strategy:\")\n", "    print(f\"  Accuracy:  {acc:.3f} ({acc*100:.1f}%)\")\n", "    print(f\"  F1-Score:  {f1:.3f}\")\n", "    \n", "    if strategy == 'most_frequent':\n", "        print(\"  (Always predicts the most common class - ham)\")\n", "    elif strategy == 'stratified':\n", "        print(\"  (Random predictions matching training distribution)\")\n", "    elif strategy == 'uniform':\n", "        print(\"  (Random predictions with equal probability)\")\n", "    elif strategy == 'prior':\n", "        print(\"  (Predicts class probabilities from training set)\")\n", "\n", "# Compare with <PERSON><PERSON>\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"NAIVE BAYES Performance:\")\n", "print(f\"  Accuracy:  {test_metrics['accuracy']:.3f} ({test_metrics['accuracy']*100:.1f}%)\")\n", "print(f\"  F1-Score:  {test_metrics['f1']:.3f}\")\n", "\n", "# Calculate improvement\n", "best_baseline_acc = max(r['accuracy'] for r in baseline_results.values())\n", "best_baseline_f1 = max(r['f1'] for r in baseline_results.values())\n", "\n", "acc_improvement = test_metrics['accuracy'] - best_baseline_acc\n", "f1_improvement = test_metrics['f1'] - best_baseline_f1\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"PERFORMANCE VALIDATION:\")\n", "print(f\"Accuracy improvement over best baseline: {acc_improvement*100:.1f}%\")\n", "print(f\"F1-Score improvement over best baseline: {f1_improvement*100:.1f}%\")\n", "\n", "if acc_improvement > 0.10:\n", "    print(\"\\n✓ Excellent! Model significantly outperforms baselines\")\n", "elif acc_improvement > 0.05:\n", "    print(\"\\n✓ Good! Model clearly beats baseline strategies\")\n", "else:\n", "    print(\"\\n⚠ Warning: Model shows minimal improvement over baselines\")"]}, {"cell_type": "markdown", "id": "separator11", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "pipeline_section", "metadata": {}, "source": ["# <a id='toc11_'></a>[Complete Pipeline Implementation](#toc0_)\n", "\n", "We create a complete pipeline that combines vectorization and classification for easier deployment and use."]}, {"cell_type": "markdown", "id": "pipeline_creation_section", "metadata": {}, "source": ["## <a id='toc11_1_'></a>[Creating the Pipeline](#toc0_)"]}, {"cell_type": "code", "execution_count": 38, "id": "pipeline_creation", "metadata": {}, "outputs": [], "source": ["# Create a complete pipeline combining vectorization and classification\n", "# - Class: Pipeline chains preprocessing and modeling steps\n", "# - Benefits:\n", "#   1. Prevents data leakage\n", "#   2. Simplifies deployment\n", "#   3. Ensures consistent preprocessing\n", "# - Steps:\n", "#   - ('vectorizer', CountVectorizer): Text to numerical transformation\n", "#   - ('classifier', MultinomialNB): <PERSON><PERSON> classification\n", "pipeline = Pipeline([\n", "    ('vectorizer', CountVectorizer()),\n", "    ('classifier', MultinomialNB()\n", "    )\n", "])\n"]}, {"cell_type": "code", "execution_count": 40, "id": "137a1cb5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pipeline Implementation Results:\n", "==================================================\n", "Pipeline Accuracy: 0.9839 (98.39%)\n", "Original Accuracy: 0.9865 (98.65%)\n", "Difference: 0.002691\n"]}], "source": ["# Train the complete pipeline\n", "# - Method: fit() trains both vectorizer and classifier\n", "# - Process:\n", "#   1. Vectorizer learns vocabulary from training text\n", "#   2. Text is transformed to numerical features\n", "#   3. Classifier learns from transformed features\n", "pipeline.fit(X_train, y_train)\n", "\n", "# Make predictions using the pipeline\n", "# - Method: predict() applies all transformations and classification\n", "y_pred_pipeline = pipeline.predict(X_test)\n", "\n", "# Evaluate pipeline performance\n", "pipeline_accuracy = accuracy_score(y_test, y_pred_pipeline)\n", "print(\"Pipeline Implementation Results:\")\n", "print(\"=\" * 50)\n", "print(f\"Pipeline Accuracy: {pipeline_accuracy:.4f} ({pipeline_accuracy*100:.2f}%)\")\n", "print(f\"Original Accuracy: {test_metrics['accuracy']:.4f} ({test_metrics['accuracy']*100:.2f}%)\")\n", "print(f\"Difference: {abs(pipeline_accuracy - test_metrics['accuracy']):.6f}\")\n", "\n", "if abs(pipeline_accuracy - test_metrics['accuracy']) < 0.001:\n", "    print(\"✓ Pipeline produces identical results - implementation verified!\")\n", "\n"]}, {"cell_type": "markdown", "id": "pipeline_testing_section", "metadata": {}, "source": ["## <a id='toc11_2_'></a>[Testing with New Examples](#toc0_)\n", "\n", "Let's test the pipeline with new messages to see how it performs on realistic examples."]}, {"cell_type": "code", "execution_count": 41, "id": "pipeline_testing", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing Pipeline with New Messages:\n", "================================================================================\n", "Message | Expected | Predicted | Confidence | Status\n", "--------------------------------------------------------------------------------\n", "Congratulations! You've won a free iPhon... | spam     | spam      | 100.0% | ✓\n", "URGENT: Your account will be suspended. ... | spam     | spam      | 98.4% | ✓\n", "Get rich quick! Make $5000 per week from... | spam     | spam      | 63.4% | ✓\n", "XXX Adult content. Must be 18+. Reply ST... | spam     | spam      | 100.0% | ✓\n", "You have won £1000000 in the UK lottery!... | spam     | spam      | 97.8% | ✓\n", "Hey, are we still meeting for lunch tomo... | ham      | ham       | 100.0% | ✓\n", "Can you pick up milk on your way home?      | ham      | ham       | 100.0% | ✓\n", "Meeting rescheduled to 3pm. See you in t... | ham      | ham       | 100.0% | ✓\n", "Happy birthday! Hope you have a wonderfu... | ham      | ham       | 100.0% | ✓\n", "Thanks for your help with the project ye... | ham      | ham       | 98.3% | ✓\n", "\n", "Accuracy on new examples: 100.0% (10/10)\n"]}], "source": ["# Test with new example messages\n", "test_messages = [\n", "    # Spam examples\n", "    \"Congratulations! You've won a free iPhone. Click here to claim.\",\n", "    \"URGENT: Your account will be suspended. Verify now!\",\n", "    \"Get rich quick! Make $5000 per week from home!\",\n", "    \"XXX Adult content. Must be 18+. Reply STOP to unsubscribe.\",\n", "    \"You have won £1000000 in the UK lottery! Call now!\",\n", "    \n", "    # Ham examples\n", "    \"Hey, are we still meeting for lunch tomorrow?\",\n", "    \"Can you pick up milk on your way home?\",\n", "    \"Meeting rescheduled to 3pm. See you in the conference room.\",\n", "    \"Happy birthday! Hope you have a wonderful day.\",\n", "    \"Thanks for your help with the project yesterday.\"\n", "]\n", "\n", "# Expected labels for validation\n", "expected_labels = ['spam', 'spam', 'spam', 'spam', 'spam',\n", "                  'ham', 'ham', 'ham', 'ham', 'ham']\n", "\n", "print(\"\\nTesting Pipeline with New Messages:\")\n", "print(\"=\" * 80)\n", "print(\"Message | Expected | Predicted | Confidence | Status\")\n", "print(\"-\" * 80)\n", "\n", "correct_predictions = 0\n", "\n", "for msg, expected in zip(test_messages, expected_labels):\n", "    # Make prediction\n", "    prediction = pipeline.predict([msg])[0]\n", "    prob = pipeline.predict_proba([msg])[0]\n", "    confidence = max(prob)\n", "    \n", "    # Check if correct\n", "    is_correct = prediction == expected\n", "    if is_correct:\n", "        correct_predictions += 1\n", "    status = \"✓\" if is_correct else \"✗\"\n", "    \n", "    # Display results\n", "    msg_preview = msg[:40] + \"...\" if len(msg) > 40 else msg\n", "    print(f\"{msg_preview:43} | {expected:8} | {prediction:9} | {confidence:.1%} | {status}\")\n", "\n", "# Summary\n", "accuracy_new = correct_predictions / len(test_messages)\n", "print(f\"\\nAccuracy on new examples: {accuracy_new:.1%} ({correct_predictions}/{len(test_messages)})\")"]}, {"cell_type": "markdown", "id": "separator12", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "hyperparameter_section", "metadata": {}, "source": ["# <a id='toc12_'></a>[Hyperparameter Tuning](#toc0_)\n", "\n", "We optimize the alpha (smoothing) parameter using cross-validation to find the best configuration."]}, {"cell_type": "markdown", "id": "alpha_optimization_section", "metadata": {}, "source": ["## <a id='toc12_1_'></a>[Alpha Parameter Optimization](#toc0_)\n", "\n", "The alpha parameter controls Laplace smoothing:\n", "- **alpha = 0**: No smoothing (can cause zero probabilities)\n", "- **alpha = 1**: Laplace smoothing (default)\n", "- **alpha > 1**: Stronger smoothing (more conservative)"]}, {"cell_type": "code", "execution_count": 42, "id": "hyperparameter_tuning", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finding Optimal Alpha using Cross-Validation\n", "==================================================\n", "Alpha= 0.001: Mean F1 = 0.9821 (+/- 0.0041)\n", "Alpha= 0.010: Mean F1 = 0.9828 (+/- 0.0050)\n", "Alpha= 0.100: Mean F1 = 0.9846 (+/- 0.0045)\n", "Alpha= 0.500: Mean F1 = 0.9861 (+/- 0.0036)\n", "Alpha= 1.000: Mean F1 = 0.9865 (+/- 0.0022)\n", "Alpha= 2.000: Mean F1 = 0.9866 (+/- 0.0012)\n", "Alpha= 5.000: Mean F1 = 0.9837 (+/- 0.0032)\n", "Alpha=10.000: Mean F1 = 0.9779 (+/- 0.0040)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAJOCAYAAACqS2TfAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAC4pElEQVR4nOzdeXhMZ/sH8O8smex7SBCEiC22SJRaaw0SqV1bpW/VUhGlFNUWLUVtVfvS8kOrrbV25UWLehFaS1FbEkuEyL5MMvv8/khympGEScxksnw/1+Uyc84zc+4zy<PERSON><PERSON>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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Optimal Alpha: 2.0\n", "Best Cross-Validation F1 Score: 0.9866\n"]}], "source": ["# Test different alpha values using cross-validation\n", "print(\"Finding Optimal Alpha using Cross-Validation\")\n", "print(\"=\" * 50)\n", "\n", "# Define range of alpha values to test\n", "alpha_values = [0.001, 0.01, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0]\n", "cv_scores_mean = []\n", "cv_scores_std = []\n", "\n", "# Evaluate each alpha value\n", "for alpha in alpha_values:\n", "    # Create pipeline with current alpha\n", "    pipeline_cv = Pipeline([\n", "        ('vectorizer', CountVectorizer(stop_words='english', max_features=5000)),\n", "        ('classifier', MultinomialNB(alpha=alpha))\n", "    ])\n", "    \n", "    # Perform 5-fold cross-validation\n", "    # Use 'f1_weighted' for imbalanced binary classification\n", "    scores = cross_val_score(pipeline_cv, X_train, y_train, cv=5, scoring='f1_weighted')\n", "    cv_scores_mean.append(scores.mean())\n", "    cv_scores_std.append(scores.std())\n", "    \n", "    print(f\"Alpha={alpha:6.3f}: Mean F1 = {scores.mean():.4f} (+/- {scores.std():.4f})\")\n", "\n", "# Find optimal alpha\n", "optimal_alpha = alpha_values[cv_scores_mean.index(max(cv_scores_mean))]\n", "best_score = max(cv_scores_mean)\n", "\n", "# Visualize results\n", "plt.figure(figsize=(10, 6))\n", "plt.errorbar(alpha_values, cv_scores_mean, yerr=cv_scores_std,\n", "            marker='o', capsize=5, capthick=2, linewidth=2)\n", "plt.xlabel('Alpha (Smoothing Parameter)', fontsize=12)\n", "plt.ylabel('Cross-Validation F1 Score', fontsize=12)\n", "plt.title('Hyperparameter Tuning: Finding Optimal Alpha', fontsize=14, fontweight='bold')\n", "plt.xscale('log')\n", "plt.grid(True, alpha=0.3)\n", "plt.axvline(x=optimal_alpha, color='red', linestyle='--', alpha=0.7,\n", "           label=f'Optimal Alpha={optimal_alpha}')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nOptimal Alpha: {optimal_alpha}\")\n", "print(f\"Best Cross-Validation F1 Score: {best_score:.4f}\")"]}, {"cell_type": "markdown", "id": "retrain_section", "metadata": {}, "source": ["## <a id='toc12_2_'></a>[Retrain with Optimal Parameters](#toc0_)"]}, {"cell_type": "code", "execution_count": 43, "id": "retrain_optimal", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Optimized Model Performance:\n", "Alpha = 2.0\n", "Test Accuracy: 0.9839 (98.39%)\n", "Test F1-Score: 0.9384\n", "\n", "Comparison with <PERSON><PERSON><PERSON> <PERSON>:\n", "Original Accuracy: 0.9865\n", "Optimized Accuracy: 0.9839\n", "Improvement: -0.27%\n"]}], "source": ["# Retrain with optimal alpha\n", "pipeline_optimal = Pipeline([\n", "    ('vectorizer', CountVectorizer(\n", "        stop_words='english',\n", "        lowercase=True,\n", "        max_features=5000\n", "    )),\n", "    ('classifier', MultinomialNB(\n", "        alpha=optimal_alpha,\n", "        fit_prior=True\n", "    ))\n", "])\n", "\n", "# Train the optimized pipeline\n", "pipeline_optimal.fit(X_train, y_train)\n", "\n", "# Evaluate on test set\n", "y_test_pred_optimal = pipeline_optimal.predict(X_test)\n", "test_accuracy_optimal = accuracy_score(y_test, y_test_pred_optimal)\n", "test_f1_optimal = f1_score(y_test, y_test_pred_optimal, pos_label='spam')\n", "\n", "print(f\"\\nOptimized Model Performance:\")\n", "print(f\"Alpha = {optimal_alpha}\")\n", "print(f\"Test Accuracy: {test_accuracy_optimal:.4f} ({test_accuracy_optimal*100:.2f}%)\")\n", "print(f\"Test F1-Score: {test_f1_optimal:.4f}\")\n", "print(f\"\\nComparison with Default Model:\")\n", "print(f\"Original Accuracy: {test_metrics['accuracy']:.4f}\")\n", "print(f\"Optimized Accuracy: {test_accuracy_optimal:.4f}\")\n", "print(f\"Improvement: {(test_accuracy_optimal - test_metrics['accuracy'])*100:+.2f}%\")"]}, {"cell_type": "markdown", "id": "separator13", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "a09f8997", "metadata": {}, "source": ["# <a id='toc13_'></a>[Saving and Loading the Model](#toc0_)\n", "\n", "We save the optimized model pipeline for future deployment."]}, {"cell_type": "markdown", "id": "saving_section", "metadata": {}, "source": ["## <a id='toc13_1_'></a>[Saving the Model Package](#toc0_)"]}, {"cell_type": "code", "execution_count": 44, "id": "0633d250", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model package saved to 'spam_classifier_naive_bayes.joblib'\n", "\n", "Saved components:\n", "  - pipeline\n", "  - vectorizer_params\n", "  - model_params\n", "  - performance_metrics\n", "  - training_info\n", "\n", "File size: 275.97 KB\n"]}], "source": ["# Create a comprehensive dictionary to store all model components and assign to 'model_package'\n", "# - Purpose: Package all necessary components for model deployment\n", "# - Contents include model, metrics, configuration, and metadata\n", "model_package = {\n", "    'pipeline': pipeline_optimal,\n", "    'vectorizer_params': {\n", "        'stop_words': 'english',\n", "        'max_features': 5000,\n", "        'min_df': 2,\n", "        'max_df': 0.95\n", "    },\n", "    'model_params': {\n", "        'alpha': optimal_alpha,\n", "        'fit_prior': True\n", "    },\n", "    'performance_metrics': {\n", "        'test_accuracy': test_accuracy_optimal,\n", "        'test_f1': test_f1_optimal,\n", "        'test_precision': precision_score(y_test, y_test_pred_optimal, pos_label='spam'),\n", "        'test_recall': recall_score(y_test, y_test_pred_optimal, pos_label='spam')\n", "    },\n", "    'training_info': {\n", "        'train_size': len(X_train),\n", "        'test_size': len(X_test),\n", "        'vocabulary_size': len(pipeline_optimal.named_steps['vectorizer'].vocabulary_),\n", "        'classes': list(pipeline_optimal.named_steps['classifier'].classes_)\n", "    },\n", "    'classification_report': classification_report(y_test, y_test_pred_optimal, \n", "                                                  target_names=['Ham', 'Spam'])\n", "}\n", "\n", "# Define filename for saving\n", "model_filename = 'spam_classifier_naive_bayes.joblib'\n", "\n", "# Save the model package to disk\n", "# - Function: joblib.dump() efficiently serializes Python objects\n", "# - Parameters: object to save, filename\n", "# - Benefits: Preserves scikit-learn object structure\n", "joblib.dump(model_package, model_filename)\n", "\n", "print(f\"Model package saved to '{model_filename}'\")\n", "print(\"\\nSaved components:\")\n", "for key in model_package.keys():\n", "    if key != 'classification_report':\n", "        print(f\"  - {key}\")\n", "\n", "import os\n", "if os.path.exists(model_filename):\n", "    file_size = os.path.getsize(model_filename) / 1024\n", "    print(f\"\\nFile size: {file_size:.2f} KB\")"]}, {"cell_type": "markdown", "id": "loading_section", "metadata": {}, "source": ["## <a id='toc13_2_'></a>[Loading the Model Package](#toc0_)"]}, {"cell_type": "code", "execution_count": 45, "id": "loading_model", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded successfully!\n", "\n", "Loaded Model Information:\n", "==================================================\n", "Model accuracy: 0.9839\n", "Model F1-score: 0.9384\n", "Model parameters: Alpha = 2.0\n", "Vocabulary size: 5000\n", "Training samples: 4457\n"]}], "source": ["# Load the saved model package\n", "# - Function: joblib.load() deserializes saved objects\n", "# - Parameters: filename to load\n", "# - Returns: Original Python object (dictionary with all components)\n", "loaded_package = joblib.load(model_filename)\n", "\n", "# Extract components from loaded package\n", "loaded_pipeline = loaded_package['pipeline']\n", "loaded_metrics = loaded_package['performance_metrics']\n", "loaded_info = loaded_package['training_info']\n", "\n", "print(\"Model loaded successfully!\")\n", "print(\"\\nLoaded Model Information:\")\n", "print(\"=\" * 50)\n", "print(f\"Model accuracy: {loaded_metrics['test_accuracy']:.4f}\")\n", "print(f\"Model F1-score: {loaded_metrics['test_f1']:.4f}\")\n", "print(f\"Model parameters: Alpha = {loaded_package['model_params']['alpha']}\")\n", "print(f\"Vocabulary size: {loaded_info['vocabulary_size']}\")\n", "print(f\"Training samples: {loaded_info['train_size']}\")"]}, {"cell_type": "markdown", "id": "using_section", "metadata": {}, "source": ["## <a id='toc13_3_'></a>[Using the Loaded Model](#toc0_)\n", "\n", "Demonstrate how to use the loaded model for spam detection."]}, {"cell_type": "code", "execution_count": 46, "id": "using_model", "metadata": {}, "outputs": [], "source": ["# Create a function for easy spam detection\n", "def detect_spam(message, pipeline=loaded_pipeline, threshold=0.5):\n", "    \"\"\"\n", "    Detect if a message is spam using the loaded model\n", "    \n", "    Parameters:\n", "    - message: Text message to classify\n", "    - pipeline: Loaded model pipeline\n", "    - threshold: Probability threshold for spam classification\n", "    \n", "    Returns:\n", "    - dict: Classification results with prediction and confidence\n", "    \"\"\"\n", "    # Make prediction\n", "    prediction = pipeline.predict([message])[0]\n", "    probabilities = pipeline.predict_proba([message])[0]\n", "    \n", "    # Get spam probability (assuming 'spam' is at index 1)\n", "    spam_prob = probabilities[1]\n", "    ham_prob = probabilities[0]\n", "    \n", "    return {\n", "        'message': message,\n", "        'prediction': prediction,\n", "        'spam_probability': spam_prob,\n", "        'ham_probability': ham_prob,\n", "        'confidence': max(spam_prob, ham_prob),\n", "        'is_spam': spam_prob > threshold\n", "    }\n", "\n"]}, {"cell_type": "code", "execution_count": 47, "id": "4c6ef4f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Spam Detection Results:\n", "================================================================================\n", "\n", "Message: Free money! Click here to get rich quick!...\n", "Prediction: HAM\n", "Spam Probability: 22.05%\n", "Confidence: 77.95%\n", "✓ This message appears to be legitimate.\n", "----------------------------------------\n", "\n", "Message: Hey, can you pick up some groceries on your way ho...\n", "Prediction: HAM\n", "Spam Probability: 0.00%\n", "Confidence: 100.00%\n", "✓ This message appears to be legitimate.\n", "----------------------------------------\n", "\n", "Message: URGENT: Your account will be closed. Act now!...\n", "Prediction: SPAM\n", "Spam Probability: 70.16%\n", "Confidence: 70.16%\n", "⚠ WARNING: This message is likely SPAM!\n", "----------------------------------------\n"]}], "source": ["# Test the function with sample messages\n", "sample_messages = [\n", "    \"Free money! Click here to get rich quick!\",\n", "    \"Hey, can you pick up some groceries on your way home?\",\n", "    \"URGENT: Your account will be closed. Act now!\"\n", "]\n", "\n", "print(\"\\nSpam Detection Results:\")\n", "print(\"=\" * 80)\n", "\n", "for message in sample_messages:\n", "    result = detect_spam(message)\n", "    \n", "    print(f\"\\nMessage: {result['message'][:50]}...\")\n", "    print(f\"Prediction: {result['prediction'].upper()}\")\n", "    print(f\"Spam Probability: {result['spam_probability']:.2%}\")\n", "    print(f\"Confidence: {result['confidence']:.2%}\")\n", "    \n", "    if result['is_spam']:\n", "        print(\"⚠ WARNING: This message is likely SPAM!\")\n", "    else:\n", "        print(\"✓ This message appears to be legitimate.\")\n", "    print(\"-\" * 40)"]}, {"cell_type": "markdown", "id": "separator14", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "summary_section", "metadata": {}, "source": ["# <a id='toc14_'></a>[Summary](#toc0_)\n", "\n", "## Model Performance Summary\n", "\n", "Our Multinomial Naive Bayes spam classifier achieved excellent results:\n", "\n", "- **High Accuracy**: Successfully classifies the vast majority of messages correctly\n", "- **Strong Precision**: Minimizes false positives (legitimate messages marked as spam)\n", "- **Good Recall**: Catches most spam messages effectively\n", "- **Fast Processing**: Can classify thousands of messages per second\n", "- **Robust Performance**: Significantly outperforms baseline models\n", "\n", "## Key Implementation Insights\n", "\n", "1. **Text Preprocessing**: Stop word removal and vocabulary limitation improve performance\n", "2. **<PERSON><PERSON> Smoothing**: Essential for handling unseen words in new messages\n", "3. **Pipeline Architecture**: Ensures consistent preprocessing and prevents data leakage\n", "4. **Hyperparameter Tuning**: Optimal alpha value improves model performance\n", "5. **Evaluation Strategy**: Multiple metrics provide comprehensive performance assessment"]}, {"cell_type": "markdown", "id": "assumptions_content", "metadata": {}, "source": ["## <a id='toc14_1_'></a>[Assumptions of Naive <PERSON>](#toc0_)\n", "\n", "**1. Feature Independence**\n", "- Assumes all features (words) are conditionally independent given the class\n", "- In reality, words are often correlated (e.g., \"free\" and \"offer\")\n", "- Despite this violation, often works well in practice\n", "\n", "**2. Bag of Words**\n", "- Word order is ignored (\"dog bites man\" = \"man bites dog\")\n", "- No semantic understanding of context\n", "- Works because spam often contains distinctive vocabulary\n", "\n", "**3. Feature Distribution**\n", "- Multinomial NB assumes word counts follow multinomial distribution\n", "- Appropriate for text classification with word frequencies\n", "- Different variants assume different distributions\n", "\n", "---\n", "\n", "## <a id='toc14_2_'></a>[Limitations](#toc0_)\n", "\n", "**1. Independence Assumption Rarely Holds**\n", "- Words in language are inherently dependent\n", "- Can lead to overconfident probability estimates\n", "- May miss complex relationships between features\n", "\n", "**2. Zero Frequency Problem**\n", "- Without smoothing, unseen words cause zero probabilities\n", "- Requires Laplace/Lidstone smoothing\n", "- Smoothing can dilute the impact of genuinely predictive features\n", "\n", "**3. Imbalanced Data Sensitivity**\n", "- Prior probabilities directly affect predictions\n", "- May be biased toward majority class\n", "- Requires careful handling of class imbalance\n", "\n", "**4. Context and Semantics**\n", "- Cannot understand word context or meaning\n", "- Treats \"not spam\" same as \"spam not\"\n", "- May miss sarcasm, negation, or subtle patterns\n", "\n", "**5. Continuous Features Challenge**\n", "- Multinomial NB works with discrete features\n", "- Continuous features require Gaussian NB or discretization\n", "- Mixed feature types complicate implementation\n", "\n", "---\n", "\n", "## <a id='toc14_3_'></a>[Best Use Cases](#toc0_)\n", "\n", "**1. Text Classification**\n", "- Email spam filtering (as demonstrated)\n", "- Document categorization\n", "- Sentiment analysis\n", "- Language detection\n", "- News article classification\n", "\n", "**2. Real-Time Prediction Systems**\n", "- Very fast training and prediction\n", "- Low memory requirements\n", "- Suitable for streaming applications\n", "- Mobile and embedded systems\n", "\n", "**3. Multi-Class Classification**\n", "- Natural extension to multiple classes\n", "- Efficient probability calculation\n", "- Works well with many categories\n", "\n", "**4. High-Dimensional Data**\n", "- Handles thousands of features well\n", "- Doesn't suffer from curse of dimensionality like KNN\n", "- Effective with sparse data (text)\n", "\n", "**5. Baseline Models**\n", "- Quick to implement and train\n", "- Provides performance benchmark\n", "- Good starting point for classification projects\n", "\n", "**6. Small Training Sets**\n", "- Requires less training data than discriminative models\n", "- Converges faster than logistic regression\n", "- Good for initial prototypes\n", "\n", "## When to <PERSON>ose <PERSON>\n", "\n", "✅ **Cho<PERSON> when:**\n", "- Working with text classification\n", "- Need fast, scalable solution\n", "- Have limited training data\n", "- Interpretability is important\n", "- Building a baseline model\n", "\n", "❌ **Avoid Naive <PERSON> when:**\n", "- Features are strongly correlated\n", "- Need to capture feature interactions\n", "- Context and word order matter\n", "- Working with continuous features primarily\n", "- Probability calibration is critical"]}, {"cell_type": "markdown", "id": "separator15", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}