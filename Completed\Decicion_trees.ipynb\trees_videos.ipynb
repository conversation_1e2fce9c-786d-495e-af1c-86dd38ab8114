{"cells": [{"cell_type": "markdown", "id": "aa4a253f", "metadata": {}, "source": ["# <a id='toc1_'></a>[Decision Trees Videos](#toc0_)"]}, {"cell_type": "markdown", "id": "c0392de0", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "b4a8267c", "metadata": {}, "source": ["# TASK\n", "\n", " Watch the below videos and make notes and take sreenshots about them in this Jupiter Notebook. If you want you can use AI for the Python code for visualizations of the concepts. After adding your notes, deliver this Jupiter Notebook to the delivery folder in Learn. "]}, {"cell_type": "markdown", "id": "28900224", "metadata": {}, "source": ["[Theory video - Decision and Classification Trees, Clearly Explained!!!](https://www.youtube.com/watch?v=_L39rN6gz7Y&list=PLblh5JKOoLUKAtDViTvRGFpphEc24M-QH&index=2)\n", "\n", "[Theory video - Decision Trees, Part 2 - Feature Selection and Missing Data](https://www.youtube.com/watch?v=wpNl-JwwplA&list=PLblh5JKOoLUKAtDViTvRGFpphEc24M-QH&index=4)\n", "\n", "[Theory video - Regression Trees, Clearly Explained!!!](https://www.youtube.com/watch?v=g9c66TUylZ4&list=PLblh5JKOoLUKAtDViTvRGFpphEc24M-QH&index=4)\n", "\n", "[Theory video - How to Prune Regression Trees, Clearly Explained!!!](https://www.youtube.com/watch?v=D0efHEJsfHo&list=PLblh5JKOoLUKAtDViTvRGFpphEc24M-QH&index=7)\n"]}, {"cell_type": "markdown", "id": "e7594808", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "6006c70a", "metadata": {}, "source": ["# Decision Trees\n", "\n", "## Fundamental Concepts\n", "\n", "- **Definition:** Decision trees are sophisticated hierarchical structures that enable decision-making processes through a systematic evaluation of feature values, allowing for both classification and regression tasks\n", "- **Types:**\n", "    - Classification trees: Categorize data into discrete groups based on feature patterns and decision rules, often used for categorical target variables\n", "    - Regression trees: Predict continuous numeric values by partitioning the feature space into regions with similar target values\n", "- **Structure:**\n", "    - Root node: Starting point of the decision process where the initial feature evaluation occurs\n", "    - Internal nodes: Decision points based on feature values that direct the flow to subsequent nodes based on specific conditions\n", "    - Branches: Connections representing possible outcomes from each decision point, typically binary (yes/no) but can be multi-way\n", "    - Leaf nodes: Terminal nodes containing final predictions where no further splitting occurs and the model provides its output\n", "- **Decision flow:** True statements typically branch left, false statements branch right, following a convention that enhances readability and interpretation\n", "\n", "## Building Decision Trees\n", "\n", "- **Process:** Tree construction uses recursive partitioning (top-down approach), systematically dividing the dataset into increasingly homogeneous subsets\n", "- **Splitting Criteria:**\n", "    - For classification: Gini impurity or Information gain/Entropy methods that quantify the homogeneity of classes within nodes\n", "    - For regression: Sum of squared residuals (SSR) or variance reduction that minimize prediction errors within resulting nodes\n", "- **Gini Impurity:** Measures how mixed the classifications are within a node, providing a quantitative metric for node purity\n", "    - Formula: 1 - Σ(pi²) where pi is the probability of class i, representing the probability of misclassification\n", "    - Lower values indicate better splits (more homogeneous nodes), with splits chosen to maximize the reduction in impurity\n", "    - Perfect split (pure node) has Gini impurity of 0, indicating all samples belong to the same class\n", "    - Example: For a node with 70% class A and 30% class B, Gini = 1 - (0.7² + 0.3²) = 1 - (0.49 + 0.09) = 0.42, indicating moderate impurity\n", "- **Split selection process:**\n", "    - Evaluate all possible feature splits at each node, considering every potential division point\n", "    - Calculate impurity/error for each potential split, measuring the resulting homogeneity after division\n", "    - Choose the feature with lowest weighted average impurity across resulting child nodes\n", "    - Create child nodes and repeat recursively, building the tree structure level by level\n", "    - Stop when reaching stopping criteria (e.g., maximum depth, minimum samples) to prevent overfitting\n", "- **Prediction at leaf nodes:**\n", "    - Classification: Majority class in that leaf, representing the most common class among training samples reaching that node\n", "    - Regression: Average value of training instances in that leaf, minimizing the squared error for prediction\n", "\n", "## Handling Different Data Types\n", "\n", "- **Categorical features:**\n", "    - Binary splits (one value vs. all others) or multi-way splits depending on the algorithm implementation and feature cardinality\n", "    - One-hot encoding sometimes used for preprocessing to transform categorical variables into a format suitable for certain algorithms\n", "- **Numerical features:**\n", "    - Binary splits based on thresholds (e.g., age > 50), creating decision boundaries at specific feature values\n", "    - Sort values from lowest to highest to establish a systematic approach to finding optimal thresholds\n", "    - Calculate thresholds between adjacent values, typically as the midpoint between consecutive distinct values\n", "    - Test each threshold to find optimal split point that maximizes information gain or minimizes impurity\n", "\n", "## Handling Missing Data\n", "\n", "- **Common approaches:**\n", "    - Excluding instances with missing values (basic approach), though this can reduce available training data\n", "    - Using surrogate splits based on correlated features when primary split features have missing values\n", "    - Treating missing as a separate category, creating an additional branch specifically for missing values\n", "    - Imputation: Replace with most common value (categorical) or mean/median (numerical) before tree construction\n", "    - Using regression with correlated features for numerical data to estimate missing values based on other available features\n", "    - Sending instances with missing values down both paths and averaging results, weighted by the probability of each path\n", "\n", "## Preventing Overfitting\n", "\n", "- **Overfitting indicators:** Small leaves with few samples, high training accuracy but poor test accuracy, excessively complex tree structure\n", "- **Pre-pruning methods:**\n", "    - Maximum depth: Limit how deep tree can grow, preventing excessive complexity by restricting the number of sequential splits\n", "    - Minimum samples per split/leaf: Ensure sufficient samples for each decision, preventing splits that would result in statistically unreliable nodes\n", "    - Minimum impurity decrease: Only split if it improves impurity by a threshold, avoiding splits with minimal information gain\n", "- **Post-pruning methods:**\n", "    - Cost-complexity pruning: Remove branches that don't significantly improve performance, balancing model complexity and accuracy\n", "    - Reduced-error pruning: Use validation set to evaluate node removal, iteratively removing nodes that don't decrease validation accuracy\n", "- **Cost-Complexity Pruning Process:**\n", "    - Calculate tree score: SSR + α × t (where t is number of leaves), incorporating both prediction error and model complexity\n", "    - Use cross-validation to find optimal α value, testing different complexity parameters to maximize validation performance\n", "    - Select final pruned tree using optimal α, applying the identified complexity parameter to the full tree\n", "\n", "## Feature Selection and Importance\n", "\n", "- **Automatic selection:** Trees only include features that reduce impurity, naturally performing feature selection during the training process\n", "- **Feature importance calculation:**\n", "    - Based on total reduction in impurity contributed by each feature across all nodes where that feature is used\n", "    - Features used higher in the tree typically have greater importance as they affect more samples and make more significant splits\n", "    - Importance can be normalized to sum to 1 or 100%, providing relative comparison between features in the model\n", "- **Thresholding:** Setting minimum impurity reduction creates simpler trees by eliminating features with minimal predictive power\n", "\n", "## Classification Process for New Data\n", "\n", "- **Prediction Steps:**\n", "    - Start at root node and follow decision path based on feature values, evaluating each condition sequentially\n", "    - Continue until reaching a leaf node, traversing the tree structure according to the sample's characteristics\n", "    - Output prediction based on majority class or average value stored in the reached leaf node\n", "\n", "## Advantages of Decision Trees\n", "\n", "- Highly interpretable and easy to visualize, allowing stakeholders to understand the decision-making process\n", "- Handle both numerical and categorical data without preprocessing, accommodating various data types naturally\n", "- Require minimal data preprocessing (no normalization or scaling needed), reducing preparation time and complexity\n", "- Automatically perform feature selection during training, identifying and utilizing the most informative variables\n", "- Robust to outliers and missing values, with methods to handle data irregularities effectively\n", "- Can model non-linear relationships between features and target variables without explicit transformation\n", "- Fast prediction time due to simple conditional logic, making them efficient for real-time applications\n", "\n", "## Limitations\n", "\n", "- Prone to overfitting, especially with deep trees that memorize training data rather than generalize\n", "- Can be unstable (small changes in data can result in very different trees), leading to high variance in predictions\n", "- May struggle with highly correlated features, potentially selecting one arbitrarily and ignoring others\n", "- Biased toward features with more levels, as they naturally create more splits and appear more informative\n", "- May create biased trees if classes are imbalanced, favoring majority classes in split decisions\n", "- Often less accurate than more complex models for certain problems, particularly those with subtle patterns\n", "- Cannot easily represent some relationships (e.g., XOR problems) efficiently, requiring deep and complex trees\n", "\n", "## Advanced Concepts\n", "\n", "- **Ensemble methods:** Combining multiple trees for better performance\n", "    - Random Forests: Building many trees with randomized feature subsets to reduce variance and improve generalization\n", "    - Gradient Boosting: Sequential tree building to correct previous errors, focusing on difficult cases iteratively\n", "    - These methods often overcome individual tree limitations while preserving many of their advantages\n", "- **Hyperparameter tuning:** Optimizing tree parameters\n", "    - Maximum depth: Controls tree complexity and is a primary defense against overfitting\n", "    - Minimum samples per split/leaf: Prevents overfitting by ensuring statistical reliability of nodes\n", "    - Maximum number of features: Controls feature randomness particularly important in ensemble methods\n", "    - Splitting criterion: Gini vs. entropy for classification, affecting how homogeneity is measured\n", "    - Alpha parameter: For cost-complexity pruning, balancing model complexity against accuracy"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}