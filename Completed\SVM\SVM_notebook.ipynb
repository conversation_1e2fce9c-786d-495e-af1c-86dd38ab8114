{"cells": [{"cell_type": "markdown", "id": "27b6ad45", "metadata": {}, "source": ["# Support Vector Machines"]}, {"cell_type": "code", "execution_count": 121, "id": "a3f6a760", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from matplotlib import colors\n", "from sklearn.utils import resample\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import scale\n", "from sklearn.svm import SVC\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay\n", "from sklearn.decomposition import PCA"]}, {"cell_type": "markdown", "id": "b5725b59", "metadata": {}, "source": ["**The Dataset needs following dependencies:**\n", "* xlrd. Run: `pip install xlrd`"]}, {"cell_type": "markdown", "id": "6d5b1fcb", "metadata": {}, "source": ["## Data"]}, {"cell_type": "markdown", "id": "45673438", "metadata": {}, "source": ["### Import"]}, {"cell_type": "code", "execution_count": 98, "id": "d9945fde", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ID", "rawType": "int64", "type": "integer"}, {"name": "LIMIT_BAL", "rawType": "int64", "type": "integer"}, {"name": "SEX", "rawType": "int64", "type": "integer"}, {"name": "EDUCATION", "rawType": "int64", "type": "integer"}, {"name": "MARRIAGE", "rawType": "int64", "type": "integer"}, {"name": "AGE", "rawType": "int64", "type": "integer"}, {"name": "PAY_0", "rawType": "int64", "type": "integer"}, {"name": "PAY_2", "rawType": "int64", "type": "integer"}, {"name": "PAY_3", "rawType": "int64", "type": "integer"}, {"name": "PAY_4", "rawType": "int64", "type": "integer"}, {"name": "PAY_5", "rawType": "int64", "type": "integer"}, {"name": "PAY_6", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT1", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT2", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT3", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT4", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT5", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT6", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT1", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT2", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT3", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT4", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT5", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT6", "rawType": "int64", "type": "integer"}, {"name": "default payment next month", "rawType": "int64", "type": "integer"}], "ref": "f04b982c-3381-41d1-8899-9dd5422347cb", "rows": [["0", "1", "20000", "2", "2", "1", "24", "2", "2", "-1", "-1", "-2", "-2", "3913", "3102", "689", "0", "0", "0", "0", "689", "0", "0", "0", "0", "1"], ["1", "2", "120000", "2", "2", "2", "26", "-1", "2", "0", "0", "0", "2", "2682", "1725", "2682", "3272", "3455", "3261", "0", "1000", "1000", "1000", "0", "2000", "1"], ["2", "3", "90000", "2", "2", "2", "34", "0", "0", "0", "0", "0", "0", "29239", "14027", "13559", "14331", "14948", "15549", "1518", "1500", "1000", "1000", "1000", "5000", "0"], ["3", "4", "50000", "2", "2", "1", "37", "0", "0", "0", "0", "0", "0", "46990", "48233", "49291", "28314", "28959", "29547", "2000", "2019", "1200", "1100", "1069", "1000", "0"], ["4", "5", "50000", "1", "2", "1", "57", "-1", "0", "-1", "0", "0", "0", "8617", "5670", "35835", "20940", "19146", "19131", "2000", "36681", "10000", "9000", "689", "679", "0"]], "shape": {"columns": 25, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_0</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>default payment next month</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>20000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>689</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>120000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>26</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>3272</td>\n", "      <td>3455</td>\n", "      <td>3261</td>\n", "      <td>0</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>90000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>14331</td>\n", "      <td>14948</td>\n", "      <td>15549</td>\n", "      <td>1518</td>\n", "      <td>1500</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>5000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>50000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>28314</td>\n", "      <td>28959</td>\n", "      <td>29547</td>\n", "      <td>2000</td>\n", "      <td>2019</td>\n", "      <td>1200</td>\n", "      <td>1100</td>\n", "      <td>1069</td>\n", "      <td>1000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>57</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>20940</td>\n", "      <td>19146</td>\n", "      <td>19131</td>\n", "      <td>2000</td>\n", "      <td>36681</td>\n", "      <td>10000</td>\n", "      <td>9000</td>\n", "      <td>689</td>\n", "      <td>679</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["   ID  LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_0  PAY_2  PAY_3  PAY_4  \\\n", "0   1      20000    2          2         1   24      2      2     -1     -1   \n", "1   2     120000    2          2         2   26     -1      2      0      0   \n", "2   3      90000    2          2         2   34      0      0      0      0   \n", "3   4      50000    2          2         1   37      0      0      0      0   \n", "4   5      50000    1          2         1   57     -1      0     -1      0   \n", "\n", "   ...  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  \\\n", "0  ...          0          0          0         0       689         0   \n", "1  ...       3272       3455       3261         0      1000      1000   \n", "2  ...      14331      14948      15549      1518      1500      1000   \n", "3  ...      28314      28959      29547      2000      2019      1200   \n", "4  ...      20940      19146      19131      2000     36681     10000   \n", "\n", "   PAY_AMT4  PAY_AMT5  PAY_AMT6  default payment next month  \n", "0         0         0         0                           1  \n", "1      1000         0      2000                           1  \n", "2      1000      1000      5000                           0  \n", "3      1100      1069      1000                           0  \n", "4      9000       689       679                           0  \n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["# Credit card clients\n", "\n", "url = 'https://archive.ics.uci.edu/ml/machine-learning-databases/00350/default%20of%20credit%20card%20clients.xls'\n", "\n", "df = pd.read_excel(url, header=1, skiprows=0)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 99, "id": "ac118bf9", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ID", "rawType": "int64", "type": "integer"}, {"name": "LIMIT_BAL", "rawType": "int64", "type": "integer"}, {"name": "SEX", "rawType": "int64", "type": "integer"}, {"name": "EDUCATION", "rawType": "int64", "type": "integer"}, {"name": "MARRIAGE", "rawType": "int64", "type": "integer"}, {"name": "AGE", "rawType": "int64", "type": "integer"}, {"name": "PAY_0", "rawType": "int64", "type": "integer"}, {"name": "PAY_2", "rawType": "int64", "type": "integer"}, {"name": "PAY_3", "rawType": "int64", "type": "integer"}, {"name": "PAY_4", "rawType": "int64", "type": "integer"}, {"name": "PAY_5", "rawType": "int64", "type": "integer"}, {"name": "PAY_6", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT1", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT2", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT3", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT4", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT5", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT6", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT1", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT2", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT3", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT4", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT5", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT6", "rawType": "int64", "type": "integer"}, {"name": "DEFAULT", "rawType": "int64", "type": "integer"}], "ref": "eb2f70cc-8b13-4999-aba4-06dfd44cf524", "rows": [["0", "1", "20000", "2", "2", "1", "24", "2", "2", "-1", "-1", "-2", "-2", "3913", "3102", "689", "0", "0", "0", "0", "689", "0", "0", "0", "0", "1"], ["1", "2", "120000", "2", "2", "2", "26", "-1", "2", "0", "0", "0", "2", "2682", "1725", "2682", "3272", "3455", "3261", "0", "1000", "1000", "1000", "0", "2000", "1"], ["2", "3", "90000", "2", "2", "2", "34", "0", "0", "0", "0", "0", "0", "29239", "14027", "13559", "14331", "14948", "15549", "1518", "1500", "1000", "1000", "1000", "5000", "0"], ["3", "4", "50000", "2", "2", "1", "37", "0", "0", "0", "0", "0", "0", "46990", "48233", "49291", "28314", "28959", "29547", "2000", "2019", "1200", "1100", "1069", "1000", "0"], ["4", "5", "50000", "1", "2", "1", "57", "-1", "0", "-1", "0", "0", "0", "8617", "5670", "35835", "20940", "19146", "19131", "2000", "36681", "10000", "9000", "689", "679", "0"]], "shape": {"columns": 25, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_0</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>DEFAULT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>20000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>689</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>120000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>26</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>3272</td>\n", "      <td>3455</td>\n", "      <td>3261</td>\n", "      <td>0</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>90000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>14331</td>\n", "      <td>14948</td>\n", "      <td>15549</td>\n", "      <td>1518</td>\n", "      <td>1500</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>5000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>50000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>28314</td>\n", "      <td>28959</td>\n", "      <td>29547</td>\n", "      <td>2000</td>\n", "      <td>2019</td>\n", "      <td>1200</td>\n", "      <td>1100</td>\n", "      <td>1069</td>\n", "      <td>1000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>57</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>20940</td>\n", "      <td>19146</td>\n", "      <td>19131</td>\n", "      <td>2000</td>\n", "      <td>36681</td>\n", "      <td>10000</td>\n", "      <td>9000</td>\n", "      <td>689</td>\n", "      <td>679</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["   ID  LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_0  PAY_2  PAY_3  PAY_4  \\\n", "0   1      20000    2          2         1   24      2      2     -1     -1   \n", "1   2     120000    2          2         2   26     -1      2      0      0   \n", "2   3      90000    2          2         2   34      0      0      0      0   \n", "3   4      50000    2          2         1   37      0      0      0      0   \n", "4   5      50000    1          2         1   57     -1      0     -1      0   \n", "\n", "   ...  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  \\\n", "0  ...          0          0          0         0       689         0   \n", "1  ...       3272       3455       3261         0      1000      1000   \n", "2  ...      14331      14948      15549      1518      1500      1000   \n", "3  ...      28314      28959      29547      2000      2019      1200   \n", "4  ...      20940      19146      19131      2000     36681     10000   \n", "\n", "   PAY_AMT4  PAY_AMT5  PAY_AMT6  DEFAULT  \n", "0         0         0         0        1  \n", "1      1000         0      2000        1  \n", "2      1000      1000      5000        0  \n", "3      1100      1069      1000        0  \n", "4      9000       689       679        0  \n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["df.rename({'default payment next month': 'DEFAULT'}, axis=1, inplace=True)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 100, "id": "b1c67b28", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "LIMIT_BAL", "rawType": "int64", "type": "integer"}, {"name": "SEX", "rawType": "int64", "type": "integer"}, {"name": "EDUCATION", "rawType": "int64", "type": "integer"}, {"name": "MARRIAGE", "rawType": "int64", "type": "integer"}, {"name": "AGE", "rawType": "int64", "type": "integer"}, {"name": "PAY_0", "rawType": "int64", "type": "integer"}, {"name": "PAY_2", "rawType": "int64", "type": "integer"}, {"name": "PAY_3", "rawType": "int64", "type": "integer"}, {"name": "PAY_4", "rawType": "int64", "type": "integer"}, {"name": "PAY_5", "rawType": "int64", "type": "integer"}, {"name": "PAY_6", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT1", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT2", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT3", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT4", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT5", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT6", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT1", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT2", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT3", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT4", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT5", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT6", "rawType": "int64", "type": "integer"}, {"name": "DEFAULT", "rawType": "int64", "type": "integer"}], "ref": "0e6d3eef-19ca-42e2-b801-eb41de5f9403", "rows": [["0", "20000", "2", "2", "1", "24", "2", "2", "-1", "-1", "-2", "-2", "3913", "3102", "689", "0", "0", "0", "0", "689", "0", "0", "0", "0", "1"], ["1", "120000", "2", "2", "2", "26", "-1", "2", "0", "0", "0", "2", "2682", "1725", "2682", "3272", "3455", "3261", "0", "1000", "1000", "1000", "0", "2000", "1"], ["2", "90000", "2", "2", "2", "34", "0", "0", "0", "0", "0", "0", "29239", "14027", "13559", "14331", "14948", "15549", "1518", "1500", "1000", "1000", "1000", "5000", "0"], ["3", "50000", "2", "2", "1", "37", "0", "0", "0", "0", "0", "0", "46990", "48233", "49291", "28314", "28959", "29547", "2000", "2019", "1200", "1100", "1069", "1000", "0"], ["4", "50000", "1", "2", "1", "57", "-1", "0", "-1", "0", "0", "0", "8617", "5670", "35835", "20940", "19146", "19131", "2000", "36681", "10000", "9000", "689", "679", "0"]], "shape": {"columns": 24, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_0</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "      <th>DEFAULT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>24</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-2</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>689</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>120000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>26</td>\n", "      <td>-1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>3272</td>\n", "      <td>3455</td>\n", "      <td>3261</td>\n", "      <td>0</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>0</td>\n", "      <td>2000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>90000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>14331</td>\n", "      <td>14948</td>\n", "      <td>15549</td>\n", "      <td>1518</td>\n", "      <td>1500</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>1000</td>\n", "      <td>5000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>28314</td>\n", "      <td>28959</td>\n", "      <td>29547</td>\n", "      <td>2000</td>\n", "      <td>2019</td>\n", "      <td>1200</td>\n", "      <td>1100</td>\n", "      <td>1069</td>\n", "      <td>1000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50000</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>57</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>20940</td>\n", "      <td>19146</td>\n", "      <td>19131</td>\n", "      <td>2000</td>\n", "      <td>36681</td>\n", "      <td>10000</td>\n", "      <td>9000</td>\n", "      <td>689</td>\n", "      <td>679</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["   LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_0  PAY_2  PAY_3  PAY_4  \\\n", "0      20000    2          2         1   24      2      2     -1     -1   \n", "1     120000    2          2         2   26     -1      2      0      0   \n", "2      90000    2          2         2   34      0      0      0      0   \n", "3      50000    2          2         1   37      0      0      0      0   \n", "4      50000    1          2         1   57     -1      0     -1      0   \n", "\n", "   PAY_5  ...  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  PAY_AMT2  PAY_AMT3  \\\n", "0     -2  ...          0          0          0         0       689         0   \n", "1      0  ...       3272       3455       3261         0      1000      1000   \n", "2      0  ...      14331      14948      15549      1518      1500      1000   \n", "3      0  ...      28314      28959      29547      2000      2019      1200   \n", "4      0  ...      20940      19146      19131      2000     36681     10000   \n", "\n", "   PAY_AMT4  PAY_AMT5  PAY_AMT6  DEFAULT  \n", "0         0         0         0        1  \n", "1      1000         0      2000        1  \n", "2      1000      1000      5000        0  \n", "3      1100      1069      1000        0  \n", "4      9000       689       679        0  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["df.drop('ID', axis=1, inplace=True)\n", "df.head()"]}, {"cell_type": "markdown", "id": "d67d7c30", "metadata": {}, "source": ["### Missing data"]}, {"cell_type": "code", "execution_count": 101, "id": "b13eaf14", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "object", "type": "unknown"}], "ref": "57c464a9-a0b1-4978-9b37-96a58c8a0217", "rows": [["LIMIT_BAL", "int64"], ["SEX", "int64"], ["EDUCATION", "int64"], ["MARRIAGE", "int64"], ["AGE", "int64"], ["PAY_0", "int64"], ["PAY_2", "int64"], ["PAY_3", "int64"], ["PAY_4", "int64"], ["PAY_5", "int64"], ["PAY_6", "int64"], ["BILL_AMT1", "int64"], ["BILL_AMT2", "int64"], ["BILL_AMT3", "int64"], ["BILL_AMT4", "int64"], ["BILL_AMT5", "int64"], ["BILL_AMT6", "int64"], ["PAY_AMT1", "int64"], ["PAY_AMT2", "int64"], ["PAY_AMT3", "int64"], ["PAY_AMT4", "int64"], ["PAY_AMT5", "int64"], ["PAY_AMT6", "int64"], ["DEFAULT", "int64"]], "shape": {"columns": 1, "rows": 24}}, "text/plain": ["LIMIT_BAL    int64\n", "SEX          int64\n", "EDUCATION    int64\n", "MARRIAGE     int64\n", "AGE          int64\n", "PAY_0        int64\n", "PAY_2        int64\n", "PAY_3        int64\n", "PAY_4        int64\n", "PAY_5        int64\n", "PAY_6        int64\n", "BILL_AMT1    int64\n", "BILL_AMT2    int64\n", "BILL_AMT3    int64\n", "BILL_AMT4    int64\n", "BILL_AMT5    int64\n", "BILL_AMT6    int64\n", "PAY_AMT1     int64\n", "PAY_AMT2     int64\n", "PAY_AMT3     int64\n", "PAY_AMT4     int64\n", "PAY_AMT5     int64\n", "PAY_AMT6     int64\n", "DEFAULT      int64\n", "dtype: object"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": 102, "id": "aaa6ed3f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2, 1], dtype=int64)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([2, 1, 3, 5, 4, 6, 0], dtype=int64)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([1, 2, 3, 0], dtype=int64)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(df.SEX.unique())\n", "display(df.EDUCATION.unique())\n", "display(df.MARRIAGE.unique())"]}, {"cell_type": "code", "execution_count": 103, "id": "beeb46a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["68"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df.loc[(df.EDUCATION == 0) | (df.MARRIAGE == 0)])"]}, {"cell_type": "code", "execution_count": 104, "id": "59ea49f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["30000"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df)"]}, {"cell_type": "code", "execution_count": 105, "id": "2966c560", "metadata": {}, "outputs": [{"data": {"text/plain": ["29932"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["df_no_missing = df.loc[(df.EDUCATION != 0) & (df.MARRIAGE != 0)]\n", "len(df_no_missing)"]}, {"cell_type": "code", "execution_count": 106, "id": "2e666b11", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2, 1, 3, 5, 4, 6], dtype=int64)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([1, 2, 3], dtype=int64)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(df_no_missing.EDUCATION.unique())\n", "display(df_no_missing.MARRIAGE.unique())"]}, {"cell_type": "markdown", "id": "b562472c", "metadata": {}, "source": ["### Downsample the data"]}, {"cell_type": "code", "execution_count": 107, "id": "c470fbe9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["len(df_no_default_downsampled): 1000\n", "len(df_downsampled): 1000\n"]}], "source": ["df_no_default = df_no_missing[df_no_missing.DEFAULT == 0]\n", "df_default = df_no_missing[df_no_missing.DEFAULT == 1]\n", "\n", "df_no_default_downsampled = resample(df_no_default,\n", "                                     replace=False,\n", "                                     n_samples= 1000,\n", "                                     random_state=42)\n", "print(f'len(df_no_default_downsampled): {len(df_no_default_downsampled)}')\n", "\n", "df_downsampled = resample(df_default,\n", "                          replace=False,\n", "                          n_samples= 1000,\n", "                          random_state=42)\n", "print(f'len(df_downsampled): {len(df_downsampled)}')"]}, {"cell_type": "code", "execution_count": 108, "id": "1c4ca839", "metadata": {}, "outputs": [{"data": {"text/plain": ["2000"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["df_downsample = pd.concat([df_no_default_downsampled, df_downsampled])\n", "len(df_downsample)"]}, {"cell_type": "markdown", "id": "1e1a492e", "metadata": {}, "source": ["## Preprosessing"]}, {"cell_type": "code", "execution_count": 109, "id": "35ce105e", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "LIMIT_BAL", "rawType": "int64", "type": "integer"}, {"name": "SEX", "rawType": "int64", "type": "integer"}, {"name": "EDUCATION", "rawType": "int64", "type": "integer"}, {"name": "MARRIAGE", "rawType": "int64", "type": "integer"}, {"name": "AGE", "rawType": "int64", "type": "integer"}, {"name": "PAY_0", "rawType": "int64", "type": "integer"}, {"name": "PAY_2", "rawType": "int64", "type": "integer"}, {"name": "PAY_3", "rawType": "int64", "type": "integer"}, {"name": "PAY_4", "rawType": "int64", "type": "integer"}, {"name": "PAY_5", "rawType": "int64", "type": "integer"}, {"name": "PAY_6", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT1", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT2", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT3", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT4", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT5", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT6", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT1", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT2", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT3", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT4", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT5", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT6", "rawType": "int64", "type": "integer"}], "ref": "c24117bd-a141-4bcc-b518-0bd39e705b3a", "rows": [["641", "130000", "2", "3", "1", "28", "0", "0", "0", "0", "-2", "-2", "100143", "50456", "50000", "0", "0", "0", "2500", "1000", "0", "0", "0", "0"], ["4678", "170000", "1", "3", "1", "29", "0", "0", "0", "0", "0", "0", "165027", "168990", "172307", "35234", "32869", "33862", "7200", "7500", "1200", "1200", "1500", "1300"], ["16004", "180000", "2", "2", "1", "29", "0", "0", "0", "0", "0", "0", "25781", "26000", "26310", "26662", "26166", "26176", "1800", "1800", "1500", "1056", "950", "1000"], ["22974", "210000", "2", "2", "2", "32", "-2", "-2", "-2", "-2", "-2", "-2", "355", "975", "410", "0", "0", "0", "979", "412", "0", "0", "0", "0"], ["17535", "190000", "2", "3", "1", "45", "0", "0", "0", "0", "0", "0", "76433", "78472", "80548", "81778", "83082", "84811", "3300", "3331", "3359", "2663", "2751", "3000"]], "shape": {"columns": 23, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>SEX</th>\n", "      <th>EDUCATION</th>\n", "      <th>MARRIAGE</th>\n", "      <th>AGE</th>\n", "      <th>PAY_0</th>\n", "      <th>PAY_2</th>\n", "      <th>PAY_3</th>\n", "      <th>PAY_4</th>\n", "      <th>PAY_5</th>\n", "      <th>...</th>\n", "      <th>BILL_AMT3</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>PAY_AMT3</th>\n", "      <th>PAY_AMT4</th>\n", "      <th>PAY_AMT5</th>\n", "      <th>PAY_AMT6</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>641</th>\n", "      <td>130000</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-2</td>\n", "      <td>...</td>\n", "      <td>50000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2500</td>\n", "      <td>1000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4678</th>\n", "      <td>170000</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>172307</td>\n", "      <td>35234</td>\n", "      <td>32869</td>\n", "      <td>33862</td>\n", "      <td>7200</td>\n", "      <td>7500</td>\n", "      <td>1200</td>\n", "      <td>1200</td>\n", "      <td>1500</td>\n", "      <td>1300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16004</th>\n", "      <td>180000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>26310</td>\n", "      <td>26662</td>\n", "      <td>26166</td>\n", "      <td>26176</td>\n", "      <td>1800</td>\n", "      <td>1800</td>\n", "      <td>1500</td>\n", "      <td>1056</td>\n", "      <td>950</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22974</th>\n", "      <td>210000</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>32</td>\n", "      <td>-2</td>\n", "      <td>-2</td>\n", "      <td>-2</td>\n", "      <td>-2</td>\n", "      <td>-2</td>\n", "      <td>...</td>\n", "      <td>410</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>979</td>\n", "      <td>412</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17535</th>\n", "      <td>190000</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>45</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>80548</td>\n", "      <td>81778</td>\n", "      <td>83082</td>\n", "      <td>84811</td>\n", "      <td>3300</td>\n", "      <td>3331</td>\n", "      <td>3359</td>\n", "      <td>2663</td>\n", "      <td>2751</td>\n", "      <td>3000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 23 columns</p>\n", "</div>"], "text/plain": ["       LIMIT_BAL  SEX  EDUCATION  MARRIAGE  AGE  PAY_0  PAY_2  PAY_3  PAY_4  \\\n", "641       130000    2          3         1   28      0      0      0      0   \n", "4678      170000    1          3         1   29      0      0      0      0   \n", "16004     180000    2          2         1   29      0      0      0      0   \n", "22974     210000    2          2         2   32     -2     -2     -2     -2   \n", "17535     190000    2          3         1   45      0      0      0      0   \n", "\n", "       PAY_5  ...  BILL_AMT3  BILL_AMT4  BILL_AMT5  BILL_AMT6  PAY_AMT1  \\\n", "641       -2  ...      50000          0          0          0      2500   \n", "4678       0  ...     172307      35234      32869      33862      7200   \n", "16004      0  ...      26310      26662      26166      26176      1800   \n", "22974     -2  ...        410          0          0          0       979   \n", "17535      0  ...      80548      81778      83082      84811      3300   \n", "\n", "       PAY_AMT2  PAY_AMT3  PAY_AMT4  PAY_AMT5  PAY_AMT6  \n", "641        1000         0         0         0         0  \n", "4678       7500      1200      1200      1500      1300  \n", "16004      1800      1500      1056       950      1000  \n", "22974       412         0         0         0         0  \n", "17535      3331      3359      2663      2751      3000  \n", "\n", "[5 rows x 23 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "DEFAULT", "rawType": "int64", "type": "integer"}], "ref": "66693511-68ab-4de9-b6cd-f71899fae3f7", "rows": [["641", "0"], ["4678", "0"], ["16004", "0"], ["22974", "0"], ["17535", "0"]], "shape": {"columns": 1, "rows": 5}}, "text/plain": ["641      0\n", "4678     0\n", "16004    0\n", "22974    0\n", "17535    0\n", "Name: DEFAULT, dtype: int64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X = df_downsample.drop('DEFAULT', axis=1).copy()\n", "y = df_downsample['DEFAULT'].copy()\n", "\n", "display(X.head())\n", "display(y.head())"]}, {"cell_type": "markdown", "id": "53ffc534", "metadata": {}, "source": ["### Ecoding Data"]}, {"cell_type": "code", "execution_count": 110, "id": "3048f2af", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "LIMIT_BAL", "rawType": "int64", "type": "integer"}, {"name": "AGE", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT1", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT2", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT3", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT4", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT5", "rawType": "int64", "type": "integer"}, {"name": "BILL_AMT6", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT1", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT2", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT3", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT4", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT5", "rawType": "int64", "type": "integer"}, {"name": "PAY_AMT6", "rawType": "int64", "type": "integer"}, {"name": "SEX_1", "rawType": "bool", "type": "boolean"}, {"name": "SEX_2", "rawType": "bool", "type": "boolean"}, {"name": "EDUCATION_1", "rawType": "bool", "type": "boolean"}, {"name": "EDUCATION_2", "rawType": "bool", "type": "boolean"}, {"name": "EDUCATION_3", "rawType": "bool", "type": "boolean"}, {"name": "EDUCATION_4", "rawType": "bool", "type": "boolean"}, {"name": "EDUCATION_5", "rawType": "bool", "type": "boolean"}, {"name": "EDUCATION_6", "rawType": "bool", "type": "boolean"}, {"name": "MARRIAGE_1", "rawType": "bool", "type": "boolean"}, {"name": "MARRIAGE_2", "rawType": "bool", "type": "boolean"}, {"name": "MARRIAGE_3", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_-2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_-1", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_0", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_1", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_3", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_4", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_5", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_6", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_7", "rawType": "bool", "type": "boolean"}, {"name": "PAY_0_8", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_-2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_-1", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_0", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_1", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_3", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_4", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_5", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_6", "rawType": "bool", "type": "boolean"}, {"name": "PAY_2_7", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_-2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_-1", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_0", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_3", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_4", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_5", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_6", "rawType": "bool", "type": "boolean"}, {"name": "PAY_3_7", "rawType": "bool", "type": "boolean"}, {"name": "PAY_4_-2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_4_-1", "rawType": "bool", "type": "boolean"}, {"name": "PAY_4_0", "rawType": "bool", "type": "boolean"}, {"name": "PAY_4_2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_4_3", "rawType": "bool", "type": "boolean"}, {"name": "PAY_4_4", "rawType": "bool", "type": "boolean"}, {"name": "PAY_4_5", "rawType": "bool", "type": "boolean"}, {"name": "PAY_4_7", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_-2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_-1", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_0", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_3", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_4", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_5", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_6", "rawType": "bool", "type": "boolean"}, {"name": "PAY_5_7", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_-2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_-1", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_0", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_2", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_3", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_4", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_5", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_6", "rawType": "bool", "type": "boolean"}, {"name": "PAY_6_7", "rawType": "bool", "type": "boolean"}], "ref": "2d3aa4bf-a597-4b30-845e-e2f04c15e465", "rows": [["641", "130000", "28", "100143", "50456", "50000", "0", "0", "0", "2500", "1000", "0", "0", "0", "0", "False", "True", "False", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["4678", "170000", "29", "165027", "168990", "172307", "35234", "32869", "33862", "7200", "7500", "1200", "1200", "1500", "1300", "True", "False", "False", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False"], ["16004", "180000", "29", "25781", "26000", "26310", "26662", "26166", "26176", "1800", "1800", "1500", "1056", "950", "1000", "False", "True", "False", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False"], ["22974", "210000", "32", "355", "975", "410", "0", "0", "0", "979", "412", "0", "0", "0", "0", "False", "True", "False", "True", "False", "False", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["17535", "190000", "45", "76433", "78472", "80548", "81778", "83082", "84811", "3300", "3331", "3359", "2663", "2751", "3000", "False", "True", "False", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False"]], "shape": {"columns": 81, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LIMIT_BAL</th>\n", "      <th>AGE</th>\n", "      <th>BILL_AMT1</th>\n", "      <th>BILL_AMT2</th>\n", "      <th>BILL_AMT3</th>\n", "      <th>BILL_AMT4</th>\n", "      <th>BILL_AMT5</th>\n", "      <th>BILL_AMT6</th>\n", "      <th>PAY_AMT1</th>\n", "      <th>PAY_AMT2</th>\n", "      <th>...</th>\n", "      <th>PAY_5_7</th>\n", "      <th>PAY_6_-2</th>\n", "      <th>PAY_6_-1</th>\n", "      <th>PAY_6_0</th>\n", "      <th>PAY_6_2</th>\n", "      <th>PAY_6_3</th>\n", "      <th>PAY_6_4</th>\n", "      <th>PAY_6_5</th>\n", "      <th>PAY_6_6</th>\n", "      <th>PAY_6_7</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>641</th>\n", "      <td>130000</td>\n", "      <td>28</td>\n", "      <td>100143</td>\n", "      <td>50456</td>\n", "      <td>50000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2500</td>\n", "      <td>1000</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4678</th>\n", "      <td>170000</td>\n", "      <td>29</td>\n", "      <td>165027</td>\n", "      <td>168990</td>\n", "      <td>172307</td>\n", "      <td>35234</td>\n", "      <td>32869</td>\n", "      <td>33862</td>\n", "      <td>7200</td>\n", "      <td>7500</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16004</th>\n", "      <td>180000</td>\n", "      <td>29</td>\n", "      <td>25781</td>\n", "      <td>26000</td>\n", "      <td>26310</td>\n", "      <td>26662</td>\n", "      <td>26166</td>\n", "      <td>26176</td>\n", "      <td>1800</td>\n", "      <td>1800</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22974</th>\n", "      <td>210000</td>\n", "      <td>32</td>\n", "      <td>355</td>\n", "      <td>975</td>\n", "      <td>410</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>979</td>\n", "      <td>412</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17535</th>\n", "      <td>190000</td>\n", "      <td>45</td>\n", "      <td>76433</td>\n", "      <td>78472</td>\n", "      <td>80548</td>\n", "      <td>81778</td>\n", "      <td>83082</td>\n", "      <td>84811</td>\n", "      <td>3300</td>\n", "      <td>3331</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 81 columns</p>\n", "</div>"], "text/plain": ["       LIMIT_BAL  AGE  BILL_AMT1  BILL_AMT2  BILL_AMT3  BILL_AMT4  BILL_AMT5  \\\n", "641       130000   28     100143      50456      50000          0          0   \n", "4678      170000   29     165027     168990     172307      35234      32869   \n", "16004     180000   29      25781      26000      26310      26662      26166   \n", "22974     210000   32        355        975        410          0          0   \n", "17535     190000   45      76433      78472      80548      81778      83082   \n", "\n", "       BILL_AMT6  PAY_AMT1  PAY_AMT2  ...  PAY_5_7  PAY_6_-2  PAY_6_-1  \\\n", "641            0      2500      1000  ...    False      True     False   \n", "4678       33862      7200      7500  ...    False     False     False   \n", "16004      26176      1800      1800  ...    False     False     False   \n", "22974          0       979       412  ...    False      True     False   \n", "17535      84811      3300      3331  ...    False     False     False   \n", "\n", "       PAY_6_0  PAY_6_2  PAY_6_3  PAY_6_4  PAY_6_5  PAY_6_6  PAY_6_7  \n", "641      False    False    False    False    False    False    False  \n", "4678      True    False    False    False    False    False    False  \n", "16004     True    False    False    False    False    False    False  \n", "22974    False    False    False    False    False    False    False  \n", "17535     True    False    False    False    False    False    False  \n", "\n", "[5 rows x 81 columns]"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["Categorical_features = ['SEX', 'EDUCATION', 'MARRIAGE', 'PAY_0', 'PAY_2', 'PAY_3', 'PAY_4', 'PAY_5', 'PAY_6']\n", "\n", "X_encoded = pd.get_dummies(X, columns=Categorical_features)\n", "\n", "X_encoded.head()"]}, {"cell_type": "code", "execution_count": 111, "id": "07a322e2", "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X_encoded, y, test_size=0.2, random_state=42)\n", "X_train_scaled = scale(X_train)\n", "X_test_scaled = scale(X_test)"]}, {"cell_type": "markdown", "id": "fda07b36", "metadata": {}, "source": ["## Model"]}, {"cell_type": "code", "execution_count": 112, "id": "286a247d", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-3 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-3 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-3 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-3 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-3 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-3 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-3 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-3 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-3 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-3 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-3 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-3 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-3 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-3 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-3 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-3 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-3 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-3 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-3 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-3 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-3 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-3 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-3 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-3 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-3 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-3 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-3 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-3 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-3\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>SVC(random_state=42)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" checked><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;SVC<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.svm.SVC.html\">?<span>Documentation for SVC</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>SVC(random_state=42)</pre></div> </div></div></div></div>"], "text/plain": ["SVC(random_state=42)"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["clf_svm = SVC(random_state=42)\n", "clf_svm.fit(X_train_scaled, y_train)"]}, {"cell_type": "code", "execution_count": 113, "id": "6767f95c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<sklearn.metrics._plot.confusion_matrix.ConfusionMatrixDisplay at 0x2e0a21d7920>"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ConfusionMatrixDisplay.from_estimator(clf_svm, X_test_scaled, y_test)"]}, {"cell_type": "markdown", "id": "faed0ffe", "metadata": {}, "source": ["### Tuning the model"]}, {"cell_type": "code", "execution_count": 115, "id": "3d1e8660", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'C': 1, 'gamma': 0.01, 'kernel': 'rbf'}\n"]}], "source": ["param_grid = [\n", "    {'C': [0.01, 0.1, 1, 10, 100],\n", "     'gamma': [0.001, 0.01, 0.1, 1, 10],\n", "     'kernel': ['rbf']}\n", "]\n", "\n", "optimal_params = GridSearchCV(\n", "    SVC(),\n", "    param_grid,\n", "    cv=5,\n", "    scoring='accuracy',\n", "    verbose=0)\n", "optimal_params.fit(X_train_scaled, y_train)\n", "print(optimal_params.best_params_)\n"]}, {"cell_type": "code", "execution_count": null, "id": "4eb6909e", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>SVC(C=1, gamma=0.01, random_state=42)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;SVC<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.svm.SVC.html\">?<span>Documentation for SVC</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>SVC(C=1, gamma=0.01, random_state=42)</pre></div> </div></div></div></div>"], "text/plain": ["SVC(C=1, gamma=0.01, random_state=42)"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["optimized_svc = SVC(**optimal_params.best_params_, random_state=42)\n", "optimized_svc.fit(X_train_scaled, y_train)"]}, {"cell_type": "code", "execution_count": null, "id": "d3e80901", "metadata": {}, "outputs": [{"data": {"text/plain": ["<sklearn.metrics._plot.confusion_matrix.ConfusionMatrixDisplay at 0x2e0a495c680>"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ConfusionMatrixDisplay.from_estimator(optimized_svc, X_test_scaled, y_test)"]}, {"cell_type": "markdown", "id": "b5174be0", "metadata": {}, "source": ["## PCA"]}, {"cell_type": "code", "execution_count": 118, "id": "024790b7", "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pca = PCA()\n", "X_train_pca = pca.fit_transform(X_train_scaled)\n", "\n", "per_val = np.round(pca.explained_variance_ratio_*100, decimals=1)\n", "labels = [str(x) for x in range(1, len(per_val)*1)]\n", "\n", "plt.bar(x=range(1,len(per_val)+1), height=per_val)\n", "plt.tick_params(\n", "    axis='x',\n", "    which='both',\n", "    bottom=False,\n", "    top=False,\n", "    labelbottom=False\n", ")\n", "plt.ylabel('Percentage of Explained Variance')\n", "plt.xlabel('Principal Components')\n", "plt.title('<PERSON><PERSON> Plot')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 120, "id": "6fd695b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'C': 0.1, 'gamma': 10, 'kernel': 'rbf'}\n"]}], "source": ["train_pc_1_coords = X_train_pca[:, 0]\n", "train_pc_2_coords = X_train_pca[:, 1]\n", "\n", "pca_train_scaled = scale(np.column_stack((train_pc_1_coords, train_pc_2_coords)))\n", "\n", "param_grid = [\n", "    {'C': [0.01, 0.1, 1, 10, 100],\n", "     'gamma': [0.001, 0.01, 0.1, 1, 10],\n", "     'kernel': ['rbf']}\n", "]\n", "\n", "optimal_params_2 = GridSearchCV(\n", "    SVC(),\n", "    param_grid,\n", "    cv=5,\n", "    scoring='accuracy',\n", "    verbose=0)\n", "\n", "optimal_params_2.fit(pca_train_scaled, y_train)\n", "print(optimal_params_2.best_params_)\n"]}, {"cell_type": "code", "execution_count": 124, "id": "f9b7d7d6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["clf_svm = SVC(**optimal_params_2.best_params_, random_state=42)\n", "clf_svm.fit(pca_train_scaled, y_train)\n", "\n", "X_test_pca = pca.transform(X_train_scaled)\n", "test_pc1_coords = X_test_pca[:, 0]\n", "test_pc2_coords = X_test_pca[:, 1]\n", "\n", "x_min = test_pc1_coords.min() - 1\n", "x_max = test_pc1_coords.max() + 1\n", "\n", "y_min = test_pc2_coords.min() - 1\n", "y_max = test_pc2_coords.max() + 1\n", "\n", "xx, yy = np.meshgrid(np.arange(x_min, x_max, 0.1),\n", "                     np.arange(y_min, y_max, 0.1))\n", "\n", "Z = clf_svm.predict(np.column_stack((xx.ravel(), yy.ravel())))\n", "\n", "Z = Z.reshape(xx.shape)\n", "\n", "fig, ax = plt.subplots(figsize=(10, 10))\n", "ax.contourf(xx, yy, Z, alpha=0.1)\n", "\n", "cmap = colors.ListedColormap(['#e41a1c', '#4daf4a'])\n", "\n", "scatter = ax.scatter(test_pc1_coords, test_pc2_coords, c=y_train,\n", "                     cmap=cmap,\n", "                     s=100,\n", "                     edgecolors='k',\n", "                     alpha=0.7\n", "                    )\n", "\n", "legend = ax.legend(*scatter.legend_elements(),\n", "                   loc=\"upper right\",\n", "                   title=\"Classes\"\n", "                  )\n", "legend.get_texts()[0].set_text('No Default')\n", "legend.get_texts()[1].set_text('Default')\n", "\n", "ax.set_ylabel('PC2')\n", "ax.set_xlabel('PC1')\n", "ax.set_title('SVM Decision Boundary on PCA-Reduced Credit Card Data')\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}