{"cells": [{"cell_type": "markdown", "id": "aa4a253f", "metadata": {}, "source": ["# <a id='toc1_'></a>[XGBoost Videos](#toc0_)"]}, {"cell_type": "markdown", "id": "c0392de0", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "b4a8267c", "metadata": {}, "source": ["# TASK\n", "\n", " Watch the below videos and make notes and take sreenshots about them in this Jupiter Notebook. If you want you can use AI for the Python code for visualizations of the concepts. After adding your notes, deliver this Jupiter Notebook to the delivery folder in Learn. "]}, {"cell_type": "markdown", "id": "28900224", "metadata": {}, "source": ["[Theory video - XGBoost Part 1 (of 4): Regression](https://www.youtube.com/watch?v=OtD8wVaFm6E&list=PLblh5JKOoLULU0irPgs1SnKO6wqVjKUsQ&index=1)\n", "\n", "[Theory video - XGBoost Part 2 (of 4): Classification)](https://www.youtube.com/watch?v=8b1JEDvenQU&list=PLblh5JKOoLULU0irPgs1SnKO6wqVjKUsQ&index=2)\n", "\n", "[OPTIONAL Theory video - XGBoost Part 3 (of 4): Mathematical Details](https://www.youtube.com/watch?v=ZVFeW798-2I&list=PLblh5JKOoLULU0irPgs1SnKO6wqVjKUsQ&index=4)\n", "\n", "[Theory video - XGBoost Part 4 (of 4): Crazy Cool Optimizations](https://www.youtube.com/watch?v=oRrKeUCEbq8&list=PLblh5JKOoLULU0irPgs1SnKO6wqVjKUsQ&index=5)\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "e7594808", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "d6d1234f", "metadata": {}, "source": ["# XGBoost for Regression - Notes\n", "\n", "## Introduction to XGBoost\n", "\n", "XGBoost (eXtreme Gradient Boosting) is an advanced implementation of gradient boosting designed for speed and performance.\n", "\n", "### Key Features\n", "\n", "- Powerful machine learning algorithm for structured/tabular data\n", "- Consistently performs well in machine learning competitions\n", "- Flexible and versatile for both regression and classification tasks\n", "- Built-in regularization to prevent overfitting\n", "- Highly optimized for computational efficiency\n", "\n", "## Regression\n", "\n", "### Similarity score\n", "\n", "$\\dfrac{(\\sum R)^2}{n+\\lambda}$\n", "\n", "- $R=$ Value of observation\n", "- $n=$ amount of observations\n", "- $\\lambda=$ Regularization parameter that prevents overfitting\n", "\n", "The similarity score is a key metric used in XGBoost to evaluate potential split points when building trees. It helps determine the quality of a split by measuring how similar the instances are within each resulting node.\n", "\n", "- Higher similarity scores indicate better splits that group similar instances together\n", "- Used to identify the optimal feature and threshold for each split in the decision tree\n", "- Incorporates regularization ($\\lambda$) to prevent creating overly complex trees\n", "- Helps XGBoost balance between model performance and complexity\n", "\n", "### Gamma\n", "\n", "Gamma ($\\gamma$) is a parameter in XGBoost that controls the minimum loss reduction required for a split to be made. It functions as a threshold that determines whether a potential split is worthwhile.\n", "\n", "- Higher gamma values make the algorithm more conservative about creating new splits\n", "- Acts as a pruning mechanism by requiring that splits provide a minimum improvement in the loss function\n", "- Helps prevent overfitting by discouraging the creation of splits with minimal impact\n", "- Can be tuned during hyperparameter optimization to find the optimal balance\n", "\n", "### Learning rate ($\\eta$)\n", "\n", "The learning rate in XGBoost determines how much each tree contributes to the final prediction. It controls the step size during the gradient boosting process.\n", "\n", "- Lower learning rates require more trees but often yield better performance\n", "- Typical values range from 0.01 to 0.3\n", "- Smaller learning rates help prevent overfitting by making the training process more conservative\n", "- Often tuned alongside the number of trees (n_estimators) to find optimal performance\n", "\n", "## Classification\n", "\n", "### Similarity score\n", "\n", "$\\dfrac{(\\sum R_i)^2}{\\sum [P_i\\times(1-P_i)]+\\lambda}$\n", "\n", "- $P=$  Previous probability\n", "- $R=$ Residiuals\n", "- $\\lambda=$ Regularization parameter (prevents overfitting)\n", "\n", "### Gain\n", "\n", "Gain in XGBoost classification refers to the improvement in the loss function achieved by a split. It quantifies how much better the model becomes after introducing a particular split.\n", "\n", "- Calculated as the difference between the similarity score of the parent node and the weighted sum of similarity scores of child nodes\n", "- Used to select the best possible split at each node in the decision tree\n", "- Higher gain values indicate more valuable splits\n", "- The gamma parameter requires that gain exceeds a threshold before creating a split\n", "\n", "### Cover\n", "\n", "Cover in XGBoost refers to the sum of the Hessian gradients for observations in a node. It's essentially a measure of the number of observations affected by a decision, weighted by their prediction difficulty.\n", "\n", "- Represents the weight of instances in each node, indicating the importance of the split\n", "- Used as part of the splitting criterion alongside gain\n", "- Helps ensure that splits are made where they will have the most impact on the dataset\n", "- Can be monitored to understand which features and splits affect the most observations\n", "\n", "## Approximate Greedy Algorithm\n", "\n", "The Approximate Greedy Algorithm is a computational optimization in XGBoost that makes tree building more efficient. Rather than evaluating every possible split point, it groups data points into quantiles (buckets) and evaluates splits only at the bucket boundaries. This approach dramatically reduces computation time while maintaining model quality, especially for large datasets.\n", "\n", "### Quantiles\n", "\n", "Quantiles in the Approximate Greedy Algorithm are data divisions used to reduce computational complexity. By creating these buckets, XGBoost only needs to evaluate split points at the boundaries between quantiles rather than at every possible data point.\n", "\n", "- Reduces computational complexity from O(n*log(n)) to O(k*log(n)), where k is the number of quantiles\n", "- The number of quantiles can be controlled through the 'sketch_eps' parameter\n", "- Enables efficient processing of large datasets without significant loss in model quality\n", "- A critical innovation that contributes to XGBoost's speed advantage over other gradient boosting implementations\n", "\n", "### Paraller Learning\n", "\n", "Parallel Learning is a key optimization in XGBoost that significantly reduces training time. It allows different parts of the algorithm to run concurrently, taking full advantage of modern multi-core processors.\n", "\n", "- Allows for parallel construction of trees by distributing the work across multiple CPU cores\n", "- Enables efficient out-of-core computation for datasets that don't fit in memory\n", "- Includes parallel implementation of both tree building and objective function calculations\n", "- Can be combined with distributed computing frameworks for training on very large datasets\n", "\n", "### Weighted Quantile Sketch\n", "\n", "The Weighted Quantile Sketch is an advanced algorithm used by XGBoost to efficiently handle weighted data points when determining split points. This technique is particularly important for datasets with imbalanced classes or varying instance weights.\n", "\n", "- Enables accurate approximation of the distribution of weighted data points\n", "- Allows XGBoost to maintain high model quality even when using quantile-based approximations\n", "- Adapts the quantile boundaries based on the importance of each data point\n", "- Contributes significantly to XGBoost's ability to handle both large datasets and complex weighting schemes\n", "\n", "$W=P_i\\times(1-P_i)$\n", "\n", "## Sparcity-Aware Split Finding\n", "\n", "Sparsity-Aware Split Finding is a technique in XGBoost that efficiently handles sparse or missing data. Instead of requiring imputation of missing values, XGBoost learns the optimal direction (left or right branch) for missing values at each split, treating them as a separate category.\n", "\n", "- Efficiently processes sparse datasets by only traversing non-missing entries\n", "- Automatically learns the best direction to send missing values at each split\n", "- Significantly improves computational efficiency for datasets with many missing values\n", "- Allows XGBoost to naturally handle different types of sparsity without explicit preprocessing\n", "\n", "## Cache-Aware Access\n", "\n", "Cache-Aware Access is an optimization technique in XGBoost that improves memory access patterns to maximize CPU cache utilization. By arranging data in memory in a way that aligns with how the algorithm accesses it, XGBoost reduces cache misses and memory latency during training.\n", "\n", "- Organizes data structures to maximize CPU cache hit rates\n", "- Reduces memory access latency by improving data locality\n", "- Particularly effective for large datasets that don't fit entirely in CPU cache\n", "- Contributes significantly to XGBoost's performance advantage over other implementations\n", "\n", "### Blocks for out-of-Core Computation\n", "\n", "Out-of-Core Computation in XGBoost refers to techniques that enable processing datasets larger than available RAM. By dividing data into blocks and processing them sequentially, XGBoost can train models on very large datasets without requiring the entire dataset to be loaded into memory simultaneously.\n", "\n", "- Divides large datasets into blocks that can be processed independently\n", "- Enables training on datasets much larger than available RAM\n", "- Minimizes disk I/O by optimizing block size and access patterns\n", "- Can be combined with parallel processing for improved performance on large datasets"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}