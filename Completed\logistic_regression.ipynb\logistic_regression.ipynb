{"cells": [{"cell_type": "markdown", "id": "d80443a0", "metadata": {}, "source": ["# <a id='toc1_'></a>[Logistic Regression - Binary Classification](#toc0_)"]}, {"cell_type": "markdown", "id": "773b33c9", "metadata": {}, "source": ["**Table of contents**<a id='toc0_'></a>    \n", "- [Logistic Regression - Binary Classification](#toc1_)    \n", "- [Theory](#toc2_)    \n", "- [Problem Statement](#toc3_)    \n", "- [Introduction](#toc4_)    \n", "- [Importing Libraries](#toc5_)    \n", "- [Loading and Preparing the Dataset](#toc6_)    \n", "  - [Loading the Dataset](#toc6_1_)    \n", "  - [Data Exploration and Cleaning](#toc6_2_)    \n", "  - [Separating Features (X) and Target (y)](#toc6_3_)    \n", "- [Train/Validation/Test Split](#toc7_)    \n", "- [Data Preprocessing Pipeline](#toc8_)    \n", "  - [Identifying Feature Types](#toc8_1_)    \n", "  - [Numerical Pipeline](#toc8_2_)    \n", "  - [Categorical Pipeline](#toc8_3_)    \n", "  - [Complete Preprocessing Pipeline](#toc8_4_)    \n", "- [Model Implementation](#toc9_)    \n", "  - [Model Initialization](#toc9_1_)    \n", "  - [Training](#toc9_2_)    \n", "  - [Making Predictions](#toc9_3_)    \n", "- [Model Evaluation](#toc10_)    \n", "  - [Additional Evaluation Metrics](#toc10_1_)    \n", "    - [Comparing Accuracy to \"Dumb\" Model](#toc10_1_1_)    \n", "- [Saving and Loading the Model](#toc11_)    \n", "  - [Saving the Model Package](#toc11_1_)    \n", "  - [Loading the Model Package](#toc11_2_)    \n", "  - [Using the Loaded Model](#toc11_3_)    \n", "- [Summary](#toc12_)    \n", "  - [ Assumptions of Logistic Regression](#toc12_1_)    \n", "  - [ Limitations](#toc12_2_)    \n", "  - [ Best Use Cases](#toc12_3_)    \n", "\n", "<!-- vscode-jupyter-toc-config\n", "\tnumbering=false\n", "\tanchor=true\n", "\tflat=false\n", "\tminLevel=1\n", "\tmaxLevel=6\n", "\t/vscode-jupyter-toc-config -->\n", "<!-- THIS CELL WILL BE REPLACED ON TOC UPDATE. DO NOT WRITE YOUR TEXT IN THIS CELL -->"]}, {"cell_type": "markdown", "id": "544f6e62", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "d4fb2a81", "metadata": {}, "source": ["# <a id='toc2_'></a>[Theory](#toc0_)"]}, {"cell_type": "markdown", "id": "03960e32", "metadata": {}, "source": ["[Theory video](https://www.youtube.com/watch?v=yIYKR4sgzI8&list=PLblh5JKOoLUKxzEP5HA2d-Li7IJkHfXSe&index=1)"]}, {"cell_type": "markdown", "id": "28d8603b", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "e569da99", "metadata": {}, "source": ["# <a id='toc3_'></a>[Problem Statement](#toc0_)\n", "\n", "Our data contains about 10 years of daily weather observations from numerous Australian weather stations. Our task is predicting whether it will rain tomorrow (yes/no). Identifying whether a given problem is a _classfication_ or _regression_ problem is an important first step in machine learning."]}, {"cell_type": "markdown", "id": "e96a098e", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "83107d9c", "metadata": {}, "source": ["# <a id='toc4_'></a>[Introduction](#toc0_)\n", "\n", "Logistic regression is a commonly used technique for solving binary classification problems. In a logistic regression model:\n", "\n", "- we take linear combination (or weighted sum of the input features)\n", "- we apply the sigmoid function to the result to obtain a number between 0 and 1\n", "- this number represents the probability of the input being classified as \"Yes\"\n", "- instead of RMSE, the cross entropy loss function is used to evaluate the results"]}, {"cell_type": "markdown", "id": "intro_markdown", "metadata": {}, "source": ["This notebook demonstrates how to implement and evaluate a logistic regression model for binary classification using Python and scikit-learn.\n", "\n", "We aim to predict whether it will rain tomorrow ('RainTomorrow') based on weather features. The model uses the sigmoid function:\n", "\n", "$$ \\sigma(z) = \\frac{1}{1 + e^{-z}}$$\n", "\n", "Where:\n", "- $z$ is the linear combination: $z = w_0x_0 + w_1x_1 + ... + w_nx_n + b$\n", "- $\\sigma(z)$ represents the probability of the positive class (Rain Tomorrow = 'Yes')\n", "- Decision boundary: if $\\sigma(z) \\geq 0.5$, predict 'Yes'; otherwise 'No'\n", "\n", "The logistic regression draws a linear decision boundary in the feature space to separate the two classes."]}, {"cell_type": "markdown", "id": "25d2f079", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki//Machine_Learning_Methods/refs/heads/main/figs/logistic_regression_equations.png\" width=\"800\">"]}, {"cell_type": "markdown", "id": "27b740f0", "metadata": {}, "source": ["The sigmoid function applied to the linear combination of inputs has the following formula:"]}, {"cell_type": "markdown", "id": "6ee63a67", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki//Machine_Learning_Methods/refs/heads/main/figs/sigmoid_function.png\" width=\"500\">"]}, {"cell_type": "markdown", "id": "6f230df1", "metadata": {}, "source": ["The output of the sigmoid function is called a logistic, hence the name _logistic regression_. "]}, {"cell_type": "markdown", "id": "7cd45c46", "metadata": {}, "source": ["So the basic idea is to divide the data into two separate categories (this figure is just an example and has nothing to do with the data of this notebook):\n", "\n", "<img src=\"https://raw.githubusercontent.com/henrylahteenmaki//Machine_Learning_Methods/refs/heads/main/figs/logistic_regression_decision_boundary.png\" width=\"500\">"]}, {"cell_type": "markdown", "id": "61b0d35f", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "d0059821", "metadata": {}, "source": ["# <a id='toc5_'></a>[Importing Libraries](#toc0_)"]}, {"cell_type": "code", "execution_count": 28, "id": "import_code", "metadata": {}, "outputs": [], "source": ["\n", "# Import core data analysis and visualization libraries\n", "# - pandas for data manipulation and analysis\n", "# - numpy for numerical operations and array handling\n", "# - matplotlib.pyplot for creating plots and visualizations\n", "# - seaborn for enhanced statistical visualizations\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Import utility for saving/loading Python objects\n", "# - joblib for efficient serialization of scikit-learn objects\n", "import joblib\n", "\n", "# Import scikit-learn modules for machine learning:\n", "# Data splitting and preprocessing\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler, OneHotEncoder\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "\n", "# Model and evaluation\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import (accuracy_score, precision_score, recall_score, \n", "                            f1_score, confusion_matrix, classification_report, \n", "                            roc_curve, auc, roc_auc_score)\n", "\n", "# Set display options for better output formatting\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', 1000)\n", "\n"]}, {"cell_type": "markdown", "id": "d717e604", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "ca415295", "metadata": {}, "source": ["# <a id='toc6_'></a>[Loading and Preparing the Dataset](#toc0_)"]}, {"cell_type": "markdown", "id": "a0c84e20", "metadata": {}, "source": ["## <a id='toc6_1_'></a>[Loading the Dataset](#toc0_)"]}, {"cell_type": "code", "execution_count": 29, "id": "load_data_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (145460, 23)\n", "Columns: ['Date', 'Location', '<PERSON><PERSON>em<PERSON>', '<PERSON><PERSON>em<PERSON>', 'Rainfall', 'Evaporation', 'Sunshine', 'WindGustDir', 'WindGustSpeed', 'WindDir9am', 'WindDir3pm', 'WindSpeed9am', 'WindSpeed3pm', 'Humidity9am', 'Humidity3pm', 'Pressure9am', 'Pressure3pm', 'Cloud9am', 'Cloud3pm', 'Temp9am', 'Temp3pm', 'RainToday', 'RainTomorrow']\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Date", "rawType": "object", "type": "string"}, {"name": "Location", "rawType": "object", "type": "string"}, {"name": "MinTemp", "rawType": "float64", "type": "float"}, {"name": "MaxTemp", "rawType": "float64", "type": "float"}, {"name": "Rainfall", "rawType": "float64", "type": "float"}, {"name": "Evaporation", "rawType": "float64", "type": "float"}, {"name": "Sunshine", "rawType": "float64", "type": "float"}, {"name": "WindGustDir", "rawType": "object", "type": "string"}, {"name": "WindGustSpeed", "rawType": "float64", "type": "float"}, {"name": "WindDir9am", "rawType": "object", "type": "string"}, {"name": "WindDir3pm", "rawType": "object", "type": "string"}, {"name": "WindSpeed9am", "rawType": "float64", "type": "float"}, {"name": "WindSpeed3pm", "rawType": "float64", "type": "float"}, {"name": "Humidity9am", "rawType": "float64", "type": "float"}, {"name": "Humidity3pm", "rawType": "float64", "type": "float"}, {"name": "Pressure9am", "rawType": "float64", "type": "float"}, {"name": "Pressure3pm", "rawType": "float64", "type": "float"}, {"name": "Cloud9am", "rawType": "float64", "type": "float"}, {"name": "Cloud3pm", "rawType": "float64", "type": "float"}, {"name": "Temp9am", "rawType": "float64", "type": "float"}, {"name": "Temp3pm", "rawType": "float64", "type": "float"}, {"name": "RainToday", "rawType": "object", "type": "string"}, {"name": "RainTomorrow", "rawType": "object", "type": "string"}], "ref": "64f784d5-eb55-4e03-8dad-5f00ad8293e8", "rows": [["0", "2008-12-01", "Albury", "13.4", "22.9", "0.6", null, null, "W", "44.0", "W", "WNW", "20.0", "24.0", "71.0", "22.0", "1007.7", "1007.1", "8.0", null, "16.9", "21.8", "No", "No"], ["1", "2008-12-02", "Albury", "7.4", "25.1", "0.0", null, null, "WNW", "44.0", "NNW", "WSW", "4.0", "22.0", "44.0", "25.0", "1010.6", "1007.8", null, null, "17.2", "24.3", "No", "No"], ["2", "2008-12-03", "Albury", "12.9", "25.7", "0.0", null, null, "WSW", "46.0", "W", "WSW", "19.0", "26.0", "38.0", "30.0", "1007.6", "1008.7", null, "2.0", "21.0", "23.2", "No", "No"], ["3", "2008-12-04", "Albury", "9.2", "28.0", "0.0", null, null, "NE", "24.0", "SE", "E", "11.0", "9.0", "45.0", "16.0", "1017.6", "1012.8", null, null, "18.1", "26.5", "No", "No"], ["4", "2008-12-05", "Albury", "17.5", "32.3", "1.0", null, null, "W", "41.0", "ENE", "NW", "7.0", "20.0", "82.0", "33.0", "1010.8", "1006.0", "7.0", "8.0", "17.8", "29.7", "No", "No"]], "shape": {"columns": 23, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Location</th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>Rainfall</th>\n", "      <th>Evaporation</th>\n", "      <th><PERSON></th>\n", "      <th>WindGustDir</th>\n", "      <th>WindGustSpeed</th>\n", "      <th>WindDir9am</th>\n", "      <th>WindDir3pm</th>\n", "      <th>WindSpeed9am</th>\n", "      <th>WindSpeed3pm</th>\n", "      <th>Humidity9am</th>\n", "      <th>Humidity3pm</th>\n", "      <th>Pressure9am</th>\n", "      <th>Pressure3pm</th>\n", "      <th>Cloud9am</th>\n", "      <th>Cloud3pm</th>\n", "      <th>Temp9am</th>\n", "      <th>Temp3pm</th>\n", "      <th><PERSON><PERSON><PERSON>y</th>\n", "      <th>RainTomorrow</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2008-12-01</td>\n", "      <td>Albury</td>\n", "      <td>13.4</td>\n", "      <td>22.9</td>\n", "      <td>0.6</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>W</td>\n", "      <td>44.0</td>\n", "      <td>W</td>\n", "      <td>WNW</td>\n", "      <td>20.0</td>\n", "      <td>24.0</td>\n", "      <td>71.0</td>\n", "      <td>22.0</td>\n", "      <td>1007.7</td>\n", "      <td>1007.1</td>\n", "      <td>8.0</td>\n", "      <td>NaN</td>\n", "      <td>16.9</td>\n", "      <td>21.8</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2008-12-02</td>\n", "      <td>Albury</td>\n", "      <td>7.4</td>\n", "      <td>25.1</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>WNW</td>\n", "      <td>44.0</td>\n", "      <td>NNW</td>\n", "      <td>WSW</td>\n", "      <td>4.0</td>\n", "      <td>22.0</td>\n", "      <td>44.0</td>\n", "      <td>25.0</td>\n", "      <td>1010.6</td>\n", "      <td>1007.8</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>17.2</td>\n", "      <td>24.3</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2008-12-03</td>\n", "      <td>Albury</td>\n", "      <td>12.9</td>\n", "      <td>25.7</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>WSW</td>\n", "      <td>46.0</td>\n", "      <td>W</td>\n", "      <td>WSW</td>\n", "      <td>19.0</td>\n", "      <td>26.0</td>\n", "      <td>38.0</td>\n", "      <td>30.0</td>\n", "      <td>1007.6</td>\n", "      <td>1008.7</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>21.0</td>\n", "      <td>23.2</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2008-12-04</td>\n", "      <td>Albury</td>\n", "      <td>9.2</td>\n", "      <td>28.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NE</td>\n", "      <td>24.0</td>\n", "      <td>SE</td>\n", "      <td>E</td>\n", "      <td>11.0</td>\n", "      <td>9.0</td>\n", "      <td>45.0</td>\n", "      <td>16.0</td>\n", "      <td>1017.6</td>\n", "      <td>1012.8</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>18.1</td>\n", "      <td>26.5</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2008-12-05</td>\n", "      <td>Albury</td>\n", "      <td>17.5</td>\n", "      <td>32.3</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>W</td>\n", "      <td>41.0</td>\n", "      <td>ENE</td>\n", "      <td>NW</td>\n", "      <td>7.0</td>\n", "      <td>20.0</td>\n", "      <td>82.0</td>\n", "      <td>33.0</td>\n", "      <td>1010.8</td>\n", "      <td>1006.0</td>\n", "      <td>7.0</td>\n", "      <td>8.0</td>\n", "      <td>17.8</td>\n", "      <td>29.7</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date Location  MinTemp  MaxTemp  Rainfall  Evaporation  Sunshine WindGustDir  WindGustSpeed WindDir9am WindDir3pm  WindSpeed9am  WindSpeed3pm  Humidity9am  Humidity3pm  Pressure9am  Pressure3pm  Cloud9am  Cloud3pm  Temp9am  Temp3pm RainToday RainTomorrow\n", "0  2008-12-01   Albury     13.4     22.9       0.6          NaN       NaN           W           44.0          W        WNW          20.0          24.0         71.0         22.0       1007.7       1007.1       8.0       NaN     16.9     21.8        No           No\n", "1  2008-12-02   Albury      7.4     25.1       0.0          NaN       NaN         WNW           44.0        NNW        WSW           4.0          22.0         44.0         25.0       1010.6       1007.8       NaN       NaN     17.2     24.3        No           No\n", "2  2008-12-03   Albury     12.9     25.7       0.0          NaN       NaN         WSW           46.0          W        WSW          19.0          26.0         38.0         30.0       1007.6       1008.7       NaN       2.0     21.0     23.2        No           No\n", "3  2008-12-04   Albury      9.2     28.0       0.0          NaN       NaN          NE           24.0         SE          E          11.0           9.0         45.0         16.0       1017.6       1012.8       NaN       NaN     18.1     26.5        No           No\n", "4  2008-12-05   Albury     17.5     32.3       1.0          NaN       NaN           W           41.0        ENE         NW           7.0          20.0         82.0         33.0       1010.8       1006.0       7.0       8.0     17.8     29.7        No           No"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Define the URL of the Australian weather dataset and assign to 'url'\n", "# - URL points to a comprehensive weather dataset with multiple features\n", "# - Dataset contains weather observations from multiple Australian locations\n", "url = 'https://raw.githubusercontent.com/henrylahteenmaki//Machine_Learning_Methods/refs/heads/main/datasets/weatherAUS.csv'\n", "\n", "# Load the dataset from the URL into a pandas DataFrame and assign to 'df'\n", "# - Function: pd.read_csv() reads CSV data from URL or local file\n", "# - Parameters: url specifies the data source location\n", "# - Returns: Pandas DataFrame containing the weather dataset\n", "df = pd.read_csv(url)\n", "\n", "# Display basic information about the dataset\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "\n", "# Display the first few rows to understand the data structure\n", "df.head()\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "id": "82207a9f", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Date", "rawType": "object", "type": "string"}, {"name": "Location", "rawType": "object", "type": "string"}, {"name": "MinTemp", "rawType": "float64", "type": "float"}, {"name": "MaxTemp", "rawType": "float64", "type": "float"}, {"name": "Rainfall", "rawType": "float64", "type": "float"}, {"name": "Evaporation", "rawType": "float64", "type": "float"}, {"name": "Sunshine", "rawType": "float64", "type": "float"}, {"name": "WindGustDir", "rawType": "object", "type": "unknown"}, {"name": "WindGustSpeed", "rawType": "float64", "type": "float"}, {"name": "WindDir9am", "rawType": "object", "type": "string"}, {"name": "WindDir3pm", "rawType": "object", "type": "string"}, {"name": "WindSpeed9am", "rawType": "float64", "type": "float"}, {"name": "WindSpeed3pm", "rawType": "float64", "type": "float"}, {"name": "Humidity9am", "rawType": "float64", "type": "float"}, {"name": "Humidity3pm", "rawType": "float64", "type": "float"}, {"name": "Pressure9am", "rawType": "float64", "type": "float"}, {"name": "Pressure3pm", "rawType": "float64", "type": "float"}, {"name": "Cloud9am", "rawType": "float64", "type": "float"}, {"name": "Cloud3pm", "rawType": "float64", "type": "float"}, {"name": "Temp9am", "rawType": "float64", "type": "float"}, {"name": "Temp3pm", "rawType": "float64", "type": "float"}, {"name": "RainToday", "rawType": "object", "type": "string"}, {"name": "RainTomorrow", "rawType": "object", "type": "unknown"}], "ref": "55a34180-1b96-4461-a10c-9b44c02100e3", "rows": [["145455", "2017-06-21", "<PERSON><PERSON><PERSON>", "2.8", "23.4", "0.0", null, null, "E", "31.0", "SE", "ENE", "13.0", "11.0", "51.0", "24.0", "1024.6", "1020.3", null, null, "10.1", "22.4", "No", "No"], ["145456", "2017-06-22", "<PERSON><PERSON><PERSON>", "3.6", "25.3", "0.0", null, null, "NNW", "22.0", "SE", "N", "13.0", "9.0", "56.0", "21.0", "1023.5", "1019.1", null, null, "10.9", "24.5", "No", "No"], ["145457", "2017-06-23", "<PERSON><PERSON><PERSON>", "5.4", "26.9", "0.0", null, null, "N", "37.0", "SE", "WNW", "9.0", "9.0", "53.0", "24.0", "1021.0", "1016.8", null, null, "12.5", "26.1", "No", "No"], ["145458", "2017-06-24", "<PERSON><PERSON><PERSON>", "7.8", "27.0", "0.0", null, null, "SE", "28.0", "SSE", "N", "13.0", "7.0", "51.0", "24.0", "1019.4", "1016.5", "3.0", "2.0", "15.1", "26.0", "No", "No"], ["145459", "2017-06-25", "<PERSON><PERSON><PERSON>", "14.9", null, "0.0", null, null, null, null, "ESE", "ESE", "17.0", "17.0", "62.0", "36.0", "1020.2", "1017.9", "8.0", "8.0", "15.0", "20.9", "No", null]], "shape": {"columns": 23, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Location</th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>Rainfall</th>\n", "      <th>Evaporation</th>\n", "      <th><PERSON></th>\n", "      <th>WindGustDir</th>\n", "      <th>WindGustSpeed</th>\n", "      <th>WindDir9am</th>\n", "      <th>WindDir3pm</th>\n", "      <th>WindSpeed9am</th>\n", "      <th>WindSpeed3pm</th>\n", "      <th>Humidity9am</th>\n", "      <th>Humidity3pm</th>\n", "      <th>Pressure9am</th>\n", "      <th>Pressure3pm</th>\n", "      <th>Cloud9am</th>\n", "      <th>Cloud3pm</th>\n", "      <th>Temp9am</th>\n", "      <th>Temp3pm</th>\n", "      <th><PERSON><PERSON><PERSON>y</th>\n", "      <th>RainTomorrow</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>145455</th>\n", "      <td>2017-06-21</td>\n", "      <td>Uluru</td>\n", "      <td>2.8</td>\n", "      <td>23.4</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>E</td>\n", "      <td>31.0</td>\n", "      <td>SE</td>\n", "      <td>ENE</td>\n", "      <td>13.0</td>\n", "      <td>11.0</td>\n", "      <td>51.0</td>\n", "      <td>24.0</td>\n", "      <td>1024.6</td>\n", "      <td>1020.3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10.1</td>\n", "      <td>22.4</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145456</th>\n", "      <td>2017-06-22</td>\n", "      <td>Uluru</td>\n", "      <td>3.6</td>\n", "      <td>25.3</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NNW</td>\n", "      <td>22.0</td>\n", "      <td>SE</td>\n", "      <td>N</td>\n", "      <td>13.0</td>\n", "      <td>9.0</td>\n", "      <td>56.0</td>\n", "      <td>21.0</td>\n", "      <td>1023.5</td>\n", "      <td>1019.1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10.9</td>\n", "      <td>24.5</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145457</th>\n", "      <td>2017-06-23</td>\n", "      <td>Uluru</td>\n", "      <td>5.4</td>\n", "      <td>26.9</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>N</td>\n", "      <td>37.0</td>\n", "      <td>SE</td>\n", "      <td>WNW</td>\n", "      <td>9.0</td>\n", "      <td>9.0</td>\n", "      <td>53.0</td>\n", "      <td>24.0</td>\n", "      <td>1021.0</td>\n", "      <td>1016.8</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12.5</td>\n", "      <td>26.1</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145458</th>\n", "      <td>2017-06-24</td>\n", "      <td>Uluru</td>\n", "      <td>7.8</td>\n", "      <td>27.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SE</td>\n", "      <td>28.0</td>\n", "      <td>SSE</td>\n", "      <td>N</td>\n", "      <td>13.0</td>\n", "      <td>7.0</td>\n", "      <td>51.0</td>\n", "      <td>24.0</td>\n", "      <td>1019.4</td>\n", "      <td>1016.5</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>15.1</td>\n", "      <td>26.0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145459</th>\n", "      <td>2017-06-25</td>\n", "      <td>Uluru</td>\n", "      <td>14.9</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ESE</td>\n", "      <td>ESE</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>62.0</td>\n", "      <td>36.0</td>\n", "      <td>1020.2</td>\n", "      <td>1017.9</td>\n", "      <td>8.0</td>\n", "      <td>8.0</td>\n", "      <td>15.0</td>\n", "      <td>20.9</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              Date Location  MinTemp  MaxTemp  Rainfall  Evaporation  Sunshine WindGustDir  WindGustSpeed WindDir9am WindDir3pm  WindSpeed9am  WindSpeed3pm  Humidity9am  Humidity3pm  Pressure9am  Pressure3pm  Cloud9am  Cloud3pm  Temp9am  Temp3pm RainToday RainTomorrow\n", "145455  2017-06-21    Uluru      2.8     23.4       0.0          NaN       NaN           E           31.0         SE        ENE          13.0          11.0         51.0         24.0       1024.6       1020.3       NaN       NaN     10.1     22.4        No           No\n", "145456  2017-06-22    Uluru      3.6     25.3       0.0          NaN       NaN         NNW           22.0         SE          N          13.0           9.0         56.0         21.0       1023.5       1019.1       NaN       NaN     10.9     24.5        No           No\n", "145457  2017-06-23    Uluru      5.4     26.9       0.0          NaN       NaN           N           37.0         SE        WNW           9.0           9.0         53.0         24.0       1021.0       1016.8       NaN       NaN     12.5     26.1        No           No\n", "145458  2017-06-24    Uluru      7.8     27.0       0.0          NaN       NaN          SE           28.0        SSE          N          13.0           7.0         51.0         24.0       1019.4       1016.5       3.0       2.0     15.1     26.0        No           No\n", "145459  2017-06-25    Uluru     14.9      NaN       0.0          NaN       NaN         NaN            NaN        ESE        ESE          17.0          17.0         62.0         36.0       1020.2       1017.9       8.0       8.0     15.0     20.9        No          NaN"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display the last rows to understand the data structure\n", "df.tail()\n"]}, {"cell_type": "markdown", "id": "fbbb8235", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "76e503b9", "metadata": {}, "source": ["## <a id='toc6_2_'></a>[Data Exploration and Cleaning](#toc0_)\n", "\n", "We explore the dataset structure, check for missing values, and understand the target variable distribution."]}, {"cell_type": "code", "execution_count": 31, "id": "data_info_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 145460 entries, 0 to 145459\n", "Data columns (total 23 columns):\n", " #   Column         Non-Null Count   Dtype  \n", "---  ------         --------------   -----  \n", " 0   Date           145460 non-null  object \n", " 1   Location       145460 non-null  object \n", " 2   MinTemp        143975 non-null  float64\n", " 3   MaxTemp        144199 non-null  float64\n", " 4   Rainfall       142199 non-null  float64\n", " 5   Evaporation    82670 non-null   float64\n", " 6   Sunshine       75625 non-null   float64\n", " 7   WindGustDir    135134 non-null  object \n", " 8   WindGustSpeed  135197 non-null  float64\n", " 9   WindDir9am     134894 non-null  object \n", " 10  WindDir3pm     141232 non-null  object \n", " 11  WindSpeed9am   143693 non-null  float64\n", " 12  WindSpeed3pm   142398 non-null  float64\n", " 13  Humidity9am    142806 non-null  float64\n", " 14  Humidity3pm    140953 non-null  float64\n", " 15  Pressure9am    130395 non-null  float64\n", " 16  Pressure3pm    130432 non-null  float64\n", " 17  Cloud9am       89572 non-null   float64\n", " 18  Cloud3pm       86102 non-null   float64\n", " 19  Temp9am        143693 non-null  float64\n", " 20  Temp3pm        141851 non-null  float64\n", " 21  RainToday      142199 non-null  object \n", " 22  RainTomorrow   142193 non-null  object \n", "dtypes: float64(16), object(7)\n", "memory usage: 25.5+ MB\n"]}], "source": ["# Display comprehensive information about the dataset structure\n", "# - Method: info() shows data types, non-null counts, and memory usage\n", "# - Helps identify missing values and appropriate preprocessing steps\n", "df.info()\n"]}, {"cell_type": "code", "execution_count": 32, "id": "7131b6ce", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "2bb612b8-6ae9-47e5-9235-0662b9903761", "rows": [["MinTemp", "1485"], ["MaxTemp", "1261"], ["Rainfall", "3261"], ["Evaporation", "62790"], ["Sunshine", "69835"], ["WindGustDir", "10326"], ["WindGustSpeed", "10263"], ["WindDir9am", "10566"], ["WindDir3pm", "4228"], ["WindSpeed9am", "1767"], ["WindSpeed3pm", "3062"], ["Humidity9am", "2654"], ["Humidity3pm", "4507"], ["Pressure9am", "15065"], ["Pressure3pm", "15028"], ["Cloud9am", "55888"], ["Cloud3pm", "59358"], ["Temp9am", "1767"], ["Temp3pm", "3609"], ["RainToday", "3261"], ["RainTomorrow", "3267"]], "shape": {"columns": 1, "rows": 21}}, "text/plain": ["MinTemp           1485\n", "MaxTemp           1261\n", "Rainfall          3261\n", "Evaporation      62790\n", "Sunshine         69835\n", "WindGustDir      10326\n", "WindGustSpeed    10263\n", "WindDir9am       10566\n", "WindDir3pm        4228\n", "WindSpeed9am      1767\n", "WindSpeed3pm      3062\n", "Humidity9am       2654\n", "Humidity3pm       4507\n", "Pressure9am      15065\n", "Pressure3pm      15028\n", "Cloud9am         55888\n", "Cloud3pm         59358\n", "Temp9am           1767\n", "Temp3pm           3609\n", "RainToday         3261\n", "RainTomorrow      3267\n", "dtype: int64"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check for missing values in each column and assign to 'missing_values'\n", "# - Method: isnull().sum() counts null values per column\n", "# - Returns: Series with column names and their missing value counts\n", "missing_values = df.isnull().sum()\n", "\n", "# Display missing values for columns that have any missing data\n", "missing_values[missing_values > 0]\n"]}, {"cell_type": "code", "execution_count": 33, "id": "target_analysis_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Target variable 'RainTomorrow' distribution (before cleaning):\n", "RainTomorrow\n", "No     110316\n", "Yes     31877\n", "NaN      3267\n", "Name: count, dtype: int64\n", "\n", "Missing values in target: 3267\n", "\n", "Percentage distribution:\n", "RainTomorrow\n", "No     75.84\n", "Yes    21.91\n", "NaN     2.25\n", "Name: proportion, dtype: float64\n"]}], "source": ["\n", "# Analyze the target variable distribution before cleaning\n", "# - Method: value_counts() shows frequency of each unique value\n", "# - Parameter: dropna=False includes missing values in the count\n", "print(\"Target variable 'RainTomorrow' distribution (before cleaning):\")\n", "target_distribution = df['RainTomorrow'].value_counts(dropna=False)\n", "print(target_distribution)\n", "print(f\"\\nMissing values in target: {df['RainTomorrow'].isnull().sum()}\")\n", "\n", "# Calculate percentage distribution for better understanding\n", "target_percentage = df['RainTomorrow'].value_counts(normalize=True, dropna=False) * 100\n", "print(\"\\nPercentage distribution:\")\n", "print(target_percentage.round(2))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 34, "id": "data_cleaning_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Target variable distribution (after cleaning):\n", "RainTomorrow\n", "No     110316\n", "Yes     31877\n", "Name: count, dtype: int64\n", "\n", "Class ratio (No:Yes) = 3.46:1\n"]}], "source": ["\n", "\n", "# Remove rows where the target variable 'RainTomorrow' is missing\n", "# - Method: dropna() removes rows with missing values in specified columns\n", "# - Parameter: subset=['RainTomorrow'] specifies which column to check\n", "# - Parameter: inplace=False returns a new DataFrame (safer approach)\n", "df_clean = df.dropna(subset=['RainTomorrow'], inplace=False)\n", "\n", "\n", "# Verify target variable distribution after cleaning\n", "print(\"\\nTarget variable distribution (after cleaning):\")\n", "clean_target_distribution = df_clean['RainTomorrow'].value_counts()\n", "print(clean_target_distribution)\n", "\n", "# Check for class imbalance\n", "class_ratio = clean_target_distribution['No'] / clean_target_distribution['Yes']\n", "print(f\"\\nClass ratio (No:Yes) = {class_ratio:.2f}:1\")\n", "\n"]}, {"cell_type": "markdown", "id": "be875ca9", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "58279e8a", "metadata": {}, "source": ["## <a id='toc6_3_'></a>[Separating Features (X) and Target (y)](#toc0_)\n", "\n", "We separate the independent variables (features) from the dependent variable (target) for modeling."]}, {"cell_type": "code", "execution_count": 35, "id": "feature_split_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 142193 entries, 0 to 145458\n", "Data columns (total 22 columns):\n", " #   Column         Non-Null Count   Dtype  \n", "---  ------         --------------   -----  \n", " 0   Date           142193 non-null  object \n", " 1   Location       142193 non-null  object \n", " 2   MinTemp        141556 non-null  float64\n", " 3   MaxTemp        141871 non-null  float64\n", " 4   Rainfall       140787 non-null  float64\n", " 5   Evaporation    81350 non-null   float64\n", " 6   Sunshine       74377 non-null   float64\n", " 7   WindGustDir    132863 non-null  object \n", " 8   WindGustSpeed  132923 non-null  float64\n", " 9   WindDir9am     132180 non-null  object \n", " 10  WindDir3pm     138415 non-null  object \n", " 11  WindSpeed9am   140845 non-null  float64\n", " 12  WindSpeed3pm   139563 non-null  float64\n", " 13  Humidity9am    140419 non-null  float64\n", " 14  Humidity3pm    138583 non-null  float64\n", " 15  Pressure9am    128179 non-null  float64\n", " 16  Pressure3pm    128212 non-null  float64\n", " 17  Cloud9am       88536 non-null   float64\n", " 18  Cloud3pm       85099 non-null   float64\n", " 19  Temp9am        141289 non-null  float64\n", " 20  Temp3pm        139467 non-null  float64\n", " 21  RainToday      140787 non-null  object \n", "dtypes: float64(16), object(6)\n", "memory usage: 25.0+ MB\n"]}, {"data": {"text/plain": ["None"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Date", "rawType": "object", "type": "string"}, {"name": "Location", "rawType": "object", "type": "string"}, {"name": "MinTemp", "rawType": "float64", "type": "float"}, {"name": "MaxTemp", "rawType": "float64", "type": "float"}, {"name": "Rainfall", "rawType": "float64", "type": "float"}, {"name": "Evaporation", "rawType": "float64", "type": "float"}, {"name": "Sunshine", "rawType": "float64", "type": "float"}, {"name": "WindGustDir", "rawType": "object", "type": "string"}, {"name": "WindGustSpeed", "rawType": "float64", "type": "float"}, {"name": "WindDir9am", "rawType": "object", "type": "string"}, {"name": "WindDir3pm", "rawType": "object", "type": "string"}, {"name": "WindSpeed9am", "rawType": "float64", "type": "float"}, {"name": "WindSpeed3pm", "rawType": "float64", "type": "float"}, {"name": "Humidity9am", "rawType": "float64", "type": "float"}, {"name": "Humidity3pm", "rawType": "float64", "type": "float"}, {"name": "Pressure9am", "rawType": "float64", "type": "float"}, {"name": "Pressure3pm", "rawType": "float64", "type": "float"}, {"name": "Cloud9am", "rawType": "float64", "type": "float"}, {"name": "Cloud3pm", "rawType": "float64", "type": "float"}, {"name": "Temp9am", "rawType": "float64", "type": "float"}, {"name": "Temp3pm", "rawType": "float64", "type": "float"}, {"name": "RainToday", "rawType": "object", "type": "string"}], "ref": "cdecc633-2de4-4079-a949-3fea2f4cea4b", "rows": [["0", "2008-12-01", "Albury", "13.4", "22.9", "0.6", null, null, "W", "44.0", "W", "WNW", "20.0", "24.0", "71.0", "22.0", "1007.7", "1007.1", "8.0", null, "16.9", "21.8", "No"], ["1", "2008-12-02", "Albury", "7.4", "25.1", "0.0", null, null, "WNW", "44.0", "NNW", "WSW", "4.0", "22.0", "44.0", "25.0", "1010.6", "1007.8", null, null, "17.2", "24.3", "No"], ["2", "2008-12-03", "Albury", "12.9", "25.7", "0.0", null, null, "WSW", "46.0", "W", "WSW", "19.0", "26.0", "38.0", "30.0", "1007.6", "1008.7", null, "2.0", "21.0", "23.2", "No"], ["3", "2008-12-04", "Albury", "9.2", "28.0", "0.0", null, null, "NE", "24.0", "SE", "E", "11.0", "9.0", "45.0", "16.0", "1017.6", "1012.8", null, null, "18.1", "26.5", "No"], ["4", "2008-12-05", "Albury", "17.5", "32.3", "1.0", null, null, "W", "41.0", "ENE", "NW", "7.0", "20.0", "82.0", "33.0", "1010.8", "1006.0", "7.0", "8.0", "17.8", "29.7", "No"]], "shape": {"columns": 22, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Location</th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>Rainfall</th>\n", "      <th>Evaporation</th>\n", "      <th><PERSON></th>\n", "      <th>WindGustDir</th>\n", "      <th>WindGustSpeed</th>\n", "      <th>WindDir9am</th>\n", "      <th>WindDir3pm</th>\n", "      <th>WindSpeed9am</th>\n", "      <th>WindSpeed3pm</th>\n", "      <th>Humidity9am</th>\n", "      <th>Humidity3pm</th>\n", "      <th>Pressure9am</th>\n", "      <th>Pressure3pm</th>\n", "      <th>Cloud9am</th>\n", "      <th>Cloud3pm</th>\n", "      <th>Temp9am</th>\n", "      <th>Temp3pm</th>\n", "      <th><PERSON><PERSON><PERSON>y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2008-12-01</td>\n", "      <td>Albury</td>\n", "      <td>13.4</td>\n", "      <td>22.9</td>\n", "      <td>0.6</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>W</td>\n", "      <td>44.0</td>\n", "      <td>W</td>\n", "      <td>WNW</td>\n", "      <td>20.0</td>\n", "      <td>24.0</td>\n", "      <td>71.0</td>\n", "      <td>22.0</td>\n", "      <td>1007.7</td>\n", "      <td>1007.1</td>\n", "      <td>8.0</td>\n", "      <td>NaN</td>\n", "      <td>16.9</td>\n", "      <td>21.8</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2008-12-02</td>\n", "      <td>Albury</td>\n", "      <td>7.4</td>\n", "      <td>25.1</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>WNW</td>\n", "      <td>44.0</td>\n", "      <td>NNW</td>\n", "      <td>WSW</td>\n", "      <td>4.0</td>\n", "      <td>22.0</td>\n", "      <td>44.0</td>\n", "      <td>25.0</td>\n", "      <td>1010.6</td>\n", "      <td>1007.8</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>17.2</td>\n", "      <td>24.3</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2008-12-03</td>\n", "      <td>Albury</td>\n", "      <td>12.9</td>\n", "      <td>25.7</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>WSW</td>\n", "      <td>46.0</td>\n", "      <td>W</td>\n", "      <td>WSW</td>\n", "      <td>19.0</td>\n", "      <td>26.0</td>\n", "      <td>38.0</td>\n", "      <td>30.0</td>\n", "      <td>1007.6</td>\n", "      <td>1008.7</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>21.0</td>\n", "      <td>23.2</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2008-12-04</td>\n", "      <td>Albury</td>\n", "      <td>9.2</td>\n", "      <td>28.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NE</td>\n", "      <td>24.0</td>\n", "      <td>SE</td>\n", "      <td>E</td>\n", "      <td>11.0</td>\n", "      <td>9.0</td>\n", "      <td>45.0</td>\n", "      <td>16.0</td>\n", "      <td>1017.6</td>\n", "      <td>1012.8</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>18.1</td>\n", "      <td>26.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2008-12-05</td>\n", "      <td>Albury</td>\n", "      <td>17.5</td>\n", "      <td>32.3</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>W</td>\n", "      <td>41.0</td>\n", "      <td>ENE</td>\n", "      <td>NW</td>\n", "      <td>7.0</td>\n", "      <td>20.0</td>\n", "      <td>82.0</td>\n", "      <td>33.0</td>\n", "      <td>1010.8</td>\n", "      <td>1006.0</td>\n", "      <td>7.0</td>\n", "      <td>8.0</td>\n", "      <td>17.8</td>\n", "      <td>29.7</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date Location  MinTemp  MaxTemp  Rainfall  Evaporation  Sunshine WindGustDir  WindGustSpeed WindDir9am WindDir3pm  WindSpeed9am  WindSpeed3pm  Humidity9am  Humidity3pm  Pressure9am  Pressure3pm  Cloud9am  Cloud3pm  Temp9am  Temp3pm RainToday\n", "0  2008-12-01   Albury     13.4     22.9       0.6          NaN       NaN           W           44.0          W        WNW          20.0          24.0         71.0         22.0       1007.7       1007.1       8.0       NaN     16.9     21.8        No\n", "1  2008-12-02   Albury      7.4     25.1       0.0          NaN       NaN         WNW           44.0        NNW        WSW           4.0          22.0         44.0         25.0       1010.6       1007.8       NaN       NaN     17.2     24.3        No\n", "2  2008-12-03   Albury     12.9     25.7       0.0          NaN       NaN         WSW           46.0          W        WSW          19.0          26.0         38.0         30.0       1007.6       1008.7       NaN       2.0     21.0     23.2        No\n", "3  2008-12-04   Albury      9.2     28.0       0.0          NaN       NaN          NE           24.0         SE          E          11.0           9.0         45.0         16.0       1017.6       1012.8       NaN       NaN     18.1     26.5        No\n", "4  2008-12-05   Albury     17.5     32.3       1.0          NaN       NaN           W           41.0        ENE         NW           7.0          20.0         82.0         33.0       1010.8       1006.0       7.0       8.0     17.8     29.7        No"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Extract features by dropping the target column and assign to 'X'\n", "# - Method: drop() removes specified columns from the DataFrame\n", "# - Parameters: '<PERSON>Tomorrow' specifies the target column to remove\n", "# - Parameters: axis=1 indicates column-wise operation\n", "# - Returns: DataFrame containing only the feature columns\n", "X = df_clean.drop('RainTomorrow', axis=1)\n", "\n", "# Display basic information about features\n", "display(X.info())\n", "\n", "# Show first few rows of features\n", "display(X.head())\n"]}, {"cell_type": "code", "execution_count": 36, "id": "target_split_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.series.Series'>\n", "Index: 142193 entries, 0 to 145458\n", "Series name: <PERSON><PERSON><PERSON><PERSON>\n", "Non-Null Count   Dtype \n", "--------------   ----- \n", "142193 non-null  object\n", "dtypes: object(1)\n", "memory usage: 2.2+ MB\n"]}, {"data": {"text/plain": ["None"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "RainTomorrow", "rawType": "object", "type": "string"}], "ref": "4b095fef-67bc-4566-be6b-b49d9bee325d", "rows": [["0", "No"], ["1", "No"], ["2", "No"], ["3", "No"], ["4", "No"]], "shape": {"columns": 1, "rows": 5}}, "text/plain": ["0    No\n", "1    No\n", "2    No\n", "3    No\n", "4    No\n", "Name: <PERSON><PERSON><PERSON><PERSON>, dtype: object"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Select the target column and assign to 'y'\n", "# - Syntax: DataFrame['column'] selects a single column as Series\n", "# - Returns: Pandas Series containing the target values\n", "y = df_clean['RainTomorrow']\n", "\n", "# Display basic information about the target\n", "display(y.info())\n", "\n", "# Show first few values of the target\n", "display(y.head())\n"]}, {"cell_type": "markdown", "id": "bf9bc2f4", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "a2aa91f3", "metadata": {}, "source": ["# <a id='toc7_'></a>[Train/Validation/Test Split](#toc0_)\n", "\n", "We split the dataset into training, validation, and test sets to properly evaluate the model's performance. The training set is used to fit the model, the validation set for hyperparameter tuning and comparing several models, and the test set for final performance assessment. Notice that if you don't need to do hyperparameter tuning nor compare several models, train/test split will suffice."]}, {"cell_type": "markdown", "id": "9afc5429", "metadata": {}, "source": ["While building real-world machine learning models, it is quite common to split the dataset into three parts:\n", "\n", "1. **Training set** - used to train the model, i.e., compute the loss and adjust the model's weights using an optimization technique.\n", "\n", "\n", "2. **Validation set** - used to evaluate the model during training, tune model hyperparameters (optimization technique, regularization etc.), pick the best version of the model or compare and choose between different models. Picking a good validation set is essential for training models that generalize well. [Learn more here.](https://www.fast.ai/2017/11/13/validation-sets/)\n", "\n", "\n", "3. **Test set** - used to compare different models or approaches and report the model's final accuracy. For many datasets, test sets are provided separately. The test set should reflect the kind of data the model will encounter in the real-world, as closely as feasible.\n", "\n", "\n", "<img src=\"https://i.imgur.com/j8eITrK.png\" width=\"480\">\n", "\n", "A key property of the validation and test sets is that they must be representative of the new data you will see in the future.\n", "\n", "As a general rule of thumb you can use around 60% of the data for the training set, 20% for the validation set and 20% for the test set. If a separate test set is already provided, you can use a 75%-25% training-validation split.\n", "\n", "\n", "When rows in the dataset have no inherent order, it's common practice to pick random subsets of rows for creating test and validation sets. This can be done using the `train_test_split` utility from `scikit-learn`. Learn more about it here: https://scikit-learn.org/stable/modules/generated/sklearn.model_selection.train_test_split.html"]}, {"cell_type": "code", "execution_count": 37, "id": "train_test_code", "metadata": {}, "outputs": [], "source": ["\n", "# Split data into training, validation, and test sets\n", "# First split: separate test set (20%)\n", "X_train_temp, X_test, y_train_temp, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "# Second split: separate validation set from remaining data (20% of original = 16% total)\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_train_temp, y_train_temp, test_size=0.2, random_state=42, stratify=y_train_temp\n", ")\n", "\n"]}, {"cell_type": "markdown", "id": "1b1910ed", "metadata": {}, "source": ["However, while working with dates, it's often a better idea to separate the training, validation and test sets with time, so that the model is trained on data from the past and evaluated on data from the future.\n", "\n", "For the current dataset, we can use the Date column in the dataset to create another column for year. We'll pick the last two years for the test set, and one year before it for the validation set."]}, {"cell_type": "code", "execution_count": 38, "id": "61ccec3f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "plt.title('No. of Rows per Year')\n", "sns.countplot(x=pd.to_datetime(X.Date).dt.year)\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "1b83f008", "metadata": {}, "outputs": [], "source": ["# Making a chronological split manually\n", "X_sorted = X.sort_values(by='Date')\n", "X_train_sorted = X_sorted.iloc[0:int(len(X_sorted)*0.6)]\n", "X_val_sorted = X_sorted.iloc[int(len(X_sorted)*0.6):int(len(X_sorted)*0.8)]\n", "X_test_sorted = X_sorted.iloc[int(len(X_sorted)*0.8):]\n"]}, {"cell_type": "markdown", "id": "e1d0636c", "metadata": {}, "source": ["While not a perfect 60-20-20 split, we have ensured that the test validation and test sets both contain data for all 12 months of the year."]}, {"cell_type": "markdown", "id": "88199b89", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "78f6feab", "metadata": {}, "source": ["# <a id='toc8_'></a>[Data Preprocessing Pipeline](#toc0_)\n", "\n", "We create comprehensive preprocessing pipelines to handle both numerical and categorical features. This includes imputation for missing values, scaling for numerical features, and encoding for categorical features."]}, {"cell_type": "markdown", "id": "2735487d", "metadata": {}, "source": ["## <a id='toc8_1_'></a>[Identifying Feature Types](#toc0_)"]}, {"cell_type": "code", "execution_count": 68, "id": "feature_types_code", "metadata": {}, "outputs": [], "source": ["# Identify numerical features by selecting columns with numeric data types\n", "# - Method: select_dtypes() filters columns based on data types\n", "# - Parameters: include=['float64', 'int64'] selects numeric columns\n", "# - Returns: Column names of numerical features\n", "numerical_features = X_train_sorted.select_dtypes(include=['float64', 'int64']).columns\n", "\n", "# Identify categorical features by selecting object-type columns\n", "# - Method: select_dtypes() with include=['object'] for categorical data\n", "# - Returns: Column names of categorical features\n", "categorical_features = X_train_sorted.select_dtypes(include=['object']).columns\n"]}, {"cell_type": "markdown", "id": "0c975af6", "metadata": {}, "source": ["## <a id='toc8_2_'></a>[Numerical Pipeline](#toc0_)\n", "\n", "We create a pipeline for numerical features that handles missing values and scales the data."]}, {"cell_type": "code", "execution_count": 67, "id": "numerical_pipeline_code", "metadata": {}, "outputs": [], "source": ["# Create a preprocessing pipeline for numerical features and assign to 'numerical_pipeline'\n", "# - Class: Pipeline chains multiple preprocessing steps sequentially\n", "# - Steps:\n", "#   - ('imputer', SimpleImputer(strategy='mean')): Fills missing values with column mean\n", "#   - ('scaler', MinMaxScaler()): Scales features to [0, 1] range for logistic regression\n", "# - Returns: Pipeline object for numerical feature preprocessing\n", "\n", "numerical_pipeline = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='mean')),\n", "    ('scaler', MinMaxScaler())\n", "])"]}, {"cell_type": "markdown", "id": "9e031870", "metadata": {}, "source": ["## <a id='toc8_3_'></a>[Categorical Pipeline](#toc0_)\n", "\n", "We create a pipeline for categorical features that handles missing values and encodes categories."]}, {"cell_type": "code", "execution_count": 69, "id": "categorical_pipeline_code", "metadata": {}, "outputs": [], "source": ["# Create a preprocessing pipeline for categorical features and assign to 'categorical_pipeline'\n", "# - Class: Pipeline chains preprocessing steps for categorical data\n", "# - Steps:\n", "#   - ('imputer', SimpleImputer(strategy='most_frequent')): Fills missing values with mode\n", "#   - ('onehot', OneHotEncoder(handle_unknown='ignore')): Creates binary columns for each category\n", "#     - handle_unknown='ignore': Handles new categories in test data gracefully\n", "# - Returns: Pipeline object for categorical feature preprocessing\n", "\n", "categorical_pipeline = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='most_frequent')),\n", "    ('onehot', OneHotEncoder(handle_unknown='ignore'))\n", "])"]}, {"cell_type": "markdown", "id": "634b36d1", "metadata": {}, "source": ["## <a id='toc8_4_'></a>[Complete Preprocessing Pipeline](#toc0_)\n", "\n", "We combine both numerical and categorical pipelines into a single comprehensive preprocessor."]}, {"cell_type": "code", "execution_count": 71, "id": "complete_pipeline_code", "metadata": {}, "outputs": [], "source": ["# Create a comprehensive preprocessor using ColumnTransformer and assign to 'preprocessor'\n", "# - Class: ColumnTransformer applies different transformers to different column groups\n", "# - Parameters:\n", "#   - transformers: List of (name, transformer, columns) tuples\n", "#     - ('num', numerical_pipeline, numerical_features): Apply numerical pipeline to numeric columns\n", "#     - ('cat', categorical_pipeline, categorical_features): Apply categorical pipeline to categorical columns\n", "#   - remainder='drop': Drop any columns not specified in transformers\n", "# - Returns: ColumnTransformer that handles all feature preprocessing\n", "preprocessor = ColumnTransformer([\n", "    ('num', numerical_pipeline, numerical_features),\n", "    ('cat', categorical_pipeline, categorical_features)\n", "])\n", "\n", "# Create the complete preprocessing pipeline and assign to 'preprocessing_pipeline'\n", "# - Class: Pipeline wraps the ColumnTransformer for consistent interface\n", "# - Steps: Single step containing the complete preprocessor\n", "\n", "preprocessing_pipeline = Pipeline([\n", "    ('preprocessor', preprocessor)\n", "])\n"]}, {"cell_type": "code", "execution_count": 73, "id": "apply_preprocessing_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preprocessing completed:\n", "Original training shape: (91003, 22)\n", "Preprocessed training shape: (91003, 3492)\n", "Original validation shape: (22751, 22)\n", "Preprocessed validation shape: (22751, 3492)\n", "Original test shape: (28439, 22)\n", "Preprocessed test shape: (28439, 3492)\n", "\n", "Feature count increased by 3470 due to one-hot encoding\n"]}], "source": ["# Fit the preprocessing pipeline on training data and transform all sets\n", "# - Method: fit_transform() learns preprocessing parameters from training data and applies them\n", "# - Parameters: X_train provides the training features\n", "# - Returns: Preprocessed training features as numpy array\n", "X_train_preprocessed = preprocessing_pipeline.fit_transform(X_train)\n", "\n", "# Apply the fitted preprocessing to validation data (no fitting, only transformation)\n", "X_val_preprocessed = preprocessing_pipeline.transform(X_val)\n", "\n", "# Apply the fitted preprocessing to test data (no fitting, only transformation)\n", "# - Method: transform() applies learned preprocessing to test data\n", "# - Parameters: X_test provides the test features\n", "# - Returns: Preprocessed test features using training data parameters\n", "X_test_preprocessed = preprocessing_pipeline.transform(X_test)\n", "\n", "# Display shapes after preprocessing\n", "print(\"Preprocessing completed:\")\n", "print(f\"Original training shape: {X_train.shape}\")\n", "print(f\"Preprocessed training shape: {X_train_preprocessed.shape}\")\n", "print(f\"Original validation shape: {X_val.shape}\")\n", "print(f\"Preprocessed validation shape: {X_val_preprocessed.shape}\")\n", "print(f\"Original test shape: {X_test.shape}\")\n", "print(f\"Preprocessed test shape: {X_test_preprocessed.shape}\")\n", "\n", "# The increase in feature count is due to one-hot encoding of categorical variables\n", "feature_increase = X_train_preprocessed.shape[1] - X_train.shape[1]\n", "print(f\"\\nFeature count increased by {feature_increase} due to one-hot encoding\")\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "e2dc00dc", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "e66be40d", "metadata": {}, "source": ["# <a id='toc9_'></a>[Model Implementation](#toc0_)\n", "\n", "We implement logistic regression using scikit-learn's `LogisticRegression`, which uses optimization algorithms like Limited-memory BFGS (L-BFGS) to find the optimal parameters that minimize the logistic loss function."]}, {"cell_type": "markdown", "id": "fcd57280", "metadata": {}, "source": ["## <a id='toc9_1_'></a>[Model Initialization](#toc0_)"]}, {"cell_type": "code", "execution_count": 74, "id": "model_init_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logistic Regression Model Initialized:\n", "LogisticRegression(max_iter=1000, random_state=42)\n", "\n", "Model parameters:\n", "  Solver: lbfgs\n", "  Max iterations: 1000\n", "  Random state: 42\n"]}], "source": ["# Initialize the Logistic Regression model and assign to 'logistic_model'\n", "# - Class: LogisticRegression implements logistic regression for binary/multiclass classification\n", "# - Parameters:\n", "#   - max_iter=1000: Maximum number of iterations for the solver to converge\n", "#   - random_state=42: Ensures reproducible results across runs\n", "#   - solver='lbfgs': Uses L-BFGS algorithm (good for small datasets)\n", "# - Returns: Unfitted LogisticRegression instance\n", "logistic_model = LogisticRegression(\n", "    max_iter=1000,\n", "    random_state=42,\n", "    solver='lbfgs'\n", ")\n", "\n", "\n", "# Display the initialized model parameters\n", "print(\"Logistic Regression Model Initialized:\")\n", "print(logistic_model)\n", "print(f\"\\nModel parameters:\")\n", "print(f\"  Solver: {logistic_model.solver}\")\n", "print(f\"  Max iterations: {logistic_model.max_iter}\")\n", "print(f\"  Random state: {logistic_model.random_state}\")\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "f2a3146f", "metadata": {}, "source": ["## <a id='toc9_2_'></a>[Training](#toc0_)\n", "\n", "We fit the logistic regression model to the preprocessed training data to learn the optimal weights and bias."]}, {"cell_type": "code", "execution_count": 75, "id": "model_training_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model Training Completed!\n", "Number of iterations: 91\n", "\n", "Model Details:\n", "  Number of features used: 3492\n", "  Classes: ['No' 'Yes']\n", "  Intercept (bias): -2.6388\n", "  Number of coefficients: 3492\n"]}], "source": ["# Train the Logistic Regression model on preprocessed training data\n", "# - Method: fit() learns model parameters by minimizing the logistic loss function\n", "# - Parameters:\n", "#   - X_train_preprocessed: Preprocessed training features (scaled and encoded)\n", "#   - y_train: Training target values ('Yes'/'No' for rain tomorrow)\n", "# - Process: Uses maximum likelihood estimation to find optimal coefficients\n", "# - Returns: Fitted model (modifies 'logistic_model' in-place)\n", "logistic_model.fit(X_train_preprocessed,y_train)\n", "\n", "\n", "# Display training completion information\n", "print(\"Model Training Completed!\")\n", "print(f\"Number of iterations: {logistic_model.n_iter_[0]}\")\n", "\n", "# Extract and display key model parameters\n", "print(f\"\\nModel Details:\")\n", "print(f\"  Number of features used: {X_train_preprocessed.shape[1]}\")\n", "print(f\"  Classes: {logistic_model.classes_}\")\n", "print(f\"  Intercept (bias): {logistic_model.intercept_[0]:.4f}\")\n", "print(f\"  Number of coefficients: {len(logistic_model.coef_[0])}\")\n"]}, {"cell_type": "markdown", "id": "333f30bf", "metadata": {}, "source": ["## <a id='toc9_3_'></a>[Making Predictions](#toc0_)\n", "\n", "We use the trained model to make predictions on the validation and test data."]}, {"cell_type": "code", "execution_count": 77, "id": "prediction_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predictions Generated:\n", "Validation predictions: 22751\n", "Test predictions: 28439\n", "Unique predicted classes: ['No' 'Yes']\n", "\n", "Test set prediction distribution:\n", "  No: 23679 (83.3%)\n", "  Yes: 4760 (16.7%)\n"]}], "source": ["# Make predictions on preprocessed validation and test data\n", "# - Method: predict() applies the learned decision boundary to classify new data\n", "# - Process: Computes sigmoid(X * weights + bias) and applies 0.5 threshold\n", "# - Returns: NumPy array of predicted class labels ('Yes' or 'No')\n", "y_val_pred = logistic_model.predict(X_val_preprocessed)\n", "y_test_pred = logistic_model.predict(X_test_preprocessed)\n", "\n", "# Display prediction summary\n", "print(\"Predictions Generated:\")\n", "print(f\"Validation predictions: {len(y_val_pred)}\")\n", "print(f\"Test predictions: {len(y_test_pred)}\")\n", "print(f\"Unique predicted classes: {np.unique(y_test_pred)}\")\n", "\n", "# Show prediction distribution for test set\n", "test_prediction_counts = pd.Series(y_test_pred).value_counts()\n", "print(f\"\\nTest set prediction distribution:\")\n", "for class_label, count in test_prediction_counts.items():\n", "    percentage = (count / len(y_test_pred)) * 100\n", "    print(f\"  {class_label}: {count} ({percentage:.1f}%)\")\n", "\n"]}, {"cell_type": "markdown", "id": "d8b33182", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "103cdead", "metadata": {}, "source": ["# <a id='toc10_'></a>[Model Evaluation](#toc0_)\n"]}, {"cell_type": "markdown", "id": "322a4183", "metadata": {}, "source": ["We define a helper function that will be used consistently across all data splits for standardized evaluation and visualization."]}, {"cell_type": "code", "execution_count": 78, "id": "9852d571", "metadata": {}, "outputs": [], "source": ["\n", "# Helper function for evaluation with accuracy and confusion matrix\n", "def predict_and_plot(model, inputs, targets, name=''):\n", "    \"\"\"\n", "    Make predictions and display accuracy score and confusion matrix\n", "    \n", "    Parameters:\n", "    - model: Trained model\n", "    - inputs: Feature matrix\n", "    - targets: Target vector\n", "    - name: Name for the evaluation (e.g., 'Training', 'Validation', 'Test')\n", "    \n", "    Returns:\n", "    - preds: Model predictions\n", "    \"\"\"\n", "    preds = model.predict(inputs)\n", "    \n", "    accuracy = accuracy_score(targets, preds)\n", "    print(\"Accuracy: {:.2f}%\".format(accuracy * 100))\n", "    \n", "    cf = confusion_matrix(targets, preds, normalize='true')\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cf, annot=True, fmt='.3f', cmap='Blues')\n", "    plt.xlabel('Prediction')\n", "    plt.ylabel('Target')\n", "    plt.title('{} Confusion Matrix'.format(name))\n", "    plt.show()\n", "    \n", "    return preds\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "4a871158", "metadata": {}, "source": ["\n", "In classification problems, we can measure the performance of our model using metrics like accuracy, confusion matrix, precision, recall, and F1 score.\n", "\n", "- **Accuracy**: The percentage of predictions the model got right. For example, if it correctly predicts 85 out of 100 days, the accuracy is 85%.\n", "- **Precision**: Of the days the model predicted as \"rain,\" how many actually had rain? High precision means fewer false alarms (predicting rain when it doesn’t happen).\n", "- **Recall**: Of the days it actually rained, how many did the model correctly predict? High recall means the model catches most rainy days.\n", "- **F1 Score**: A balance between precision and recall. It’s useful when you care about both false alarms and missed rainy days.\n", "\n", "The **confusion matrix** is a table where:\n", "- **True Positives (TP)**: Correctly predicted \"Yes\" (rain).\n", "- **True Negatives (TN)**: Correctly predicted \"No\" (no rain).\n", "- **False Positives (FP)**: Predicted \"Yes\" but it was \"No\" (false alarm).\n", "- **False Negatives (FN)**: Predicted \"No\" but it was \"Yes\" (missed rain).\n", "\n", "<img src=\"https://raw.githubusercontent.com/henrylahteenmaki//Machine_Learning_Methods/refs/heads/main/figs/confusion_matrix_fig1.png\" width=\"800\">"]}, {"cell_type": "code", "execution_count": 79, "id": "model_evaluation_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "MODEL EVALUATION USING HELPER FUNCTION\n", "============================================================\n", "\n", "==============================\n", "TRAINING SET EVALUATION\n", "==============================\n", "Accuracy: 86.41%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==============================\n", "VALIDATION SET EVALUATION\n", "==============================\n", "Accuracy: 85.16%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==============================\n", "TEST SET EVALUATION\n", "==============================\n", "Accuracy: 85.54%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "PERFORMANCE SUMMARY ACROSS ALL SETS\n", "==================================================\n", "Training Accuracy:   0.8641 (86.41%)\n", "Validation Accuracy: 0.8516 (85.16%)\n", "Test Accuracy:       0.8554 (85.54%)\n", "\n", "Model Diagnosis:\n", "Train-Validation difference: 0.0124\n", "Validation-Test difference: 0.0038\n", "Moderate train-validation gap is acceptable\n", "Validation set is a good proxy for test performance\n"]}], "source": ["\n", "print(\"=\" * 60)\n", "print(\"MODEL EVALUATION USING HELPER FUNCTION\")\n", "print(\"=\" * 60)\n", "\n", "# Evaluate on Training Set\n", "print(\"\\n\" + \"=\" * 30)\n", "print(\"TRAINING SET EVALUATION\")\n", "print(\"=\" * 30)\n", "train_preds = predict_and_plot(logistic_model, X_train_preprocessed, y_train, name='Training')\n", "\n", "# Evaluate on Validation Set  \n", "print(\"\\n\" + \"=\" * 30)\n", "print(\"VALIDATION SET EVALUATION\")\n", "print(\"=\" * 30)\n", "val_preds = predict_and_plot(logistic_model, X_val_preprocessed, y_val, name='Validation')\n", "\n", "# Evaluate on Test Set\n", "print(\"\\n\" + \"=\" * 30)\n", "print(\"TEST SET EVALUATION\")\n", "print(\"=\" * 30)\n", "test_preds = predict_and_plot(logistic_model, X_test_preprocessed, y_test, name='Test')\n", "\n", "# Summary comparison of all three sets\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"PERFORMANCE SUMMARY ACROSS ALL SETS\")\n", "print(\"=\" * 50)\n", "\n", "train_accuracy = accuracy_score(y_train, train_preds)\n", "val_accuracy = accuracy_score(y_val, val_preds)\n", "test_accuracy = accuracy_score(y_test, test_preds)\n", "\n", "print(f\"Training Accuracy:   {train_accuracy:.4f} ({train_accuracy*100:.2f}%)\")\n", "print(f\"Validation Accuracy: {val_accuracy:.4f} ({val_accuracy*100:.2f}%)\")\n", "print(f\"Test Accuracy:       {test_accuracy:.4f} ({test_accuracy*100:.2f}%)\")\n", "\n", "# Check for overfitting/underfitting\n", "train_val_diff = abs(train_accuracy - val_accuracy)\n", "val_test_diff = abs(val_accuracy - test_accuracy)\n", "\n", "print(f\"\\nModel Diagnosis:\")\n", "print(f\"Train-Validation difference: {train_val_diff:.4f}\")\n", "print(f\"Validation-Test difference: {val_test_diff:.4f}\")\n", "\n", "if train_val_diff > 0.05:\n", "    print(\" Large train-validation gap suggests potential overfitting\")\n", "elif train_val_diff < 0.01:\n", "    print(\"Small train-validation gap suggests good generalization\")\n", "else:\n", "    print(\"Moderate train-validation gap is acceptable\")\n", "\n", "if val_test_diff < 0.03:\n", "    print(\"Validation set is a good proxy for test performance\")\n", "else:\n", "    print(\"Consider using different validation strategy\")\n", "\n"]}, {"cell_type": "markdown", "id": "classification_report_section", "metadata": {}, "source": ["## <a id='toc10_1_'></a>[Additional Evaluation Metrics](#toc0_)\n", "\n", "Let's also calculate additional metrics for comprehensive evaluation."]}, {"cell_type": "code", "execution_count": 87, "id": "additional_metrics_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "========================================\n", "ADDITIONAL TEST SET METRICS\n", "========================================\n", "Accuracy: 0.8554 (85.54%)\n", "Precision: 0.7378 (73.78%)\n", "Recall:    0.5509 (55.09%)\n", "F1-Score:  0.6308 (63.08%)\n", "ROC-AUC:   0.7472 (74.72%)\n"]}], "source": ["# Calculate additional metrics for test set\n", "test_precision = precision_score(y_test, test_preds, pos_label='Yes')\n", "test_recall = recall_score(y_test, test_preds, pos_label='Yes')\n", "test_f1 = f1_score(y_test, test_preds, pos_label='Yes')\n", "# Calculate metrics\n", "\n", "\n", "# Convert target labels to binary for ROC-AUC calculation\n", "y_binary = (y_test == 'Yes').astype(int)\n", "preds_binary = (test_preds == 'Yes').astype(int)\n", "test_auc = roc_auc_score(y_binary, preds_binary)\n", "\n", "\n", "# Display additional metrics\n", "print(\"\\n\" + \"=\" * 40)\n", "print(\"ADDITIONAL TEST SET METRICS\")\n", "print(\"=\" * 40)\n", "print(f\"Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)\")\n", "print(f\"Precision: {test_precision:.4f} ({test_precision*100:.2f}%)\")\n", "print(f\"Recall:    {test_recall:.4f} ({test_recall*100:.2f}%)\")\n", "print(f\"F1-Score:  {test_f1:.4f} ({test_f1*100:.2f}%)\")\n", "print(f\"ROC-AUC:   {test_auc:.4f} ({test_auc*100:.2f}%)\")\n", "\n", "# Generate classification report\n", "\n"]}, {"cell_type": "markdown", "id": "17bcda0d", "metadata": {}, "source": ["In this case, the test accuracy is 0.84, which means that the model correctly\n", "predicts 84% of the cases. The precision is 0.72, which means that 72% of\n", "the positive predictions are actually positive. The recall is 0.48, which\n", "means that 48% of the actual positives are predicted positive. The F1-score\n", "is 0.57, which is a balance between precision and recall."]}, {"cell_type": "markdown", "id": "e16b1ebb", "metadata": {}, "source": [" For classification problems, where the classes might be\n", "imbalanced, precision, recall, and the F1-score are more important than\n", "accuracy. This is because the model might be accurate but still have a low\n", "recall."]}, {"cell_type": "markdown", "id": "db337d30", "metadata": {}, "source": ["### <a id='toc10_1_1_'></a>[Comparing Accuracy to \"Dumb\" Model](#toc0_)\n", "\n", "The accuracy of the model on the test and validation set are above 84%, which suggests that our model generalizes well to data it hasn't seen before.\n", "\n", "But how good is 84% accuracy? While this depends on the nature of the problem and on business requirements, a good way to verify whether a model has actually learned something useful is to compare its results to a \"random\" or \"dumb\" model.\n", "\n", "Let's create two models: one that guesses randomly and another that always return \"No\". Both of these models completely ignore the inputs given to them."]}, {"cell_type": "code", "execution_count": 89, "id": "28b7b120", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 due to no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "MOST_FREQUENT Strategy:\n", "  Accuracy:  0.776 (77.6%)\n", "  Precision: 0.000 (0.0%)\n", "  Recall:    0.000 (0.0%)\n", "  F1-Score:  0.000\n", "  (This just predicts the most common class every time)\n", "\n", "STRATIFIED Strategy:\n", "  Accuracy:  0.651 (65.1%)\n", "  Precision: 0.220 (22.0%)\n", "  Recall:    0.219 (21.9%)\n", "  F1-Score:  0.219\n", "  (Random predictions matching training set class distribution)\n", "\n", "UNIFORM Strategy:\n", "  Accuracy:  0.499 (49.9%)\n", "  Precision: 0.223 (22.3%)\n", "  Recall:    0.498 (49.8%)\n", "  F1-Score:  0.308\n", "  (Random predictions with equal probability for each class)\n", "\n", "PRIOR Strategy:\n", "  Accuracy:  0.776 (77.6%)\n", "  Precision: 0.000 (0.0%)\n", "  Recall:    0.000 (0.0%)\n", "  F1-Score:  0.000\n", "  (Always predicts positive class probabilities from training)\n", "\n", "==================================================\n", "OUR MODEL Performance:\n", "  Accuracy:  0.855 (85.5%)\n", "  Precision: 0.738\n", "  Recall:    0.551\n", "  F1-Score:  0.631\n", "\n", "==================================================\n", "REALITY CHECK:\n", "Good news: Model beats best baseline accuracy by 8.0%\n", "\n", "F1-Score improvement over best baseline: 32.3%\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 due to no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}], "source": ["# Create different baseline models to establish performance floor\n", "from sklearn.dummy import DummyClassifier\n", "baseline_strategies = ['most_frequent', 'stratified', 'uniform', 'prior']\n", "baseline_results = {}\n", "\n", "for strategy in baseline_strategies:\n", "    # Create baseline model with specific strategy\n", "    baseline = DummyClassifier(strategy=strategy, random_state=42)\n", "\n", "    # Train on training data (even baselines need to see class distribution)\n", "    baseline.fit(X_train_preprocessed, y_train)\n", "\n", "    # Predict on test set\n", "    y_baseline_pred = baseline.predict(X_test_preprocessed)\n", "\n", "    # Calculate metrics: Added pos_label='Yes'\n", "    acc = accuracy_score(y_test, y_baseline_pred)\n", "    prec = precision_score(y_test, y_baseline_pred, pos_label='Yes')\n", "    rec = recall_score(y_test, y_baseline_pred, pos_label='Yes')\n", "    f1_baseline = f1_score(y_test, y_baseline_pred, pos_label='Yes')\n", "\n", "\n", "    print(f\"\\n{strategy.upper()} Strategy:\")\n", "    print(f\"  Accuracy:  {acc:.3f} ({acc*100:.1f}%)\")\n", "    print(f\"  Precision: {prec:.3f} ({prec*100:.1f}%)\")\n", "    print(f\"  Recall:    {rec:.3f} ({rec*100:.1f}%)\")\n", "    print(f\"  F1-Score:  {f1_baseline:.3f}\")\n", "    \n", "    if strategy == 'most_frequent':\n", "        print(\"  (This just predicts the most common class every time)\")\n", "    elif strategy == 'stratified':\n", "        print(\"  (Random predictions matching training set class distribution)\")\n", "    elif strategy == 'uniform':\n", "        print(\"  (Random predictions with equal probability for each class)\")\n", "    elif strategy == 'prior':\n", "        print(\"  (Always predicts positive class probabilities from training)\")\n", "    baseline_results[strategy] = {\n", "        'accuracy': acc,\n", "        'precision': prec,\n", "        'recall': rec,\n", "        'f1': f1_baseline\n", "    }\n", "\n", "# Now compare with our actual model\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"OUR MODEL Performance:\")\n", "print(f\"  Accuracy:  {test_accuracy:.3f} ({test_accuracy*100:.1f}%)\")\n", "print(f\"  Precision: {test_precision:.3f}\")\n", "print(f\"  Recall:    {test_recall:.3f}\")\n", "print(f\"  F1-Score:  {test_f1:.3f}\")\n", "\n", "# Check if we beat the baseline\n", "best_baseline_acc = max(baseline_results.values(), key=lambda x: x['accuracy'])['accuracy']\n", "best_baseline_f1 = max(baseline_results.values(), key=lambda x: x['f1'])['f1']\n", "\n", "improvement_acc = test_accuracy - best_baseline_acc\n", "improvement_f1 = test_f1 - best_baseline_f1\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"REALITY CHECK:\")\n", "if improvement_acc > 0.05:\n", "    print(f\"Good news: Model beats best baseline accuracy by {improvement_acc*100:.1f}%\")\n", "elif improvement_acc > 0:\n", "    print(f\"Barely winning: <PERSON> beats baseline accuracy by only {improvement_acc*100:.1f}%\")\n", "else:\n", "    print(f\"Bad news: Model LOSES to baseline accuracy by {abs(improvement_acc)*100:.1f}%\")\n", "\n", "print(f\"\\nF1-Score improvement over best baseline: {improvement_f1*100:.1f}%\")\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "c990175f", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "9a607241", "metadata": {}, "source": ["# <a id='toc11_'></a>[Saving and Loading the Model](#toc0_)\n", "\n", "We save the trained model and preprocessing pipeline using `joblib` to enable reuse without retraining."]}, {"cell_type": "markdown", "id": "a970cac5", "metadata": {}, "source": ["## <a id='toc11_1_'></a>[Saving the Model Package](#toc0_)"]}, {"cell_type": "code", "execution_count": 90, "id": "saving_save_code", "metadata": {}, "outputs": [{"data": {"text/plain": ["['weather_logistic_regression.joblib']"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a comprehensive dictionary to store all model components and assign to 'model_package'\n", "# - Purpose: Package all necessary components for model deployment and reuse\n", "# - Contents:\n", "#   - model: Trained logistic regression model\n", "#   - preprocessor: Complete preprocessing pipeline\n", "#   - feature_names: Original feature column names\n", "#   - target_classes: Class labels for prediction interpretation\n", "#   - evaluation_metrics: Performance metrics on test data\n", "#   - model_metadata: Additional information about the model\n", "model_package = {\n", "    'model': logistic_model,\n", "    'preprocessor': preprocessing_pipeline,\n", "    'feature_names': list(X_train.columns),\n", "    'target_classes': list(logistic_model.classes_),\n", "    'evaluation_metrics': {\n", "        'test_accuracy': test_accuracy,\n", "        'test_precision': test_precision,\n", "        'test_recall': test_recall,\n", "        'test_f1': test_f1,\n", "        'test_auc': test_auc\n", "    }\n", "}\n", "\n", "# Define the filename for saving the complete model package\n", "name = 'weather_logistic_regression.joblib'\n", "\n", "# Save the model package to disk using joblib\n", "# - Function: joblib.dump() efficiently serializes the complete model package\n", "# - Parameters: model_package (object to save), model_filename (destination file)\n", "# - Benefits: Preserves scikit-learn object structure and is more efficient than pickle\n", "joblib.dump(model_package, name)\n"]}, {"cell_type": "markdown", "id": "5c217faf", "metadata": {}, "source": ["## <a id='toc11_2_'></a>[Loading the Model Package](#toc0_)"]}, {"cell_type": "code", "execution_count": 92, "id": "saving_load_code", "metadata": {}, "outputs": [], "source": ["# Load the complete model package from disk and assign to 'loaded_package'\n", "# - Function: joblib.load() deserializes the saved model package\n", "# - Parameters: model_filename specifies the file to load\n", "# - Returns: Dictionary containing all saved model components\n", "loaded_package = joblib.load(name)\n", "\n", "# Extract components from the loaded package for practical use\n", "# - Extract the trained model for making predictions\n", "loaded_model = loaded_package['model']\n", "\n", "# - Extract the preprocessing pipeline to prepare new data\n", "loaded_preprocessor = loaded_package['preprocessor']\n", "\n", "# - Extract feature information for data validation\n", "loaded_feature_names = loaded_package['feature_names']\n"]}, {"cell_type": "markdown", "id": "ca75e738", "metadata": {}, "source": ["## <a id='toc11_3_'></a>[Using the Loaded Model](#toc0_)\n", "\n", "TASK: Code the below function to return predictions and probabilities"]}, {"cell_type": "code", "execution_count": 93, "id": "saving_extract_code", "metadata": {}, "outputs": [], "source": ["\n", "# Demonstrate prediction function for new data\n", "def predict_weather(new_data, model=loaded_model, preprocessor=loaded_preprocessor):\n", "    \"\"\"\n", "    Make weather predictions using the loaded model\n", "    \n", "    Parameters:\n", "    - new_data: DataFrame with same structure as training data\n", "    - model: Loaded logistic regression model\n", "    - preprocessor: Loaded preprocessing pipeline\n", "    \n", "    Returns:\n", "    - predictions: Predicted classes\n", "    - probabilities: Prediction probabilities\n", "    as\n", "    return predictions, probabilities\n", "    \"\"\"\n", "    # Preprocess the new data using the loaded pipeline\n", "    processed_data = preprocessor.transform(new_data)\n", "    \n", "    # Make predictions and get probabilities\n", "    predictions = model.predict(processed_data)\n", "    probabilities = model.predict_proba(processed_data)\n", "    \n", "    return predictions, probabilities\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "2a7cf99f", "metadata": {}, "source": ["TASK: use the fuction and print the predictions for the sample_data"]}, {"cell_type": "code", "execution_count": 94, "id": "d2485242", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['No', 'Yes', 'No', 'No', 'No'], dtype=object)"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Test the prediction function with a subset of test data\n", "sample_data = X_test.head(5)\n", "sample_predictions, sample_probabilities = predict_weather(sample_data)\n", "\n", "\n", "\n", "# Display prediction results\n", "sample_predictions\n"]}, {"cell_type": "markdown", "id": "42f27ea5", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "model_assumptions_section", "metadata": {}, "source": ["# <a id='toc12_'></a>[Summary](#toc0_)\n", "\n", "Understanding when and how to use logistic regression effectively is crucial for successful machine learning applications."]}, {"cell_type": "markdown", "id": "assumptions_content", "metadata": {}, "source": ["## <a id='toc12_1_'></a>[ Assumptions of Logistic Regression](#toc0_)\n", "\n", "**1. Linearity in Log-Odds**\n", "- The relationship between features and log-odds of the outcome should be linear\n", "- $\\log\\left(\\frac{p}{1-p}\\right) = w_0x_0 + w_1x_1 + ... + w_nx_n + b$\n", "\n", "**2. Independence of Observations**\n", "- Each observation should be independent of others\n", "- No autocorrelation in residuals (important for time series data)\n", "\n", "**3. No Multicollinearity**\n", "- Features should not be highly correlated with each other\n", "- High correlation can make coefficient interpretation unreliable\n", "\n", "**4. <PERSON> Sample Size**\n", "- Logistic regression requires adequate sample sizes for stable results\n", "- Rule of thumb: at least 10 events per predictor variable\n", "\n", "**5. No Extreme Outliers**\n", "- Outliers can disproportionately influence the model\n", "- Unlike linear regression, logistic regression is somewhat robust to outliers in the response variable\n", "\n", "---\n", "\n", "## <a id='toc12_2_'></a>[ Limitations](#toc0_)\n", "\n", "**1. Linear Decision Boundary**\n", "- Can only model linear relationships between features and log-odds\n", "- Cannot capture complex non-linear patterns without feature engineering\n", "- May underperform with complex datasets\n", "\n", "**2. Sensitive to Outliers in Features**\n", "- Extreme values in predictor variables can significantly impact coefficients\n", "- Feature scaling often necessary for optimal performance\n", "\n", "**3. Poor Performance with Imbalanced Data**\n", "- May be biased toward majority class without proper handling\n", "- Requires techniques like resampling, class weights, or threshold adjustment\n", "\n", "**4. Requires Complete Separation Handling**\n", "- Problems arise when classes are perfectly separated by features\n", "- Can lead to infinite coefficient estimates\n", "\n", "**5. <PERSON>aling Sensitivity**\n", "- Performance can vary significantly with unscaled features\n", "- Regularization effectiveness depends on proper scaling\n", "\n", "---\n", "\n", "## <a id='toc12_3_'></a>[ Best Use Cases](#toc0_)\n", "\n", "**1. Binary Classification Problems**\n", "- Email spam detection (spam/not spam)\n", "- Medical diagnosis (disease/no disease)\n", "- Marketing response prediction (respond/not respond)\n", "- Credit approval (approve/deny)\n", "\n", "**2. Baseline Model Development**\n", "- Excellent starting point for classification projects\n", "- Provides interpretable results for stakeholder communication\n", "- Helps establish performance benchmarks\n", "\n", "**3. Interpretability Requirements**\n", "- Coefficient interpretation shows feature importance and direction\n", "- Odds ratios provide intuitive understanding of feature effects\n", "- Regulatory compliance where model explainability is required\n", "\n", "**4. Probability Estimation**\n", "- When you need well-calibrated probability estimates\n", "- Risk assessment applications\n", "- Decision-making under uncertainty\n", "\n", "**5. Limited Data Scenarios**\n", "- Works well with smaller datasets compared to complex models\n", "- Less prone to overfitting than complex algorithms\n", "- Computationally efficient for real-time applications\n", "\n", "**6. Feature Selection and Analysis**\n", "- Coefficient analysis helps identify important features\n", "- Statistical significance testing of predictors\n", "- Understanding linear relationships in data\n", "\n"]}, {"cell_type": "markdown", "id": "609bce55", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}