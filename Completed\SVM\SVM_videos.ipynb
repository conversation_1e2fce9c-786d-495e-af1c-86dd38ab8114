{"cells": [{"cell_type": "markdown", "id": "aa4a253f", "metadata": {}, "source": ["# <a id='toc1_'></a>[SVM Videos](#toc0_)"]}, {"cell_type": "markdown", "id": "c0392de0", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "b4a8267c", "metadata": {}, "source": ["# TASK\n", "\n", " Watch the below videos and make notes and take sreenshots about them in this Jupiter Notebook. If you want you can use AI for the Python code for visualizations of the concepts. After adding your notes, deliver this Jupiter Notebook to the delivery folder in Learn. "]}, {"cell_type": "markdown", "id": "28900224", "metadata": {}, "source": ["[Theory video - Support Vector Machines Part 1 (of 3): Main Ideas!!!](https://www.youtube.com/watch?v=efR1C6CvhmE&list=PLblh5JKOoLUL3IJ4-yor0HzkqDQ3JmJkc)\n", "\n", "[Theory video - Support Vector Machines Part 2: The Polynomial Kernel (Part 2 of 3)](https://www.youtube.com/watch?v=Toet3EiSFcM&list=PLblh5JKOoLUL3IJ4-yor0HzkqDQ3JmJkc&index=2)\n", "\n", "[Theory video - Support Vector Machines Part 3: The Radial (RBF) Kernel (Part 3 of 3)](https://www.youtube.com/watch?v=Qc5IyLW_hns&list=PLblh5JKOoLUL3IJ4-yor0HzkqDQ3JmJkc&index=3)\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "e7594808", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "8ab5a30d", "metadata": {}, "source": ["## Support Vector Machines: Main Ideas\n", "\n", "### Core Concept\n", "\n", "Support Vector Machines (SVMs) are supervised learning models used for classification and regression analysis. The core idea is to find the optimal hyperplane that maximizes the margin between different classes.\n", "\n", "### Key Components\n", "\n", "- **Hyperplane:** The decision boundary that separates different classes in the feature space.\n", "- **Margin:** The distance between the hyperplane and the closest data points (support vectors).\n", "- **Support Vectors:** The data points closest to the decision boundary that influence its position and orientation.\n", "\n", "### How SVMs Work\n", "\n", "SVMs aim to find the hyperplane with the maximum margin, which provides the best generalization ability. This helps in classifying unseen data more accurately.\n", "\n", "- **Linear SVM:** Used when data is linearly separable, creating a straight line (in 2D) or flat plane (in higher dimensions) to separate classes.\n", "- **Non-linear SVM:** Employs kernel functions to map data to higher dimensions where it becomes linearly separable.\n", "\n", "### Common Kernel Functions\n", "\n", "- **Linear Kernel:** $K(x,y) = x·y$\n", "- **Polynomial Kernel:** $K(x,y) = (x·y + c)ᵈ$\n", "- **Radial Basis Function (RBF):** $K(x,y) = exp(-γ||x-y||²)$\n", "- **<PERSON><PERSON><PERSON><PERSON>:** $K(x,y) = tanh(αx·y + c)$\n", "\n", "### Advantages of SVMs\n", "\n", "- Effective in high-dimensional spaces\n", "- Memory efficient as only support vectors are used\n", "- Versatile through different kernel functions\n", "- Robust against overfitting, especially in high-dimensional space\n", "\n", "### Limitations\n", "\n", "- Not suitable for large datasets due to high training time\n", "- Less effective when classes overlap significantly\n", "- Requires careful selection of kernel functions and parameters\n", "- No probabilistic explanation for classification\n", "\n", "### Applications\n", "\n", "- Text and hypertext categorization\n", "- Image classification\n", "- Biological data analysis\n", "- Handwriting recognition\n", "\n", "## Polynomial Kernels in SVMs\n", "\n", "### Understanding Polynomial Kernels\n", "\n", "The polynomial kernel is a popular kernel function used in SVMs to capture non-linear relationships in data. It allows the SVM to create more complex decision boundaries than linear kernels.\n", "\n", "### Mathematical Representation\n", "\n", "The polynomial kernel function is defined as:\n", "\n", "$K(x,y) = (x·y + c)ᵈ$\n", "\n", "Where:\n", "\n", "- x and y are feature vectors\n", "- c is a constant that trades off the influence of higher-order versus lower-order terms\n", "- d is the degree of the polynomial\n", "\n", "### Properties of Polynomial Kernels\n", "\n", "- **Flexibility:** The degree parameter d controls the flexibility of the decision boundary\n", "- **Feature Mapping:** Implicitly maps data to higher dimensions without explicitly calculating the transformation\n", "- **Computational Efficiency:** More efficient than explicitly computing features in high-dimensional space\n", "\n", "### Effect of Degree Parameter\n", "\n", "- **d = 1:** Equivalent to a linear kernel\n", "- **d = 2:** Creates quadratic decision boundaries, capturing more complex patterns\n", "- **d = 3 or higher:** Generates increasingly complex boundaries, but risks overfitting\n", "\n", "### Choosing Polynomial Degree\n", "\n", "The optimal degree depends on the dataset's complexity:\n", "\n", "- Lower degrees (d=2,3) are often sufficient for moderately complex problems\n", "- Higher degrees can capture more intricate patterns but require more training data\n", "- Cross-validation helps determine the optimal degree for a specific problem\n", "\n", "### Advantages of Polynomial Kernels\n", "\n", "- Effective for problems where interactions between features are important\n", "- Well-suited for normalized data\n", "- More interpretable than some other kernel functions\n", "\n", "### Common Applications\n", "\n", "- Natural language processing tasks\n", "- Image recognition with structured patterns\n", "- Bioinformatics for analyzing protein sequences\n", "\n", "## Radial Basis Function (RBF) Kernel in SVMs\n", "\n", "### Understanding the RBF Kernel\n", "\n", "The Radial Basis Function (RBF) kernel, also known as the Gaussian kernel, is one of the most popular kernel functions used in SVMs for non-linear classification tasks.\n", "\n", "### Mathematical Definition\n", "\n", "The RBF kernel is defined as:\n", "\n", "$K(x,y) = exp(-γ||x-y||²)$\n", "\n", "Where:\n", "\n", "- x and y are feature vectors\n", "- ||x-y|| is the Euclidean distance between the vectors\n", "- γ (gamma) is a parameter that controls the width of the Gaussian function\n", "\n", "### Key Properties of the RBF Kernel\n", "\n", "- **Infinite Dimensionality:** Theoretically maps data to infinite-dimensional space\n", "- **Locality:** The influence of each support vector is localized to its neighborhood\n", "- **Flexibility:** Can model complex decision boundaries with proper parameter tuning\n", "- **Universality:** Can approximate any smooth function with enough training data\n", "\n", "### The Gamma Parameter\n", "\n", "- **Small γ:** Creates a smoother decision boundary with wider influence of each example\n", "- **Large γ:** Creates more complex, tighter decision boundaries that might overfit\n", "- **Intuition:** γ represents the inverse of the radius of influence for support vectors\n", "\n", "### When to Use the RBF Kernel\n", "\n", "- When the relationship between features and classes is non-linear\n", "- When the number of features is smaller than the number of samples\n", "- For datasets where complex decision boundaries are needed\n", "- As a default kernel when you have no prior knowledge about the data structure\n", "\n", "### Parameter Tuning\n", "\n", "- **C parameter:** Controls trade-off between smooth decision boundary and classifying training points correctly\n", "- **γ parameter:** Defines how far the influence of a single training example reaches\n", "- **Cross-validation:** Grid search and cross-validation are commonly used to find optimal parameters\n", "\n", "### Advantages of the RBF Kernel\n", "\n", "- Captures complex non-linear relationships in data\n", "- Effective when dealing with data that has many features\n", "- Often performs well even with minimal parameter tuning\n", "- Creates smooth decision boundaries\n", "\n", "### Applications\n", "\n", "- Image classification and computer vision tasks\n", "- Bioinformatics and genomic data analysis\n", "- Financial data prediction\n", "- Medical diagnosis systems"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}