{"cells": [{"cell_type": "markdown", "id": "d80443a0", "metadata": {}, "source": ["# <a id='toc1_'></a>[Elastic Net Regression - Finding the Optimal L1/L2 Balance with Scikit-Learn](#toc0_)"]}, {"cell_type": "markdown", "id": "ef072d66", "metadata": {}, "source": ["**Table of contents**<a id='toc0_'></a>    \n", "- [Elastic Net Regression - Finding the Optimal L1/L2 Balance with Scikit-Learn](#toc1_)    \n", "- [Theory](#toc2_)    \n", "- [Problem Statement](#toc3_)    \n", "- [Introduction](#toc4_)    \n", "  - [L2 Regularization (Ridge Regression)](#toc4_1_)    \n", "  - [L1 Regularization (Lasso Regression)](#toc4_2_)    \n", "  - [Elastic Net - The Best of Both Worlds](#toc4_3_)    \n", "- [Importing Libraries](#toc5_)    \n", "- [Loading and Preparing the Dataset](#toc6_)    \n", "  - [Loading the Dataset](#toc6_1_)    \n", "  - [Exploratory Data Analysis](#toc6_2_)    \n", "- [Data Preprocessing](#toc7_)    \n", "  - [Separating Features and Target](#toc7_1_)    \n", "  - [Train/Test Split](#toc7_2_)    \n", "  - [Preprocessing Pipelines](#toc7_3_)    \n", "  - [Applying Preprocessing](#toc7_4_)    \n", "- [Model Implementation and Comparison](#toc8_)    \n", "  - [Linear Regression (Baseline)](#toc8_1_)    \n", "  - [Lasso Regression (L1)](#toc8_2_)    \n", "  - [Ridge Regression (L2)](#toc8_3_)    \n", "  - [Elastic Net with Hyperparameter Tuning](#toc8_4_)    \n", "- [Model Evaluation](#toc9_)    \n", "  - [Performance Comparison](#toc9_1_)    \n", "- [Saving and Loading the Model](#toc10_)    \n", "  - [Saving the Model Package](#toc10_1_)    \n", "  - [Loading a the Model](#toc10_2_)    \n", "  - [Using the Model](#toc10_3_)    \n", "\n", "<!-- vscode-jupyter-toc-config\n", "\tnumbering=false\n", "\tanchor=true\n", "\tflat=false\n", "\tminLevel=1\n", "\tmaxLevel=6\n", "\t/vscode-jupyter-toc-config -->\n", "<!-- THIS CELL WILL BE REPLACED ON TOC UPDATE. DO NOT WRITE YOUR TEXT IN THIS CELL -->"]}, {"cell_type": "markdown", "id": "25d2f079", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "f34969a9", "metadata": {}, "source": ["# <a id='toc2_'></a>[Theory](#toc0_)"]}, {"cell_type": "markdown", "id": "f1bae663", "metadata": {}, "source": ["[Theory video - Regularization Part 1: Ridge (L2) Regression](https://www.youtube.com/watch?v=Q81RR3yKn30)\n", "\n", "[Theory video - Regularization Part 2: Lasso (L1) Regression](https://www.youtube.com/watch?v=NGf0voTMlcs)\n", "\n", "[Theory video - Ridge vs Lasso Regression, Visualized!!!](https://www.youtube.com/watch?v=Xm2C_gTAl8c)\n", "\n", "[Theory video - Regularization Part 3: Elastic Net Regression](https://www.youtube.com/watch?v=1dKRdX9bfIo)"]}, {"cell_type": "markdown", "id": "b864eb46", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "997bbe9c", "metadata": {}, "source": ["# <a id='toc3_'></a>[Problem Statement](#toc0_)\n", "\n", "You are asked to predict what is the price of a house based on its caracteristics like location, number of rooms etc."]}, {"cell_type": "markdown", "id": "b2b8a6b7", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "understanding_regularization", "metadata": {}, "source": ["# <a id='toc4_'></a>[Introduction](#toc0_)\n", "\n", "When training machine learning models, we want them to perform well on both training data and new unseen data (generalization). However, complex models can **overfit** by memorizing training data rather than learning patterns. Regularization helps prevent this by adding penalties to the loss function."]}, {"cell_type": "markdown", "id": "ridge_section", "metadata": {}, "source": ["## <a id='toc4_1_'></a>[L2 Regularization (Ridge Regression)](#toc0_)\n", "\n", "L2 regularization adds a penalty proportional to the sum of squared coefficients:\n", "\n", "$$ \\text{Ridge Loss} = \\text{MSE} + \\alpha \\sum_{i=1}^{n} w_i^2 $$\n", "\n", "**Characteristics:**\n", "- **Effect**: Shrinks coefficients toward zero but keeps all features\n", "- **Geometry**: Creates a circular constraint region\n", "- **Best for**: Datasets with multicollinearity, when all features contribute\n", "\n", "**Mathematical Intuition**: The penalty term $\\alpha \\sum w_i^2$ penalizes large coefficients quadratically, encouraging the model to distribute weights more evenly across features."]}, {"cell_type": "markdown", "id": "lasso_section", "metadata": {}, "source": ["## <a id='toc4_2_'></a>[L1 Regularization (Lasso Regression)](#toc0_)\n", "\n", "L1 regularization adds a penalty proportional to the sum of absolute coefficient values:\n", "\n", "$$ \\text{<PERSON>so Loss} = \\text{MSE} + \\alpha \\sum_{i=1}^{n} |w_i| $$\n", "\n", "**Characteristics:**\n", "- **Effect**: Can set coefficients to exactly zero (automatic feature selection)\n", "- **Geometry**: Creates a diamond-shaped constraint region\n", "- **Best for**: High-dimensional data, when feature selection is desired\n", "\n", "**Mathematical Intuition**: The penalty term $\\alpha \\sum |w_i|$ has corners at coordinate axes, making it likely for the optimal solution to have some coefficients equal to zero."]}, {"cell_type": "markdown", "id": "elastic_net_section", "metadata": {}, "source": ["## <a id='toc4_3_'></a>[Elastic Net - The Best of Both Worlds](#toc0_)\n", "\n", "Elastic Net combines L1 and L2 regularization with a mixing parameter:\n", "\n", "$$ \\text{Elastic Net Loss} = \\text{MSE} + \\alpha \\left[ \\text{l1\\_ratio} \\cdot \\sum |w_i| + (1 - \\text{l1\\_ratio}) \\cdot \\sum w_i^2 \\right] $$\n", "\n", "**Parameters:**\n", "- **$\\alpha$**: Controls overall regularization strength (higher = more penalty)\n", "- **l1_ratio**: Mixing parameter between L1 and L2 penalties\n", "  - `l1_ratio = 0.0` → Pure Ridge regression\n", "  - `l1_ratio = 1.0` → Pure Lasso regression\n", "  - `0 < l1_ratio < 1` → Elastic Net combination\n", "\n", "**Advantages:**\n", "- **Balanced approach**: Feature selection + coefficient shrinkage\n", "- **Handles correlated features**: Better than <PERSON><PERSON> for grouped variables\n", "- **Flexible**: Can adapt to different data characteristics through tuning"]}, {"cell_type": "markdown", "id": "d0059821", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "imports_section", "metadata": {}, "source": ["# <a id='toc5_'></a>[Importing Libraries](#toc0_)"]}, {"cell_type": "code", "execution_count": 1, "id": "import_code", "metadata": {}, "outputs": [], "source": ["\n", "# Import core data analysis and visualization libraries\n", "# - numpy for numerical operations and array manipulations\n", "# - pandas for data manipulation and analysis\n", "# - matplotlib.pyplot for basic plotting functionality\n", "# - seaborn for enhanced statistical visualizations\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "\n", "# Import utility for saving/loading Python objects\n", "import joblib\n", "\n", "\n", "# Import scikit-learn modules for machine learning workflow:\n", "# - train_test_split for splitting data into train/test sets\n", "# - Pipeline for creating sequential transformation workflows\n", "# - ColumnTransformer for applying different transformers to different columns\n", "# - SimpleImputer for handling missing values with various strategies\n", "# - StandardScaler for feature scaling/normalization\n", "# - OneHotEncoder for encoding categorical variables\n", "from sklearn.model_selection import train_test_split, GridSearchCV\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "\n", "\n", "# Import linear model implementations:\n", "# - LinearRegression for ordinary least squares (baseline)\n", "# - <PERSON><PERSON> for L1 regularized linear regression\n", "# - Ridge for L2 regularized linear regression  \n", "# - ElasticNet for combined L1/L2 regularized linear regression\n", "from sklearn.linear_model import LinearRegression, Lasso, Ridge, ElasticNet\n", "\n", "\n", "# Import evaluation metrics:\n", "# - mean_squared_error for MSE calculation\n", "# - root_mean_squared_error for RMSE calculation\n", "# - r2_score for coefficient of determination\n", "from sklearn.metrics import mean_squared_error, root_mean_squared_error, r2_score\n", "\n", "\n", "# Set random seed for reproducible results across all operations\n", "# - Ensures consistent train/test splits and model initialization\n", "np.random.seed(42)\n", "\n", "\n", "# Configure plotting parameters for better visualizations\n", "plt.rcParams['figure.figsize'] = (10, 6)\n", "plt.rcParams['font.size'] = 12\n", "sns.set_style('whitegrid')\n"]}, {"cell_type": "markdown", "id": "d717e604", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "ca415295", "metadata": {}, "source": ["# <a id='toc6_'></a>[Loading and Preparing the Dataset](#toc0_)"]}, {"cell_type": "markdown", "id": "a0c84e20", "metadata": {}, "source": ["## <a id='toc6_1_'></a>[Loading the Dataset](#toc0_)"]}, {"cell_type": "code", "execution_count": 2, "id": "load_data_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Overview:\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Id", "rawType": "int64", "type": "integer"}, {"name": "MSSubClass", "rawType": "int64", "type": "integer"}, {"name": "MSZoning", "rawType": "object", "type": "string"}, {"name": "LotFrontage", "rawType": "float64", "type": "float"}, {"name": "LotArea", "rawType": "int64", "type": "integer"}, {"name": "Street", "rawType": "object", "type": "string"}, {"name": "Alley", "rawType": "object", "type": "unknown"}, {"name": "LotShape", "rawType": "object", "type": "string"}, {"name": "LandContour", "rawType": "object", "type": "string"}, {"name": "Utilities", "rawType": "object", "type": "string"}, {"name": "LotConfig", "rawType": "object", "type": "string"}, {"name": "LandSlope", "rawType": "object", "type": "string"}, {"name": "Neighborhood", "rawType": "object", "type": "string"}, {"name": "Condition1", "rawType": "object", "type": "string"}, {"name": "Condition2", "rawType": "object", "type": "string"}, {"name": "BldgType", "rawType": "object", "type": "string"}, {"name": "HouseStyle", "rawType": "object", "type": "string"}, {"name": "OverallQual", "rawType": "int64", "type": "integer"}, {"name": "OverallCond", "rawType": "int64", "type": "integer"}, {"name": "YearBuilt", "rawType": "int64", "type": "integer"}, {"name": "YearRemodAdd", "rawType": "int64", "type": "integer"}, {"name": "RoofStyle", "rawType": "object", "type": "string"}, {"name": "RoofMatl", "rawType": "object", "type": "string"}, {"name": "Exterior1st", "rawType": "object", "type": "string"}, {"name": "Exterior2nd", "rawType": "object", "type": "string"}, {"name": "MasVnrType", "rawType": "object", "type": "unknown"}, {"name": "MasVnrArea", "rawType": "float64", "type": "float"}, {"name": "ExterQual", "rawType": "object", "type": "string"}, {"name": "ExterCond", "rawType": "object", "type": "string"}, {"name": "Foundation", "rawType": "object", "type": "string"}, {"name": "BsmtQual", "rawType": "object", "type": "string"}, {"name": "BsmtCond", "rawType": "object", "type": "string"}, {"name": "BsmtExposure", "rawType": "object", "type": "string"}, {"name": "BsmtFinType1", "rawType": "object", "type": "string"}, {"name": "BsmtFinSF1", "rawType": "int64", "type": "integer"}, {"name": "BsmtFinType2", "rawType": "object", "type": "string"}, {"name": "BsmtFinSF2", "rawType": "int64", "type": "integer"}, {"name": "BsmtUnfSF", "rawType": "int64", "type": "integer"}, {"name": "TotalBsmtSF", "rawType": "int64", "type": "integer"}, {"name": "Heating", "rawType": "object", "type": "string"}, {"name": "HeatingQC", "rawType": "object", "type": "string"}, {"name": "CentralAir", "rawType": "object", "type": "string"}, {"name": "Electrical", "rawType": "object", "type": "string"}, {"name": "1stFlrSF", "rawType": "int64", "type": "integer"}, {"name": "2ndFlrSF", "rawType": "int64", "type": "integer"}, {"name": "LowQualFinSF", "rawType": "int64", "type": "integer"}, {"name": "GrLivArea", "rawType": "int64", "type": "integer"}, {"name": "BsmtFullBath", "rawType": "int64", "type": "integer"}, {"name": "BsmtHalfBath", "rawType": "int64", "type": "integer"}, {"name": "FullBath", "rawType": "int64", "type": "integer"}, {"name": "HalfBath", "rawType": "int64", "type": "integer"}, {"name": "BedroomAbvGr", "rawType": "int64", "type": "integer"}, {"name": "KitchenAbvGr", "rawType": "int64", "type": "integer"}, {"name": "KitchenQual", "rawType": "object", "type": "string"}, {"name": "TotRmsAbvGrd", "rawType": "int64", "type": "integer"}, {"name": "Functional", "rawType": "object", "type": "string"}, {"name": "Fireplaces", "rawType": "int64", "type": "integer"}, {"name": "FireplaceQu", "rawType": "object", "type": "unknown"}, {"name": "GarageType", "rawType": "object", "type": "string"}, {"name": "GarageYrBlt", "rawType": "float64", "type": "float"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rawType": "object", "type": "string"}, {"name": "GarageCars", "rawType": "int64", "type": "integer"}, {"name": "GarageArea", "rawType": "int64", "type": "integer"}, {"name": "GarageQual", "rawType": "object", "type": "string"}, {"name": "GarageCond", "rawType": "object", "type": "string"}, {"name": "PavedDrive", "rawType": "object", "type": "string"}, {"name": "WoodDeckSF", "rawType": "int64", "type": "integer"}, {"name": "OpenPorchSF", "rawType": "int64", "type": "integer"}, {"name": "EnclosedPorch", "rawType": "int64", "type": "integer"}, {"name": "3SsnPorch", "rawType": "int64", "type": "integer"}, {"name": "ScreenPorch", "rawType": "int64", "type": "integer"}, {"name": "PoolArea", "rawType": "int64", "type": "integer"}, {"name": "PoolQC", "rawType": "object", "type": "unknown"}, {"name": "<PERSON><PERSON>", "rawType": "object", "type": "unknown"}, {"name": "MiscFeature", "rawType": "object", "type": "unknown"}, {"name": "MiscVal", "rawType": "int64", "type": "integer"}, {"name": "MoSold", "rawType": "int64", "type": "integer"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "rawType": "int64", "type": "integer"}, {"name": "SaleType", "rawType": "object", "type": "string"}, {"name": "SaleCondition", "rawType": "object", "type": "string"}, {"name": "SalePrice", "rawType": "int64", "type": "integer"}], "ref": "c507656b-d710-48da-a976-1668922ad070", "rows": [["0", "1", "60", "RL", "65.0", "8450", "Pave", null, "Reg", "Lvl", "AllPub", "Inside", "Gtl", "CollgCr", "Norm", "Norm", "1Fam", "2Story", "7", "5", "2003", "2003", "Gable", "CompShg", "VinylSd", "VinylSd", "BrkFace", "196.0", "Gd", "TA", "PConc", "Gd", "TA", "No", "GLQ", "706", "Unf", "0", "150", "856", "GasA", "Ex", "Y", "SBrkr", "856", "854", "0", "1710", "1", "0", "2", "1", "3", "1", "Gd", "8", "<PERSON><PERSON>", "0", null, "Attchd", "2003.0", "RFn", "2", "548", "TA", "TA", "Y", "0", "61", "0", "0", "0", "0", null, null, null, "0", "2", "2008", "WD", "Normal", "208500"], ["1", "2", "20", "RL", "80.0", "9600", "Pave", null, "Reg", "Lvl", "AllPub", "FR2", "Gtl", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Norm", "1Fam", "1Story", "6", "8", "1976", "1976", "Gable", "CompShg", "MetalSd", "MetalSd", null, "0.0", "TA", "TA", "<PERSON>lock", "Gd", "TA", "Gd", "ALQ", "978", "Unf", "0", "284", "1262", "GasA", "Ex", "Y", "SBrkr", "1262", "0", "0", "1262", "0", "1", "2", "0", "3", "1", "TA", "6", "<PERSON><PERSON>", "1", "TA", "Attchd", "1976.0", "RFn", "2", "460", "TA", "TA", "Y", "298", "0", "0", "0", "0", "0", null, null, null, "0", "5", "2007", "WD", "Normal", "181500"], ["2", "3", "60", "RL", "68.0", "11250", "Pave", null, "IR1", "Lvl", "AllPub", "Inside", "Gtl", "CollgCr", "Norm", "Norm", "1Fam", "2Story", "7", "5", "2001", "2002", "Gable", "CompShg", "VinylSd", "VinylSd", "BrkFace", "162.0", "Gd", "TA", "PConc", "Gd", "TA", "Mn", "GLQ", "486", "Unf", "0", "434", "920", "GasA", "Ex", "Y", "SBrkr", "920", "866", "0", "1786", "1", "0", "2", "1", "3", "1", "Gd", "6", "<PERSON><PERSON>", "1", "TA", "Attchd", "2001.0", "RFn", "2", "608", "TA", "TA", "Y", "0", "42", "0", "0", "0", "0", null, null, null, "0", "9", "2008", "WD", "Normal", "223500"], ["3", "4", "70", "RL", "60.0", "9550", "Pave", null, "IR1", "Lvl", "AllPub", "Corner", "Gtl", "Crawfor", "Norm", "Norm", "1Fam", "2Story", "7", "5", "1915", "1970", "Gable", "CompShg", "Wd Sdng", "Wd Shng", null, "0.0", "TA", "TA", "BrkTil", "TA", "Gd", "No", "ALQ", "216", "Unf", "0", "540", "756", "GasA", "Gd", "Y", "SBrkr", "961", "756", "0", "1717", "1", "0", "1", "0", "3", "1", "Gd", "7", "<PERSON><PERSON>", "1", "Gd", "Detchd", "1998.0", "Unf", "3", "642", "TA", "TA", "Y", "0", "35", "272", "0", "0", "0", null, null, null, "0", "2", "2006", "WD", "Abnorml", "140000"], ["4", "5", "60", "RL", "84.0", "14260", "Pave", null, "IR1", "Lvl", "AllPub", "FR2", "Gtl", "<PERSON><PERSON><PERSON>", "Norm", "Norm", "1Fam", "2Story", "8", "5", "2000", "2000", "Gable", "CompShg", "VinylSd", "VinylSd", "BrkFace", "350.0", "Gd", "TA", "PConc", "Gd", "TA", "Av", "GLQ", "655", "Unf", "0", "490", "1145", "GasA", "Ex", "Y", "SBrkr", "1145", "1053", "0", "2198", "1", "0", "2", "1", "4", "1", "Gd", "9", "<PERSON><PERSON>", "1", "TA", "Attchd", "2000.0", "RFn", "3", "836", "TA", "TA", "Y", "192", "84", "0", "0", "0", "0", null, null, null, "0", "12", "2008", "WD", "Normal", "250000"]], "shape": {"columns": 81, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Id</th>\n", "      <th>MSSubClass</th>\n", "      <th>MSZoning</th>\n", "      <th>LotFrontage</th>\n", "      <th>LotArea</th>\n", "      <th>Street</th>\n", "      <th>Alley</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>LandContour</th>\n", "      <th>Utilities</th>\n", "      <th>...</th>\n", "      <th>PoolArea</th>\n", "      <th>PoolQC</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>MiscFeature</th>\n", "      <th>MiscVal</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>YrSold</th>\n", "      <th>SaleType</th>\n", "      <th>SaleCondition</th>\n", "      <th>SalePrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>65.0</td>\n", "      <td>8450</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>208500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>20</td>\n", "      <td>RL</td>\n", "      <td>80.0</td>\n", "      <td>9600</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>2007</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>181500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>11250</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>223500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>70</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>9550</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2006</td>\n", "      <td>WD</td>\n", "      <td>Abnorml</td>\n", "      <td>140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>60</td>\n", "      <td>RL</td>\n", "      <td>84.0</td>\n", "      <td>14260</td>\n", "      <td>Pave</td>\n", "      <td>NaN</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>2008</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>250000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 81 columns</p>\n", "</div>"], "text/plain": ["   Id  MSSubClass MSZoning  LotFrontage  LotArea Street Alley LotShape  \\\n", "0   1          60       RL         65.0     8450   Pave   NaN      Reg   \n", "1   2          20       RL         80.0     9600   Pave   NaN      Reg   \n", "2   3          60       RL         68.0    11250   Pave   NaN      IR1   \n", "3   4          70       RL         60.0     9550   Pave   NaN      IR1   \n", "4   5          60       RL         84.0    14260   Pave   NaN      IR1   \n", "\n", "  LandContour Utilities  ... PoolArea PoolQC Fence MiscFeature MiscVal MoSold  \\\n", "0         Lvl    AllPub  ...        0    NaN   NaN         NaN       0      2   \n", "1         Lvl    AllPub  ...        0    NaN   NaN         NaN       0      5   \n", "2         Lvl    AllPub  ...        0    NaN   NaN         NaN       0      9   \n", "3         Lvl    AllPub  ...        0    NaN   NaN         NaN       0      2   \n", "4         Lvl    AllPub  ...        0    NaN   NaN         NaN       0     12   \n", "\n", "  YrSold  SaleType  SaleCondition  SalePrice  \n", "0   2008        WD         Normal     208500  \n", "1   2007        WD         Normal     181500  \n", "2   2008        WD         Normal     223500  \n", "3   2006        WD        Abnorml     140000  \n", "4   2008        WD         Normal     250000  \n", "\n", "[5 rows x 81 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Dataset shape: (1460, 81)\n", "Number of samples: 1460\n", "Number of features: 80\n"]}], "source": ["\n", "# Load the House Prices dataset from CSV file and assign to 'df'\n", "# - Function: pd.read_csv() reads data from a CSV file\n", "# - Parameters: filename specifies the path to the CSV file\n", "# - Returns: Pandas DataFrame containing the house price dataset\n", "# - Dataset contains: Multiple features (numerical/categorical) and SalePrice target\n", "url = 'https://raw.githubusercontent.com/henrylahteenmaki/Machine_Learning_Methods/refs/heads/main/datasets/House_Prices_Elastic.csv'\n", "df = pd.read_csv(url)\n", "\n", "\n", "# Display the first 5 rows of the dataset for initial inspection\n", "# - Method: head() returns the first n rows (default 5)\n", "# - Purpose: Quick overview of data structure and content\n", "# - Shows: Column names, data types, and sample values\n", "print(\"Dataset Overview:\")\n", "display(df.head())\n", "\n", "\n", "# Display dataset dimensions and basic information\n", "# - Method: len() returns number of rows in DataFrame\n", "# - Purpose: Understanding dataset size for modeling decisions\n", "print(f\"\\nDataset shape: {df.shape}\")\n", "print(f\"Number of samples: {len(df)}\")\n", "print(f\"Number of features: {df.shape[1] - 1}\")"]}, {"cell_type": "markdown", "id": "fbbb8235", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "76e503b9", "metadata": {}, "source": ["## <a id='toc6_2_'></a>[Exploratory Data Analysis](#toc0_)\n", "\n", "We examine the dataset structure, identify missing values, and understand feature distributions to inform our preprocessing strategy."]}, {"cell_type": "code", "execution_count": 3, "id": "eda_missing_values", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing Values Analysis:\n", "==============================\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "c2235b25-e620-49f8-bb33-7062cc497aa2", "rows": [["LotFrontage", "259"], ["Alley", "1369"], ["MasVnrType", "872"], ["MasVnrArea", "8"], ["BsmtQual", "37"], ["BsmtCond", "37"], ["BsmtExposure", "38"], ["BsmtFinType1", "37"], ["BsmtFinType2", "38"], ["Electrical", "1"], ["FireplaceQu", "690"], ["GarageType", "81"], ["GarageYrBlt", "81"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "81"], ["GarageQual", "81"], ["GarageCond", "81"], ["PoolQC", "1453"], ["<PERSON><PERSON>", "1179"], ["MiscFeature", "1406"]], "shape": {"columns": 1, "rows": 19}}, "text/plain": ["LotFrontage      259\n", "Alley           1369\n", "MasVnrType       872\n", "MasVnrArea         8\n", "BsmtQual          37\n", "BsmtCond          37\n", "BsmtExposure      38\n", "BsmtFinType1      37\n", "BsmtFinType2      38\n", "Electrical         1\n", "FireplaceQu      690\n", "GarageType        81\n", "GarageYrBlt       81\n", "Garage<PERSON><PERSON>sh      81\n", "GarageQual        81\n", "GarageCond        81\n", "PoolQC          1453\n", "Fence           1179\n", "MiscFeature     1406\n", "dtype: int64"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Total columns with missing values: 19\n", "Maximum missing values in any column: 1453\n", "\n", "Dataset Info:\n", "Total features: 81\n", "Numerical features: 38\n", "Categorical features: 43\n"]}], "source": ["\n", "# Examine missing values in the dataset\n", "# - Method: isnull().sum() counts missing values per column\n", "# - Purpose: Identify columns requiring imputation\n", "# - Returns: Series with column names and missing value counts\n", "missing_values = df.isnull().sum()\n", "\n", "\n", "print(\"Missing Values Analysis:\")\n", "print(\"=\" * 30)\n", "# Display only columns with missing values for clarity\n", "missing_columns = missing_values[missing_values > 0]\n", "if len(missing_columns) > 0:\n", "    display(missing_columns)\n", "    print(f\"\\nTotal columns with missing values: {len(missing_columns)}\")\n", "    print(f\"Maximum missing values in any column: {missing_columns.max()}\")\n", "else:\n", "    print(\"✓ No missing values found in the dataset\")\n", "\n", "\n", "# Display basic dataset information\n", "print(f\"\\nDataset Info:\")\n", "print(f\"Total features: {df.shape[1]}\")\n", "print(f\"Numerical features: {len(df.select_dtypes(include='number').columns)}\")\n", "print(f\"Categorical features: {len(df.select_dtypes(include='object').columns)}\")\n"]}, {"cell_type": "markdown", "id": "be875ca9", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "58279e8a", "metadata": {}, "source": ["# <a id='toc7_'></a>[Data Preprocessing](#toc0_)\n", "\n", "We implement a systematic preprocessing pipeline following machine learning best practices to prepare data for regularized regression models."]}, {"cell_type": "markdown", "id": "feature_target_split", "metadata": {}, "source": ["## <a id='toc7_1_'></a>[Separating Features and Target](#toc0_)"]}, {"cell_type": "code", "execution_count": 4, "id": "feature_target_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature-Target Separation:\n", "Features (X) shape: (1460, 80)\n", "Target (y) shape: (1460,)\n", "Target variable: SalePrice\n", "\n", "Target statistics:\n", "Mean: $180,921\n", "Median: $163,000\n", "Min: $34,900\n", "Max: $755,000\n"]}], "source": ["# Separate features (X) and target variable (y) for supervised learning\n", "# - X contains all independent variables used for prediction\n", "# - y contains the dependent variable we want to predict (SalePrice)\n", "# Extract feature columns by dropping the target column\n", "# - Method: drop() removes specified columns from DataFrame\n", "# - Parameters: 'SalePrice' specifies target column to remove, axis=1 for columns\n", "# - Returns: DataFrame containing only feature columns\n", "# Extract the target column for prediction\n", "# - Syntax: DataFrame['column'] selects single column as Series\n", "# - Returns: Pandas Series containing target values (house sale prices)\n", "X = df.drop('SalePrice', axis=1)\n", "y = df['SalePrice']\n", "\n", "\n", "print(\"Feature-Target Separation:\")\n", "print(f\"Features (X) shape: {X.shape}\")\n", "print(f\"Target (y) shape: {y.shape}\")\n", "print(f\"Target variable: {y.name}\")\n", "print(f\"\\nTarget statistics:\")\n", "print(f\"Mean: ${y.mean():,.0f}\")\n", "print(f\"Median: ${y.median():,.0f}\")\n", "print(f\"Min: ${y.min():,.0f}\")\n", "print(f\"Max: ${y.max():,.0f}\")\n"]}, {"cell_type": "markdown", "id": "train_test_split", "metadata": {}, "source": ["## <a id='toc7_2_'></a>[Train/Test Split](#toc0_)\n", "\n", "**CRITICAL**: We split the data BEFORE any preprocessing to prevent data leakage and ensure valid model evaluation."]}, {"cell_type": "code", "execution_count": 5, "id": "train_test_split_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train/Test Split Results:\n", "Training set - Features: (1168, 80), Target: (1168,)\n", "Testing set - Features: (292, 80), Target: (292,)\n", "\n", "Split proportions:\n", "Training: 80.0% (1168 samples)\n", "Testing: 20.0% (292 samples)\n", "\n", "Target distribution verification:\n", "Training set mean: $181,442\n", "Testing set mean: $178,840\n", "Distribution similarity: ✓ Good\n"]}], "source": ["\n", "# Split the dataset into training and testing sets\n", "# - Function: train_test_split() randomly divides data into train/test portions\n", "# - Parameters:\n", "#   - X, y: Feature matrix and target vector to split\n", "#   - test_size=0.2: Reserve 20% of data for testing, 80% for training\n", "#   - random_state=42: Ensures reproducible results across runs\n", "# - Returns: X_train, X_test, y_train, y_test arrays\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, \n", "    test_size=0.2, \n", "    random_state=42,\n", "\n", "\n", ")\n", "\n", "\n", "# Display the dimensions of the split datasets\n", "print(\"Train/Test Split Results:\")\n", "print(f\"Training set - Features: {X_train.shape}, Target: {y_train.shape}\")\n", "print(f\"Testing set - Features: {X_test.shape}, Target: {y_test.shape}\")\n", "print(f\"\\nSplit proportions:\")\n", "print(f\"Training: {len(X_train)/len(X)*100:.1f}% ({len(X_train)} samples)\")\n", "print(f\"Testing: {len(X_test)/len(X)*100:.1f}% ({len(X_test)} samples)\")\n", "\n", "\n", "# Verify target distribution consistency across splits\n", "print(f\"\\nTarget distribution verification:\")\n", "print(f\"Training set mean: ${y_train.mean():,.0f}\")\n", "print(f\"Testing set mean: ${y_test.mean():,.0f}\")\n", "print(f\"Distribution similarity: {'✓ Good' if abs(y_train.mean() - y_test.mean()) / y_train.mean() < 0.05 else '⚠ Check'}\")\n"]}, {"cell_type": "markdown", "id": "preprocessing_pipelines", "metadata": {}, "source": ["## <a id='toc7_3_'></a>[Preprocessing Pipelines](#toc0_)\n", "\n", "We create systematic preprocessing pipelines to handle numerical and categorical features appropriately for regularized regression models."]}, {"cell_type": "code", "execution_count": 6, "id": "feature_identification", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature Type Classification:\n", "Numerical features (37):\n", "  Index(['Id', 'MSSubClass', 'LotFrontage', 'LotArea', 'OverallQual'], dtype='object')...\n", "\n", "Categorical features (43):\n", "  Index(['MSZoning', 'Street', 'Alley', 'LotShape', 'LandContour'], dtype='object')...\n", "\n", "Total features: 80\n"]}], "source": ["# Identify numerical and categorical features for systematic preprocessing\n", "# - Method: select_dtypes() filters columns by data type\n", "# - include='number': Selects numerical columns (int, float)\n", "# - include='object': Selects categorical/text columns (strings)\n", "# - Returns: Column names for each feature type\n", "numerical_features = X_train.select_dtypes(include='number').columns\n", "categorical_features = X_train.select_dtypes(include='object').columns\n", "\n", "print(\"Feature Type Classification:\")\n", "print(f\"Numerical features ({len(numerical_features)}):\")\n", "print(f\"  {numerical_features[:5]}{'...' if len(numerical_features) > 5 else ''}\")\n", "print(f\"\\nCategorical features ({len(categorical_features)}):\")\n", "print(f\"  {categorical_features[:5]}{'...' if len(categorical_features) > 5 else ''}\")\n", "print(f\"\\nTotal features: {len(numerical_features) + len(categorical_features)}\")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "pipeline_creation", "metadata": {}, "outputs": [], "source": ["# Create systematic preprocessing pipelines for different feature types\n", "# Pipeline for numerical features\n", "# 1. Impute missing values using the mean of each column\n", "# 2. Scale features to have zero mean and unit variance (critical for regularization)\n", "# - Pipeline: Ensures consistent sequential application of transformations\n", "# - SimpleImputer: <PERSON><PERSON> missing values with mean strategy for numerical data\n", "# - StandardScaler: Normalizes features for optimal regularization performance\n", "# Pipeline for categorical features\n", "# 1. Impute missing values using the most frequent value of each column\n", "# 2. Apply One-Hot Encoding, handling unknown categories during prediction\n", "# - SimpleImputer: Uses mode imputation for categorical variables\n", "# - OneHotEncoder: Creates binary columns, drops first to avoid multicollinearity\n", "# - handle_unknown='ignore': Gracefully handles new categories in test data\n", "# - sparse_output=False: Returns dense arrays for easier handling\n", "# Combine pipelines using ColumnTransformer\n", "# - ColumnTransformer: Applies different transformations to specified column types\n", "# - 'num': applies numeric_pipeline to numerical_features\n", "# - 'cat': applies categorical_pipeline to categorical_features\n", "# - transformers: List of (name, transformer, columns) tuples\n", "# Create final preprocessing pipeline\n", "# - Pipeline: Wraps preprocessor for consistent interface\n", "# - Enables easy integration with model pipelines if needed\n", "# - Provides fit_transform and transform methods\n", "\n", "num_pipeline = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='mean')),\n", "    ('scaler', StandardScaler())\n", "])\n", "\n", "cat_pipeline = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='most_frequent')),\n", "    ('encoder', OneHotEncoder(handle_unknown='ignore', sparse_output=False))\n", "])\n", "\n", "preprocessor = ColumnTransformer([\n", "    ('num', num_pipeline, numerical_features),\n", "    ('cat', cat_pipeline, categorical_features)\n", "])\n", "\n", "final_pipeline = Pipeline([\n", "    ('preprocessor', preprocessor)\n", "])\n"]}, {"cell_type": "markdown", "id": "applying_preprocessing", "metadata": {}, "source": ["## <a id='toc7_4_'></a>[Applying Preprocessing](#toc0_)\n", "\n", "**CRITICAL**: We fit the preprocessing pipeline ONLY on training data to prevent data leakage, then apply the learned transformations to both training and test sets."]}, {"cell_type": "code", "execution_count": 8, "id": "apply_preprocessing", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Preprocessing Results:\n", "Original training features shape: (1168, 80)\n", "Processed training features shape: (1168, 286)\n", "Original test features shape: (292, 80)\n", "Processed test features shape: (292, 286)\n", "\n", "Feature expansion: 206 additional features\n", "(Due to one-hot encoding of categorical variables)\n", "\n", "Preprocessing Quality Check:\n", "✓ No missing values in processed training data: True\n", "✓ No missing values in processed test data: True\n", "✓ Feature shapes match between train/test: True\n"]}], "source": ["# Apply preprocessing to training and test data\n", "# - Method: fit_transform() learns parameters from training data and applies transformation\n", "# - Method: transform() applies learned parameters to test data (no learning from test)\n", "# - Critical: Prevents data leakage by learning only from training data\n", "# Fit preprocessing on training data and transform\n", "# - Learns imputation values, scaling parameters, and encoding mappings from training set ONLY\n", "# - Applies learned transformations to training features\n", "# - Returns: Preprocessed training features as numpy array\n", "# Transform test data using preprocessing fitted on training data\n", "# - Applies same imputation, scaling, and encoding learned from training\n", "# - Ensures consistent preprocessing without test set leakage\n", "# - Returns: Preprocessed test features with same transformations\n", "X_train_preprocessed = final_pipeline.fit_transform(X_train)\n", "X_test_preprocessed = final_pipeline.transform(X_test)\n", "\n", "\n", "# Display preprocessing results\n", "print(\"\\nPreprocessing Results:\")\n", "print(f\"Original training features shape: {X_train.shape}\")\n", "print(f\"Processed training features shape: {X_train_preprocessed.shape}\")\n", "print(f\"Original test features shape: {X_test.shape}\")\n", "print(f\"Processed test features shape: {X_test_preprocessed.shape}\")\n", "print(f\"\\nFeature expansion: {X_train_preprocessed.shape[1] - X_train.shape[1]} additional features\")\n", "print(f\"(Due to one-hot encoding of categorical variables)\")\n", "\n", "\n", "# Verify preprocessing quality\n", "print(f\"\\nPreprocessing Quality Check:\")\n", "print(f\"✓ No missing values in processed training data: {not np.isnan(X_train_preprocessed).any()}\")\n", "print(f\"✓ No missing values in processed test data: {not np.isnan(X_test_preprocessed).any()}\")\n", "print(f\"✓ Feature shapes match between train/test: {X_train_preprocessed.shape[1] == X_test_preprocessed.shape[1]}\")\n"]}, {"cell_type": "markdown", "id": "bf9bc2f4", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "model_implementation", "metadata": {}, "source": ["# <a id='toc8_'></a>[Model Implementation and Comparison](#toc0_)\n", "\n", "We implement and compare different linear regression approaches: baseline Linear Regression, Lasso (L1), Ridge (L2), and Elastic Net with optimized hyperparameters."]}, {"cell_type": "markdown", "id": "linear_baseline", "metadata": {}, "source": ["## <a id='toc8_1_'></a>[Linear Regression (Baseline)](#toc0_)\n", "\n", "We start with ordinary least squares linear regression as our baseline model for comparison."]}, {"cell_type": "code", "execution_count": 9, "id": "linear_regression_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Linear Regression (Baseline) Results:\n", "Mean Squared Error: 2,498,842,983,393,033,200,332,701,696\n", "Root Mean Squared Error: $49,988,428,494,933\n", "Number of coefficients: 286\n", "Model complexity: No regularization (potential overfitting)\n"]}], "source": ["# Create and train Linear Regression model (baseline)\n", "# - Class: LinearRegression implements ordinary least squares\n", "# - No regularization: Minimizes MSE without penalty terms\n", "# - Parameters: Default settings for baseline comparison\n", "# - Returns: Unfitted LinearRegression instance\n", "# Train the model on preprocessed training data\n", "# - Method: fit() computes optimal coefficients using normal equation\n", "# - Parameters: X_train_preprocessed, y_train provide training data\n", "# - Modifies: Updates model coefficients and intercept in-place\n", "# Make predictions on test set\n", "# - Method: predict() applies learned parameters to test features\n", "# - Parameters: X_test_preprocessed provides preprocessed test features\n", "# - Returns: Array of predicted house prices\n", "# Calculate performance metrics\n", "# - MSE: Mean squared error for penalty-sensitive evaluation\n", "# - RMSE: Root mean squared error in interpretable units\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train_preprocessed, y_train)\n", "y_pred_lr = lr_model.predict(X_test_preprocessed)\n", "mse_linear = mean_squared_error(y_test, y_pred_lr)\n", "rmse_linear = root_mean_squared_error(y_test, y_pred_lr)\n", "r2_linear = r2_score(y_test, y_pred_lr)\n", "\n", "\n", "\n", "print(\"Linear Regression (Baseline) Results:\")\n", "print(f\"Mean Squared Error: {mse_linear:,.0f}\")\n", "print(f\"Root Mean Squared Error: ${rmse_linear:,.0f}\")\n", "print(f\"Number of coefficients: {len(lr_model.coef_)}\")\n", "print(f\"Model complexity: No regularization (potential overfitting)\")\n", "\n"]}, {"cell_type": "markdown", "id": "lasso_implementation", "metadata": {}, "source": ["## <a id='toc8_2_'></a>[Lasso Regression (L1)](#toc0_)\n", "\n", "We implement Lasso regression with L1 regularization for automatic feature selection."]}, {"cell_type": "code", "execution_count": 10, "id": "lasso_regression_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Lasso Regression (L1) Results:\n", "Mean Squared Error: 797,525,646\n", "Root Mean Squared Error: $28,240\n", "\n", "Feature Selection Analysis:\n", "Selected features: 263/286 (92.0%)\n", "Eliminated features: 23 (8.0%)\n", "Sparsity achieved: Moderate\n"]}], "source": ["# Create and train Lasso Regression model with L1 regularization\n", "# - Class: <PERSON><PERSON> implements L1 regularized linear regression\n", "# - Parameters:\n", "#   - alpha=1.0: Regularization strength (higher = more penalty)\n", "#   - max_iter=10000: Maximum iterations for coordinate descent solver\n", "# - Effect: Can set coefficients to exactly zero (feature selection)\n", "# Train the Lasso model on preprocessed training data\n", "# - Method: fit() uses coordinate descent to optimize L1-penalized loss\n", "# - Objective: Minimize MSE + alpha * sum(|coefficients|)\n", "# - Result: Sparse coefficient vector with some zero values\n", "# Generate predictions on test set\n", "# - Method: predict() applies learned sparse coefficients\n", "# - Benefits: Uses only selected features for prediction\n", "# Calculate performance metrics\n", "# Analyze feature selection results\n", "# - Count non-zero coefficients to assess sparsity\n", "# - Compare with total features to measure selection rate\n", "laso_model = Lasso(alpha=1.0, max_iter=10000)\n", "laso_model.fit(X_train_preprocessed, y_train)\n", "y_pred_lasso = laso_model.predict(X_test_preprocessed)\n", "mse_lasso = mean_squared_error(y_test, y_pred_lasso)\n", "rmse_lasso = root_mean_squared_error(y_test, y_pred_lasso)\n", "n_selected_features = np.sum(laso_model.coef_ != 0)\n", "total_features = len(laso_model.coef_)\n", "\n", "print(\"Lasso Regression (L1) Results:\")\n", "print(f\"Mean Squared Error: {mse_lasso:,.0f}\")\n", "print(f\"Root Mean Squared Error: ${rmse_lasso:,.0f}\")\n", "print(f\"\\nFeature Selection Analysis:\")\n", "print(f\"Selected features: {n_selected_features}/{total_features} ({n_selected_features/total_features*100:.1f}%)\")\n", "print(f\"Eliminated features: {total_features - n_selected_features} ({(total_features - n_selected_features)/total_features*100:.1f}%)\")\n", "print(f\"Sparsity achieved: {'High' if n_selected_features/total_features < 0.5 else 'Moderate'}\")"]}, {"cell_type": "markdown", "id": "ridge_implementation", "metadata": {}, "source": ["## <a id='toc8_3_'></a>[Ridge Regression (L2)](#toc0_)\n", "\n", "We implement Ridge regression with L2 regularization for coefficient shrinkage while retaining all features."]}, {"cell_type": "code", "execution_count": 11, "id": "ridge_regression_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ridge Regression (L2) Results:\n", "Mean Squared Error: 890,606,216\n", "Root Mean Squared Error: $29,843\n", "\n", "Regularization Analysis:\n", "All features retained: 286\n", "Average coefficient magnitude: 7946.2374\n", "Shrinkage vs. linear regression: 0.000x\n", "Regularization effect: Strong\n"]}], "source": ["# Create and train Ridge Regression model with L2 regularization\n", "# - Class: Ridge implements L2 regularized linear regression\n", "# - Parameters:\n", "#   - alpha=1.0: Regularization strength controlling penalty magnitude\n", "#   - max_iter=10000: Maximum iterations for iterative solver\n", "# - Effect: Shrinks coefficients toward zero but keeps all features\n", "# Train the Ridge model on preprocessed training data\n", "# - Method: fit() optimizes L2-penalized least squares objective\n", "# - Objective: Minimize MSE + alpha * sum(coefficients^2)\n", "# - Result: Shrunken but non-zero coefficients for all features\n", "# Generate predictions on test set\n", "# - Method: predict() uses all features with regularized coefficients\n", "# - Benefits: Stable predictions with reduced overfitting\n", "# Calculate performance metrics\n", "ridge_model = Ridge(alpha=1.0, max_iter=10000)\n", "ridge_model.fit(X_train_preprocessed, y_train)\n", "y_pred_ridge = ridge_model.predict(X_test_preprocessed)\n", "mse_ridge = mean_squared_error(y_test, y_pred_ridge)\n", "rmse_ridge = root_mean_squared_error(y_test, y_pred_ridge)\n", "\n", "\n", "# Analyze coefficient shrinkage\n", "# - Compare coefficient magnitudes with linear regression\n", "# - Assess regularization effect on model complexity\n", "coef_magnitude_ridge = np.mean(np.abs(ridge_model.coef_))\n", "coef_magnitude_linear = np.mean(np.abs(lr_model.coef_))\n", "shrinkage_factor = coef_magnitude_ridge / coef_magnitude_linear\n", "\n", "\n", "print(\"Ridge Regression (L2) Results:\")\n", "print(f\"Mean Squared Error: {mse_ridge:,.0f}\")\n", "print(f\"Root Mean Squared Error: ${rmse_ridge:,.0f}\")\n", "print(f\"\\nRegularization Analysis:\")\n", "print(f\"All features retained: {len(ridge_model.coef_)}\")\n", "print(f\"Average coefficient magnitude: {coef_magnitude_ridge:.4f}\")\n", "print(f\"Shrinkage vs. linear regression: {shrinkage_factor:.3f}x\")\n", "print(f\"Regularization effect: {'Strong' if shrinkage_factor < 0.5 else 'Moderate' if shrinkage_factor < 0.8 else 'Mild'}\")"]}, {"cell_type": "markdown", "id": "elastic_net_implementation", "metadata": {}, "source": ["## <a id='toc8_4_'></a>[Elastic Net with Hyperparameter Tuning](#toc0_)\n", "\n", "We implement Elastic Net regression with systematic hyperparameter optimization using GridSearchCV to find the optimal balance between L1 and L2 regularization."]}, {"cell_type": "code", "execution_count": 12, "id": "elastic_net_setup", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Elastic Net Hyperparameter Grid:\n", "Alpha values: [0.001, 0.01, 0.1, 1.0, 10.0, 100.0]\n", "L1 ratio values: [0.01, 0.1, 0.25, 0.5, 0.75, 0.9, 0.99, 1.0]\n", "Total combinations: 48\n", "\n", "Grid Search Configuration:\n", "- 5-fold cross-validation for robust evaluation\n", "- Negative MSE scoring (GridSearchCV maximizes scores)\n", "- Parallel processing for faster computation\n"]}], "source": ["# Create Elastic Net model for hyperparameter tuning\n", "# - Class: ElasticNet combines L1 and L2 regularization\n", "# - Parameters:\n", "#   - max_iter=10000: Sufficient iterations for convergence\n", "#   - random_state=42: Reproducible results\n", "# - Hyperparameters to tune: alpha (strength) and l1_ratio (L1/L2 balance)\n", "# Define comprehensive parameter grid for hyperparameter search\n", "# - Dictionary maps parameter names to lists of candidate values\n", "# - alpha: Regularization strength from weak to strong\n", "#   - 0.001, 0.01: Weak regularization (close to linear regression)\n", "#   - 0.1, 1.0: Moderate regularization (balanced penalty)\n", "#   - 10.0, 100.0: Strong regularization (heavy penalty)\n", "# - l1_ratio: Mixing parameter between L1 and L2 penalties\n", "#   - 0.01: Nearly pure Ridge (L2-dominated)\n", "#   - 0.1, 0.25: L2-dominated with some L1\n", "#   - 0.5: Balanced L1/L2 combination\n", "#   - 0.75, 0.9: L1-dominated with some L2\n", "#   - 0.99, 1.0: Nearly pure Lasso (L1-dominated)\n", "ela_model = ElasticNet(max_iter=10000, random_state=42)\n", "param_grid = {\n", "    'alpha': [0.001, 0.01, 0.1, 1.0, 10.0, 100.0],\n", "    'l1_ratio': [0.01, 0.1, 0.25, 0.5, 0.75, 0.9, 0.99, 1.0]\n", "}\n", "\n", "\n", "print(\"Elastic Net Hyperparameter Grid:\")\n", "print(f\"Alpha values: {param_grid['alpha']}\")\n", "print(f\"L1 ratio values: {param_grid['l1_ratio']}\")\n", "print(f\"Total combinations: {len(param_grid['alpha']) * len(param_grid['l1_ratio'])}\")\n", "print(f\"\\nGrid Search Configuration:\")\n", "print(f\"- 5-fold cross-validation for robust evaluation\")\n", "print(f\"- Negative MSE scoring (GridSearchCV maximizes scores)\")\n", "print(f\"- Parallel processing for faster computation\")\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "id": "grid_search_execution", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fitting 5 folds for each of 48 candidates, totalling 240 fits\n", "\n", "✓ Grid Search completed successfully!\n"]}], "source": ["# Set up GridSearchCV for systematic hyperparameter optimization\n", "# - Class: GridSearchCV performs exhaustive search over parameter grid\n", "# - Parameters:\n", "#   - estimator: The Elastic Net model to optimize\n", "#   - param_grid: Dictionary of parameters and candidate values\n", "#   - scoring: 'neg_mean_squared_error' for MSE optimization\n", "#   - cv=5: 5-fold cross-validation for robust performance estimation\n", "#   - verbose=1: Progress updates during search\n", "#   - n_jobs=-1: Use all available CPU cores for parallel processing\n", "# - Note: 'neg_mean_squared_error' because GridSearchCV maximizes scores\n", "grid_search = GridSearchCV(\n", "    estimator=ela_model,\n", "    param_grid=param_grid,\n", "    scoring='neg_mean_squared_error',\n", "    cv=5,\n", "    verbose=1,\n", "    n_jobs=-1\n", ")\n", "\n", "# Execute grid search on training data\n", "# - Method: fit() trains models for all parameter combinations\n", "# - Process: For each combination, performs 5-fold CV and computes mean score\n", "# - Result: Identifies best parameters based on cross-validation performance\n", "grid_search.fit(X_train_preprocessed, y_train)\n", "\n", "\n", "print(\"\\n✓ Grid Search completed successfully!\")\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "grid_search_results", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Grid Search Results:\n", "========================================\n", "Best parameters found: {'alpha': 100.0, 'l1_ratio': 1.0}\n", "Best cross-validation score (Negative MSE): -1139710358.10\n", "Best cross-validation score (MSE): 1,139,710,358\n", "\n", "Optimal Regularization Analysis:\n", "Alpha (regularization strength): 100.0\n", "L1 ratio (L1/L2 balance): 1.0\n", "Model behavior: Behaving like <PERSON><PERSON> (strong L1 penalty, feature selection)\n"]}], "source": ["# Extract and analyze best parameters from grid search\n", "# - Attribute: best_params_ contains optimal hyperparameter values\n", "# - Attribute: best_score_ contains best cross-validation score\n", "# - Attribute: best_estimator_ contains trained model with best parameters\n", "best_params = grid_search.best_params_\n", "best_score = grid_search.best_score_\n", "\n", "print(\"Grid Search Results:\")\n", "print(\"=\" * 40)\n", "print(f\"Best parameters found: {best_params}\")\n", "print(f\"Best cross-validation score (Negative MSE): {best_score:.2f}\")\n", "print(f\"Best cross-validation score (MSE): {-best_score:,.0f}\")\n", "\n", "\n", "# Interpret the optimal l1_ratio value\n", "best_l1_ratio = best_params['l1_ratio']\n", "best_alpha = best_params['alpha']\n", "\n", "\n", "print(f\"\\nOptimal Regularization Analysis:\")\n", "print(f\"Alpha (regularization strength): {best_alpha}\")\n", "print(f\"L1 ratio (L1/L2 balance): {best_l1_ratio}\")\n", "\n", "\n", "# Interpret l1_ratio value to understand model behavior\n", "if np.isclose(best_l1_ratio, 1.0, atol=0.05):\n", "    interpretation = \"Behaving like <PERSON><PERSON> (strong L1 penalty, feature selection)\"\n", "elif np.isclose(best_l1_ratio, 0.01, atol=0.05):\n", "    interpretation = \"Behaving like <PERSON> (strong L2 penalty, coefficient shrinkage)\"\n", "elif best_l1_ratio > 0.7:\n", "    interpretation = \"L1-dominated Elastic Net (feature selection with some shrinkage)\"\n", "elif best_l1_ratio < 0.3:\n", "    interpretation = \"L2-dominated Elastic Net (shrinkage with minimal feature selection)\"\n", "else:\n", "    interpretation = \"Balanced Elastic Net (equal emphasis on L1 and L2)\"\n", "\n", "\n", "print(f\"Model behavior: {interpretation}\")"]}, {"cell_type": "code", "execution_count": 15, "id": "elastic_net_evaluation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimized Elastic Net Performance:\n", "==================================================\n", "Mean Squared Error: 806,041,688\n", "Root Mean Squared Error: $28,391\n", "R² Score: 0.8949 (89.5% variance explained)\n", "\n", "Regularization Effects:\n", "Selected features: 114/286 (39.9%)\n", "Feature elimination: 172 features removed\n", "Average coefficient magnitude: 3847.8176\n", "\n", "Improvement vs. Baseline Linear Regression:\n", "MSE improvement: +100.0% (Better)\n", "RMSE improvement: +100.0% (Better)\n"]}], "source": ["# Evaluate the best Elastic Net model on test data\n", "# - Use the model with optimal hyperparameters found by grid search\n", "# - Generate predictions on held-out test set for unbiased performance assessment\n", "# Generate predictions using the best Elastic Net model\n", "# - Method: predict() applies optimized L1/L2 regularized coefficients\n", "# - Parameters: X_test_preprocessed provides preprocessed test features\n", "# - Returns: Array of predicted house prices using optimal regularization\n", "# Calculate comprehensive performance metrics\n", "best_elastic_net_model = grid_search.best_estimator_\n", "y_pred_elastic = best_elastic_net_model.predict(X_test_preprocessed)\n", "mse_elastic = mean_squared_error(y_test, y_pred_elastic)\n", "rmse_elastic = root_mean_squared_error(y_test, y_pred_elastic)\n", "r2_elastic = r2_score(y_test, y_pred_elastic)\n", "\n", "\n", "# Analyze feature selection and coefficient behavior\n", "n_selected_elastic = np.sum(best_elastic_net_model.coef_ != 0)\n", "total_features_elastic = len(best_elastic_net_model.coef_)\n", "coef_magnitude_elastic = np.mean(np.abs(best_elastic_net_model.coef_))\n", "\n", "\n", "print(\"Optimized Elastic Net Performance:\")\n", "print(\"=\" * 50)\n", "print(f\"Mean Squared Error: {mse_elastic:,.0f}\")\n", "print(f\"Root Mean Squared Error: ${rmse_elastic:,.0f}\")\n", "print(f\"R² Score: {r2_elastic:.4f} ({r2_elastic*100:.1f}% variance explained)\")\n", "\n", "\n", "print(f\"\\nRegularization Effects:\")\n", "print(f\"Selected features: {n_selected_elastic}/{total_features_elastic} ({n_selected_elastic/total_features_elastic*100:.1f}%)\")\n", "print(f\"Feature elimination: {total_features_elastic - n_selected_elastic} features removed\")\n", "print(f\"Average coefficient magnitude: {coef_magnitude_elastic:.4f}\")\n", "\n", "\n", "# Compare with baseline linear regression\n", "mse_improvement = ((mse_linear - mse_elastic) / mse_linear) * 100\n", "rmse_improvement = ((rmse_linear - rmse_elastic) / rmse_linear) * 100\n", "\n", "\n", "print(f\"\\nImprovement vs. Baseline Linear Regression:\")\n", "print(f\"MSE improvement: {mse_improvement:+.1f}% ({'Better' if mse_improvement > 0 else 'Worse'})\")\n", "print(f\"RMSE improvement: {rmse_improvement:+.1f}% ({'Better' if rmse_improvement > 0 else 'Worse'})\")\n"]}, {"cell_type": "markdown", "id": "bb73c21d", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "model_evaluation", "metadata": {}, "source": ["# <a id='toc9_'></a>[Model Evaluation](#toc0_)\n", "\n", "We provide comprehensive evaluation and comparison of all implemented models to understand the benefits of different regularization approaches."]}, {"cell_type": "markdown", "id": "performance_comparison", "metadata": {}, "source": ["## <a id='toc9_1_'></a>[Performance Comparison](#toc0_)"]}, {"cell_type": "code", "execution_count": 16, "id": "performance_comparison_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Comprehensive Model Performance Comparison:\n", "============================================================\n", "Model                MSE             RMSE            R²        \n", "------------------------------------------------------------\n", "Linear Regression    2,498,842,983,393,033,200,332,701,696 49,988,428,494,933 -325780536041818880.0000\n", "Lasso Regression     797,525,646     28,240          0.8960    \n", "Ridge Regression     890,606,216     29,843          0.8839    \n", "Elastic Net          806,041,688     28,391          0.8949    \n", "\n", "Best Performance by Metric:\n", "Lowest MSE: <PERSON><PERSON> Reg<PERSON> (797,525,646)\n", "Lowest RMSE: Lasso Regression ($28,240)\n", "Highest R²: Lasso Regression (0.8960)\n", "\n", "Improvement vs. Linear Regression Baseline:\n", "Lasso Regression: MSE +100.0%, RMSE +100.0%, R² -100.0%\n", "Ridge Regression: MSE +100.0%, RMSE +100.0%, R² -100.0%\n", "Elastic Net: MSE +100.0%, RMSE +100.0%, R² -100.0%\n"]}], "source": ["# Create comprehensive performance comparison of all models\n", "# - Purpose: Systematic evaluation to identify best regularization approach\n", "# - Metrics: MSE, RMSE, and R² for complete performance assessment\n", "# - Models: Linear, Lasso, Ridge, and optimized Elastic Net\n", "# Calculate R² scores for complete model evaluation\n", "# r2_elastic already calculated above\n", "# Create comprehensive results dictionary\n", "# - Structure: Nested dictionary for organized metric storage\n", "# - Models: All four regression approaches\n", "# - Metrics: MSE (penalty-sensitive), RMSE (interpretable), R² (variance explained)\n", "r2_linear = r2_score(y_test, y_pred_lr)\n", "r2_lasso = r2_score(y_test, y_pred_lasso)\n", "r2_ridge = r2_score(y_test, y_pred_ridge)\n", "r2_elastic = r2_score(y_test, y_pred_elastic)\n", "\n", "model_results = {\n", "    'Linear Regression': {\n", "        'MSE': mse_linear,\n", "        'RMSE': rmse_linear,\n", "        'R²': r2_linear\n", "    },\n", "    'Lasso Regression': {\n", "        'MSE': mse_lasso,\n", "        'RMSE': rmse_lasso,\n", "        'R²': r2_lasso\n", "    },\n", "    'Ridge Regression': {\n", "        'MSE': mse_ridge,\n", "        'RMSE': rmse_ridge,\n", "        'R²': r2_ridge\n", "    },\n", "    'Elastic Net': {\n", "        'MSE': mse_elastic,\n", "        'RMSE': rmse_elastic,\n", "        'R²': r2_elastic\n", "    }\n", "}\n", "\n", "\n", "# Create formatted comparison table\n", "# - DataFrame: Structured presentation for easy comparison\n", "# - Index: Model names for clear identification\n", "# - Columns: Performance metrics for comprehensive assessment\n", "results_df = pd.DataFrame(model_results).T\n", "\n", "\n", "print(\"Comprehensive Model Performance Comparison:\")\n", "print(\"=\" * 60)\n", "print(f\"{'Model':<20} {'MSE':<15} {'RMSE':<15} {'R²':<10}\")\n", "print(\"-\" * 60)\n", "\n", "\n", "for model_name, metrics in model_results.items():\n", "    print(f\"{model_name:<20} {metrics['MSE']:<15,.0f} {metrics['RMSE']:<15,.0f} {metrics['R²']:<10.4f}\")\n", "\n", "\n", "# Identify best performing model for each metric\n", "best_mse = min(model_results.items(), key=lambda x: x[1]['MSE'])\n", "best_rmse = min(model_results.items(), key=lambda x: x[1]['RMSE'])\n", "best_r2 = max(model_results.items(), key=lambda x: x[1]['R²'])\n", "\n", "\n", "print(\"\\nBest Performance by Metric:\")\n", "print(f\"Lowest MSE: {best_mse[0]} ({best_mse[1]['MSE']:,.0f})\")\n", "print(f\"Lowest RMSE: {best_rmse[0]} (${best_rmse[1]['RMSE']:,.0f})\")\n", "print(f\"Highest R²: {best_r2[0]} ({best_r2[1]['R²']:.4f})\")\n", "\n", "\n", "# Calculate percentage improvements relative to baseline\n", "print(f\"\\nImprovement vs. Linear Regression Baseline:\")\n", "for model_name, metrics in model_results.items():\n", "    if model_name != 'Linear Regression':\n", "        mse_imp = ((mse_linear - metrics['MSE']) / mse_linear) * 100\n", "        rmse_imp = ((rmse_linear - metrics['RMSE']) / rmse_linear) * 100\n", "        r2_imp = ((metrics['R²'] - r2_linear) / r2_linear) * 100\n", "        print(f\"{model_name}: MSE {mse_imp:+.1f}%, RMSE {rmse_imp:+.1f}%, R² {r2_imp:+.1f}%\")\n", "\n"]}, {"cell_type": "markdown", "id": "51967070", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "9a607241", "metadata": {}, "source": ["# <a id='toc10_'></a>[Saving and Loading the Model](#toc0_)\n", "\n", "We create a comprehensive model package containing all trained models, preprocessing pipeline, and evaluation results for deployment and future use."]}, {"cell_type": "markdown", "id": "a970cac5", "metadata": {}, "source": ["## <a id='toc10_1_'></a>[Saving the Model Package](#toc0_)"]}, {"cell_type": "code", "execution_count": 17, "id": "save_model_package", "metadata": {}, "outputs": [{"data": {"text/plain": ["['house_price_elastic_net_models.joblib']"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create comprehensive model package for deployment\n", "# - Purpose: Bundle all components needed for production deployment\n", "# - Contents: Trained models, preprocessing pipeline, evaluation metrics, metadata\n", "# - Benefits: Single file contains everything needed for predictions\n", "# Define filename for the model package\n", "# Create comprehensive model package dictionary\n", "    # Trained models for different regularization approaches\n", "    # Preprocessing pipeline fitted on training data\n", "    # Hyperparameter optimization results\n", "    # Comprehensive evaluation metrics\n", "    # Feature information and preprocessing metadata\n", "    # Training metadata for reproducibility\n", "# Save the complete model package to disk\n", "# - Function: joblib.dump() efficiently serializes Python objects\n", "# - Parameters: model_package (object), model_filename (path)\n", "# - Benefits: Optimized for NumPy arrays, handles sklearn objects well\n", "model_filename = 'house_price_elastic_net_models.joblib'\n", "model_package = {\n", "    'models': {\n", "        'linear': lr_model,\n", "        'lasso': laso_model,\n", "        'ridge': ridge_model,\n", "        'elastic_net': best_elastic_net_model\n", "    },\n", "    'preprocessor': final_pipeline,\n", "    'optimization': {\n", "        'param_grid': param_grid,\n", "        'best_params': best_params,\n", "        'best_score': best_score\n", "    },\n", "    'evaluation_metrics': model_results,\n", "    'features': {\n", "        'numerical': numerical_features,\n", "        'categorical': categorical_features\n", "    },\n", "    'training_metadata': {\n", "        'random_state': 42,\n", "        'test_size': 0.2\n", "    }\n", "}\n", "\n", "joblib.dump(model_package, model_filename)"]}, {"cell_type": "markdown", "id": "loading_using_model", "metadata": {}, "source": ["## <a id='toc10_2_'></a>[Loading a the Model](#toc0_)\n", "\n", "We demonstrate how to load the saved model package and use it for making predictions on new data."]}, {"cell_type": "code", "execution_count": 18, "id": "load_model_demo", "metadata": {}, "outputs": [], "source": ["\n", "# Demonstrate loading and using the saved model package\n", "# - Purpose: Show how to deploy models in production or new analysis\n", "# - Process: Load package, extract components, make predictions\n", "\n", "\n", "# Load the saved model package from disk\n", "# - Function: joblib.load() deserializes the saved model package\n", "# - Parameters: model_filename specifies the path to the saved file\n", "# - Returns: Dictionary containing all saved components\n", "loaded_package = joblib.load(model_filename)\n", "\n", "\n", "# Extract key components from the loaded package\n", "loaded_models = loaded_package['models']\n", "loaded_preprocessor = loaded_package['preprocessor']\n", "loaded_metrics = loaded_package['evaluation_metrics']\n", "loaded_best_params = loaded_package['optimization']['best_params']\n", "\n", "\n", "# Extract the best performing model for demonstration\n", "best_loaded_model = loaded_models['elastic_net']"]}, {"cell_type": "code", "execution_count": 31, "id": "642fb1c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["28390.87332876874"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["loaded_package['evaluation_metrics']['Elastic Net']['RMSE']"]}, {"cell_type": "markdown", "id": "58415604", "metadata": {}, "source": ["## <a id='toc10_3_'></a>[Using the Model](#toc0_)\n", "\n", "You get need house information for the prediction."]}, {"cell_type": "code", "execution_count": 19, "id": "88037428", "metadata": {}, "outputs": [], "source": ["# Use the first test sample to dictionary for demonstration\n", "sample_house = X_test.iloc[0].to_dict()"]}, {"cell_type": "markdown", "id": "d0592a5a", "metadata": {}, "source": [" TASK: Code a function to create predictions for one new house (sample_house)."]}, {"cell_type": "code", "execution_count": 38, "id": "production_function", "metadata": {}, "outputs": [], "source": ["def predict_house_price(house_features_dict, model_package_path='house_price_elastic_net_models.joblib'):\n", "    \"\"\"\n", "    Predict house price using the trained Elastic Net model.\n", "\n", "\n", "    Parameters:\n", "    -----------\n", "    house_features_dict : dict\n", "        Dictionary containing house features (must match training feature names)\n", "    model_package_path : str, optional\n", "        Path to saved model package (if None, uses loaded package)\n", "\n", "\n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary containing the following:\n", "              return {\n", "                    'predicted_price': float(prediction),\n", "                    'model_params': best_params,\n", "                    'model_rmse': float(model_performance['rmse']),\n", "                    'model_r2': float(model_performance['r2']),\n", "        }\n", "    \"\"\"\n", "        # Load model package if path provided\n", "        # Extract components\n", "        # Convert input to DataFrame with correct structure\n", "        # Validate features\n", "        # Preprocess the input\n", "        # Make prediction\n", "        # Get model metadata\n", "        \n", "    model_package = joblib.load(model_package_path)\n", "    preprocessor = model_package['preprocessor']\n", "    model = model_package['models']['elastic_net']\n", "    house_df = pd.DataFrame([house_features_dict])\n", "    house_df = preprocessor.transform(house_df)\n", "    prediction = model.predict(house_df)\n", "    rmse = model_package['evaluation_metrics']['Elastic Net']['RMSE']\n", "    r2 = model_package['evaluation_metrics']['Elastic Net']['R²']\n", "    \n", "    \n", "    return {\n", "        'predicted_price': prediction[0],\n", "        'model_params': model_package['optimization']['best_params'],\n", "        'model_rmse': rmse,\n", "        'model_r2': r2\n", "    }\n", "    \n"]}, {"cell_type": "markdown", "id": "ccb7993c", "metadata": {}, "source": ["Use following code to print the result information."]}, {"cell_type": "code", "execution_count": 39, "id": "4d30277c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Production Function Demonstration:\n", "========================================\n", "Predicted Price: $152,168\n", "Model Parameters: α=100.0, l1_ratio=1.0\n", "Model RMSE: $28,391\n", "Model R²: 0.8949\n", "\n", "Validation:\n", "Actual Price: $154,500\n", "Prediction Error: $2,332 (1.5%)\n"]}], "source": ["\n", "# Make prediction using the production function\n", "result = predict_house_price(sample_house)\n", "print(\"Production Function Demonstration:\")\n", "print(\"=\" * 40)\n", "if 'error' in result:\n", "        print(f\"Error: {result['error']}\")\n", "else:\n", "        print(f\"Predicted Price: ${result['predicted_price']:,.0f}\")\n", "        print(f\"Model Parameters: α={result['model_params']['alpha']}, l1_ratio={result['model_params']['l1_ratio']}\")\n", "        print(f\"Model RMSE: ${result['model_rmse']:,.0f}\")\n", "        print(f\"Model R²: {result['model_r2']:.4f}\")\n", "\n", "\n", "        # Compare with actual value\n", "        actual_price = y_test.iloc[0]\n", "        error = abs(actual_price - result['predicted_price'])\n", "        error_pct = (error / actual_price) * 100\n", "\n", "\n", "        print(f\"\\nValidation:\")\n", "        print(f\"Actual Price: ${actual_price:,.0f}\")\n", "        print(f\"Prediction Error: ${error:,.0f} ({error_pct:.1f}%)\")\n", "\n"]}, {"cell_type": "markdown", "id": "42f27ea5", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}