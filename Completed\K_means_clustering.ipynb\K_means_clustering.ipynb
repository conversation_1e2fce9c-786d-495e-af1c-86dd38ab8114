{"cells": [{"cell_type": "markdown", "id": "clustering-title", "metadata": {}, "source": ["# <a id='toc1_'></a>[K-Means Clustering - Unsupervised Learning](#toc0_)"]}, {"cell_type": "markdown", "id": "banner-1", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "1f737a20", "metadata": {}, "source": ["**Table of contents**<a id='toc0_'></a>    \n", "- [K-Means Clustering - Unsupervised Learning](#toc1_)    \n", "- [Theory](#toc2_)    \n", "- [Problem Statement](#toc3_)    \n", "- [Introduction](#toc4_)    \n", "  - [Mathematical Foundations](#toc4_1_)    \n", "    - [Objective Function](#toc4_1_1_)    \n", "    - [Distance Metric](#toc4_1_2_)    \n", "    - [Convergence Criteria](#toc4_1_3_)    \n", "- [Importing Libraries](#toc5_)    \n", "- [Loading and Exploring the Iris Dataset](#toc6_)    \n", "  - [Data Exploration and Cleaning](#toc6_1_)    \n", "  - [Exploratory Visualization](#toc6_2_)    \n", "  - [Data Preprocessing](#toc6_3_)    \n", "- [Determining Optimal K](#toc7_)    \n", "  - [Elbow Method](#toc7_1_)    \n", "  - [Visualization of K Selection Methods](#toc7_2_)    \n", "- [K-Means Implementation](#toc8_)    \n", "  - [Model Training](#toc8_1_)    \n", "  - [Model Evaluation](#toc8_2_)    \n", "  - [ Silhouette Score](#toc8_3_)    \n", "    - [Interpretation Scale](#toc8_3_1_)    \n", "  - [ Davies-Bouldin Index](#toc8_4_)    \n", "    - [Interpretation Scale](#toc8_4_1_)    \n", "  - [ Calinski-Harabasz Index](#toc8_5_)    \n", "    - [Interpretation Guidelines](#toc8_5_1_)    \n", "  - [Cluster Characteristics](#toc8_6_)    \n", "  - [Visualization](#toc8_7_)    \n", "- [Model Assumptions, Limitations, and Best Use Cases](#toc9_)    \n", "  - [Assumptions of K-Means](#toc9_1_)    \n", "  - [Limitations](#toc9_2_)    \n", "  - [Best Use Cases](#toc9_3_)    \n", "  - [Performance Optimization Tips](#toc9_4_)    \n", "\n", "<!-- vscode-jupyter-toc-config\n", "\tnumbering=false\n", "\tanchor=true\n", "\tflat=false\n", "\tminLevel=1\n", "\tmaxLevel=6\n", "\t/vscode-jupyter-toc-config -->\n", "<!-- THIS CELL WILL BE REPLACED ON TOC UPDATE. DO NOT WRITE YOUR TEXT IN THIS CELL -->"]}, {"cell_type": "markdown", "id": "367a6935", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "theory-section", "metadata": {}, "source": ["# <a id='toc2_'></a>[Theory](#toc0_)"]}, {"cell_type": "markdown", "id": "theory-links", "metadata": {}, "source": ["[Theory video - K-Means Clustering](https://www.youtube.com/watch?v=4b5d3muPQmA)"]}, {"cell_type": "markdown", "id": "banner-2", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "problem-statement", "metadata": {}, "source": ["# <a id='toc3_'></a>[Problem Statement](#toc0_)\n", "\n", "In many real-world scenarios, we have unlabeled data and need to discover hidden patterns or natural groupings. K-Means clustering is one of the most popular unsupervised learning algorithms that partitions data into K distinct clusters based on similarity.\n", "\n", "In this tutorial, we'll explore clustering using the famous Iris dataset. While we have the true species labels available for validation, we'll approach this as an unsupervised problem to demonstrate how clustering can discover natural groupings in flower measurements without prior knowledge of the species.\n", "\n", "Common applications of K-Means include:\n", "- **Customer Segmentation**: Grouping customers based on purchasing behavior\n", "- **Image Compression**: Reducing colors in images by clustering similar pixels\n", "- **Anomaly Detection**: Identifying outliers that don't fit well into any cluster\n", "- **Biological Classification**: Discovering natural groupings in biological data\n", "- **Document Clustering**: Organizing documents by topic similarity"]}, {"cell_type": "markdown", "id": "banner-3", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "introduction", "metadata": {}, "source": ["# <a id='toc4_'></a>[Introduction](#toc0_)\n", "\n", "<img src=\"https://raw.githubusercontent.com/henrylahteenmaki//Machine_Learning_Methods/refs/heads/main/figs/K_means_fig1.png\" width=\"800\">\n", "\n", "<img src=\"https://raw.githubusercontent.com/henrylahteenmaki//Machine_Learning_Methods/refs/heads/main/figs/DBSCAN_vs_k_means.png\" width=\"800\">\n", "\n", "\n", "K-Means clustering is an iterative algorithm that partitions n observations into k clusters where each observation belongs to the cluster with the nearest mean (cluster centroid). The algorithm:\n", "\n", "1. **Initializes** k cluster centroids randomly\n", "2. **Assigns** each data point to the nearest centroid\n", "3. **Updates** centroids as the mean of assigned points\n", "4. **Repeats** steps 2-3 until convergence\n", "\n", "The algorithm minimizes within-cluster sum of squares (WCSS):\n", "\n", "$$ J = \\sum_{i=1}^{K} \\sum_{x \\in C_i} ||x - \\mu_i||^2 $$\n", "\n", "Where:\n", "- $K$ is the number of clusters\n", "- $C_i$ is the set of points in cluster $i$\n", "- $\\mu_i$ is the centroid of cluster $i$\n", "- $||x - \\mu_i||^2$ is the squared Euclidean distance"]}, {"cell_type": "markdown", "id": "mathematical-foundations", "metadata": {}, "source": ["## <a id='toc4_1_'></a>[Mathematical Foundations](#toc0_)\n", "\n", "### <a id='toc4_1_1_'></a>[Objective Function](#toc0_)\n", "\n", "K-Means seeks to minimize the within-cluster sum of squares (inertia):\n", "\n", "$$ \\text{argmin}_S \\sum_{i=1}^{k} \\sum_{x \\in S_i} ||x - \\mu_i||^2 $$\n", "\n", "### <a id='toc4_1_2_'></a>[Distance Metric](#toc0_)\n", "\n", "Typically uses Euclidean distance:\n", "\n", "$$ d(x, y) = \\sqrt{\\sum_{i=1}^{n} (x_i - y_i)^2} $$\n", "\n", "### <a id='toc4_1_3_'></a>[Convergence Criteria](#toc0_)\n", "\n", "- No change in cluster assignments\n", "- Centroids move less than threshold\n", "- Maximum iterations reached"]}, {"cell_type": "markdown", "id": "banner-5", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "imports-title", "metadata": {}, "source": ["# <a id='toc5_'></a>[Importing Libraries](#toc0_)"]}, {"cell_type": "code", "execution_count": 29, "id": "imports", "metadata": {}, "outputs": [], "source": ["\n", "#  Import core data analysis and visualization libraries\n", "# pandas: For DataFrame operations and data manipulation\n", "# numpy: For numerical operations and array handling\n", "# matplotlib.pyplot: For creating static plots and visualizations\n", "# seaborn: For enhanced statistical visualizations with aesthetic appeal\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "\n", "\n", "# Import scikit-learn clustering algorithms\n", "# KMeans: Centroid-based clustering algorithm that partitions data into K clusters\n", "# DBSCAN: Density-based clustering for arbitrary shaped clusters\n", "# AgglomerativeClustering: Hierarchical clustering using bottom-up approach\n", "\n", "\n", "\n", "\n", "# Import preprocessing utilities\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "\n", "\n", "\n", "# Import evaluation metrics\n", "# silhouette_score: Measures how similar objects are to their own cluster vs other clusters\n", "# davies_bouldin_score: Ratio of within-cluster to between-cluster distances\n", "# calinski_harabasz_score: Ratio of between-cluster to within-cluster dispersion\n", "\n", "\n", "# Import datasets\n", "# load_iris: Famous flower dataset with 150 samples and 3 species\n", "# make_blobs: Generate synthetic isotropic Gaussian blobs for testing\n", "from sklearn.datasets import load_iris, make_blobs\n", "\n", "# Configure display settings for better visualization\n", "plt.rcParams['figure.figsize'] = (12, 8)  # Set default figure size\n", "sns.set_style('whitegrid')  # Set seaborn style for cleaner plots\n", "pd.set_option('display.max_columns', None)  # Show all columns in DataFrame display\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n"]}, {"cell_type": "markdown", "id": "banner-6", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "data-loading-title", "metadata": {}, "source": ["# <a id='toc6_'></a>[Loading and Exploring the Iris Dataset](#toc0_)"]}, {"cell_type": "code", "execution_count": 30, "id": "load-iris-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading Iris dataset...\n", "Data loaded successfully!\n", "  Shape: (150, 4)\n", "  Features: ['sepal length (cm)', 'sepal width (cm)', 'petal length (cm)', 'petal width (cm)']\n", "  Species: ['setosa' 'versicolor' 'virginica']\n", "\n", "Dataset Overview:\n", "   sepal length (cm)  sepal width (cm)  petal length (cm)  petal width (cm)  \\\n", "0                5.1               3.5                1.4               0.2   \n", "1                4.9               3.0                1.4               0.2   \n", "2                4.7               3.2                1.3               0.2   \n", "3                4.6               3.1                1.5               0.2   \n", "4                5.0               3.6                1.4               0.2   \n", "\n", "   species species_name  \n", "0        0       setosa  \n", "1        0       setosa  \n", "2        0       setosa  \n", "3        0       setosa  \n", "4        0       setosa  \n", "\n", "Statistical Summary:\n", "       sepal length (cm)  sepal width (cm)  petal length (cm)  \\\n", "count         150.000000        150.000000         150.000000   \n", "mean            5.843333          3.057333           3.758000   \n", "std             0.828066          0.435866           1.765298   \n", "min             4.300000          2.000000           1.000000   \n", "25%             5.100000          2.800000           1.600000   \n", "50%             5.800000          3.000000           4.350000   \n", "75%             6.400000          3.300000           5.100000   \n", "max             7.900000          4.400000           6.900000   \n", "\n", "       petal width (cm)     species  \n", "count        150.000000  150.000000  \n", "mean           1.199333    1.000000  \n", "std            0.762238    0.819232  \n", "min            0.100000    0.000000  \n", "25%            0.300000    0.000000  \n", "50%            1.300000    1.000000  \n", "75%            1.800000    2.000000  \n", "max            2.500000    2.000000  \n"]}], "source": ["\n", "# Load the Iris dataset from scikit-learn\n", "# The Iris dataset is a classic dataset in machine learning containing measurements\n", "# of 150 iris flowers from 3 different species (50 samples each)\n", "# Features: sepal length, sepal width, petal length, petal width (all in cm)\n", "# Target: species (setosa, versicolor, virginica)\n", "print(\"Loading Iris dataset...\")\n", "\n", "iris = load_iris()\n", "X = iris.data  # Feature matrix (we'll ignore labels for clustering)\n", "y_true = iris.target  # True labels (only for evaluation, not for clustering)\n", "feature_names = iris.feature_names\n", "target_names = iris.target_names\n", "\n", "# Create DataFrame for easier analysis and visualization\n", "df = pd.DataFrame(X, columns=feature_names)\n", "df['species'] = y_true\n", "df['species_name'] = df['species'].map({i: name for i, name in enumerate(target_names)})\n", "\n", "print(\"Data loaded successfully!\")\n", "print(f\"  Shape: {X.shape}\")\n", "print(f\"  Features: {feature_names}\")\n", "print(f\"  Species: {target_names}\")\n", "print(f\"\\nDataset Overview:\")\n", "print(df.head())\n", "print(f\"\\nStatistical Summary:\")\n", "print(df.describe())\n", "\n"]}, {"cell_type": "code", "execution_count": 31, "id": "ebf6a81b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['sepal_length_(cm)', 'sepal_width_(cm)', 'petal_length_(cm)',\n", "       'petal_width_(cm)', 'species', 'species_name'],\n", "      dtype='object')"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Replace spaces in column names with underscores\n", "for col in df.columns:\n", "    if ' ' in col:\n", "        df.rename(columns={col: col.replace(' ', '_')}, inplace=True)\n", "        \n", "df.columns"]}, {"cell_type": "code", "execution_count": 52, "id": "7b1f1907", "metadata": {}, "outputs": [{"data": {"text/plain": ["['sepal_length_(cm)',\n", " 'sepal_width_(cm)',\n", " 'petal_length_(cm)',\n", " 'petal_width_(cm)']"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["# Extract feature names and assign them to 'feature_names'\n", "feature_names = df.drop(['species', 'species_name'], axis=1).columns.tolist()\n", "\n", "feature_names"]}, {"cell_type": "markdown", "id": "9d1217bc", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "09e53030", "metadata": {}, "source": ["## <a id='toc6_1_'></a>[Data Exploration and Cleaning](#toc0_)\n", "\n", "We explore the dataset structure, check for missing values, and understand the target variable distribution."]}, {"cell_type": "code", "execution_count": 53, "id": "cb466d33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Information:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 150 entries, 0 to 149\n", "Data columns (total 6 columns):\n", " #   Column             Non-Null Count  Dtype  \n", "---  ------             --------------  -----  \n", " 0   sepal_length_(cm)  150 non-null    float64\n", " 1   sepal_width_(cm)   150 non-null    float64\n", " 2   petal_length_(cm)  150 non-null    float64\n", " 3   petal_width_(cm)   150 non-null    float64\n", " 4   species            150 non-null    int32  \n", " 5   species_name       150 non-null    object \n", "dtypes: float64(4), int32(1), object(1)\n", "memory usage: 6.6+ KB\n"]}], "source": ["\n", "# Display comprehensive information about the dataset structure\n", "print(\"Dataset Information:\")\n", "df.info()\n", "\n"]}, {"cell_type": "code", "execution_count": 54, "id": "94bf110a", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "273faa6b-49bb-4b02-951e-7e05dd1b59f0", "rows": [["sepal_length_(cm)", "0"], ["sepal_width_(cm)", "0"], ["petal_length_(cm)", "0"], ["petal_width_(cm)", "0"], ["species", "0"], ["species_name", "0"]], "shape": {"columns": 1, "rows": 6}}, "text/plain": ["sepal_length_(cm)    0\n", "sepal_width_(cm)     0\n", "petal_length_(cm)    0\n", "petal_width_(cm)     0\n", "species              0\n", "species_name         0\n", "dtype: int64"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Check for missing values in each column\n", "missing_values = df.isnull().sum()\n", "missing_values\n", "\n"]}, {"cell_type": "markdown", "id": "exploratory-viz-title", "metadata": {}, "source": ["## <a id='toc6_2_'></a>[Exploratory Visualization](#toc0_)"]}, {"cell_type": "code", "execution_count": 55, "id": "exploratory-viz", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Plot 1: Sepal dimensions\n", "for i, species in enumerate(target_names):\n", "    mask = df['species_name'] == species\n", "    axes[0].scatter(df[mask]['sepal_length_(cm)'], \n", "                    df[mask]['sepal_width_(cm)'],\n", "                    label=species, s=50, alpha=0.7)\n", "axes[0].set_xlabel('Sepal Length (cm)')\n", "axes[0].set_ylabel('Sepal Width (cm)')\n", "axes[0].set_title('Iris Dataset - Sepal Dimensions (True Labels)')\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Petal dimensions\n", "for i, species in enumerate(target_names):\n", "    mask = df['species_name'] == species\n", "    axes[1].scatter(df[mask]['petal_length_(cm)'], \n", "                    df[mask]['petal_width_(cm)'],\n", "                    label=species, s=50, alpha=0.7)\n", "axes[1].set_xlabel('Petal Length (cm)')\n", "axes[1].set_ylabel('<PERSON><PERSON> Width (cm)')\n", "axes[1].set_title('Iris Dataset - Petal Dimensions (True Labels)')\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n"]}, {"cell_type": "markdown", "id": "b8fe997e", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "data-preprocessing-title", "metadata": {}, "source": ["## <a id='toc6_3_'></a>[Data Preprocessing](#toc0_)"]}, {"cell_type": "code", "execution_count": 56, "id": "c331042e", "metadata": {}, "outputs": [], "source": ["# Drop 'species' and 'species_name' columns from the DataFrame and store the remaining features in 'X'\n", "X = df.drop(['species', 'species_name'], axis=1)"]}, {"cell_type": "code", "execution_count": 57, "id": "59d04af0", "metadata": {}, "outputs": [], "source": ["\n", "# Identify numerical features and assign them to 'numerical_features'\n", "numerical_features = X.select_dtypes(include=['number']).columns.tolist()\n", "\n", "\n", "# Create a preprocessing pipeline for numerical features\n", "numerical_pipeline = Pipeline(steps=[\n", "    ('scaler', StandardScaler()),\n", "])\n", "\n", "# Create a comprehensive preprocessor using ColumnTransformer\n", "preprocessor = ColumnTransformer(\n", "    transformers=[\n", "        ('num', numerical_pipeline, numerical_features),\n", "    ],\n", "    remainder='drop'\n", ")\n", "\n", "# Create the complete preprocessing pipeline\n", "preprocessing_pipeline = Pipeline(steps=[\n", "    ('preprocessor', preprocessor)\n", "])\n", "\n"]}, {"cell_type": "code", "execution_count": 58, "id": "data-preprocessing", "metadata": {}, "outputs": [], "source": ["# Scale features for K-Means clustering\n", "# K-Means uses distance-based calculations, so features with larger scales\n", "# can dominate the distance calculations. StandardScaler normalizes features\n", "# to have zero mean and unit variance, ensuring equal contribution.\n", "X_scaled = preprocessing_pipeline.fit_transform(X)"]}, {"cell_type": "markdown", "id": "banner-7", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "determine-k-title", "metadata": {}, "source": ["# <a id='toc7_'></a>[Determining Optimal K](#toc0_)\n", "\n", "While statistical methods like the Elbow Method and Silhouette Analysis are valuable tools, **domain knowledge should always be considered**. In the case of the Iris dataset, even if silhouette analysis suggests K=2 as optimal, we know from botanical knowledge that there are **3 distinct species** of iris flowers. This is a perfect example of why understanding your data context is crucial in machine learning."]}, {"cell_type": "markdown", "id": "elbow-method-title", "metadata": {}, "source": ["## <a id='toc7_1_'></a>[Elbow Method](#toc0_)"]}, {"cell_type": "code", "execution_count": 59, "id": "elbow-method", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finding optimal K using Elbow Method and Silhouette Analysis...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n"]}], "source": ["from sklearn.cluster import KMeans\n", "from sklearn.metrics import silhouette_score\n", "\n", "# Elbow Method to find optimal number of clusters\n", "# The elbow point indicates where adding more clusters provides diminishing returns\n", "# WCSS (Within-Cluster Sum of Squares) measures cluster compactness\n", "# Lower WCSS means tighter clusters, but too many clusters leads to overfitting\n", "\n", "print(\"Finding optimal K using Elbow Method and Silhouette Analysis...\")\n", "\n", "wcss = []  # Within-cluster sum of squares\n", "silhouette_scores = []  # Silhouette coefficients\n", "K_range = range(2, 11)  # Test K from 2 to 10\n", "\n", "for k in K_range:\n", "    # Create K-Means model with current K\n", "    # init='k-means++': Smart initialization for better convergence\n", "    # n_init: Number of times algorithm runs with different seeds\n", "    # max_iter: Maximum iterations for convergence\n", "    # random_state: Ensures reproducibility\n", "    kmeans = KMeans(\n", "        n_clusters=k,\n", "        init='k-means++',\n", "        n_init=10,\n", "        max_iter=300,\n", "        random_state=42\n", "    )\n", "    \n", "    # Fit the model and get cluster labels\n", "    kmeans.fit(X_scaled)\n", "    \n", "    # Store WCSS (inertia)\n", "    wcss.append(kmeans.inertia_)\n", "    \n", "    # Calculate silhouette score\n", "    # Range: [-1, 1], where:\n", "    #   1: Perfectly separated clusters\n", "    #   0: Overlapping clusters\n", "    #  -1: Misclassified samples\n", "    silhouette_scores.append(silhouette_score(X_scaled, kmeans.labels_))\n"]}, {"cell_type": "markdown", "id": "elbow-viz-title", "metadata": {}, "source": ["## <a id='toc7_2_'></a>[Visualization of K Selection Methods](#toc0_)"]}, {"cell_type": "code", "execution_count": 60, "id": "elbow-visualization", "metadata": {}, "outputs": [{"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1600x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Analysis Results:\n", "  Silhouette Analysis suggests K=2 (score: 0.582)\n", "  Domain knowledge suggests K=3 (3 iris species)\n", "\n", "  Decision: Using K=3 based on domain knowledge\n", "  <PERSON><PERSON><PERSON><PERSON> score for K=3: 0.460\n"]}], "source": ["\n", "# Visualization of elbow curve and silhouette scores\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# Elbow plot\n", "ax1.plot(K_range, wcss, 'bo-', linewidth=2, markersize=8)\n", "ax1.set_xlabel('Number of Clusters (K)', fontsize=12)\n", "ax1.set_ylabel('Within-Cluster Sum of Squares', fontsize=12)\n", "ax1.set_title('Elbow Method for Optimal K', fontsize=14, fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "# <PERSON>=3 as the elbow point based on domain knowledge\n", "ax1.axvline(x=3, color='r', linestyle='--', alpha=0.5, label='K=3 (Domain Knowledge)')\n", "ax1.legend()\n", "\n", "# Silhouette score plot\n", "ax2.plot(K_range, silhouette_scores, 'go-', linewidth=2, markersize=8)\n", "ax2.set_xlabel('Number of Clusters (K)', fontsize=12)\n", "ax2.set_ylabel('Silhouette Score', fontsize=12)\n", "ax2.set_title('Silhouette Analysis', fontsize=14, fontweight='bold')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Find <PERSON> with maximum silhouette score\n", "max_silhouette_k = K_range[np.argmax(silhouette_scores)]\n", "ax2.axvline(x=max_silhouette_k, color='b', linestyle='--', alpha=0.5, \n", "            label=f'<PERSON> (K={max_silhouette_k})')\n", "# Also mark K=3 based on domain knowledge\n", "ax2.axvline(x=3, color='r', linestyle='--', alpha=0.5, label='K=3 (Domain Knowledge)')\n", "ax2.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print analysis results\n", "print(f\"\\nAnalysis Results:\")\n", "print(f\"  Silhouette Analysis suggests K={max_silhouette_k} (score: {max(silhouette_scores):.3f})\")\n", "print(f\"  Domain knowledge suggests K=3 (3 iris species)\")\n", "print(f\"\\n  Decision: Using K=3 based on domain knowledge\")\n", "print(f\"  Silhouette score for K=3: {silhouette_scores[1]:.3f}\")\n", "\n", "# Set optimal K based on domain knowledge\n", "optimal_k = 3\n", "\n"]}, {"cell_type": "markdown", "id": "banner-8", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "kmeans-training-title", "metadata": {}, "source": ["# <a id='toc8_'></a>[K-Means Implementation](#toc0_)\n", "\n", "## <a id='toc8_1_'></a>[Model Training](#toc0_)"]}, {"cell_type": "code", "execution_count": 69, "id": "kmeans-training", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K-Means Training Completed!\n", "   Number of iterations: 4\n", "   Final inertia (WCSS): 139.82\n", "\n", "Cluster Distribution:\n", "   Cluster 0: 53 samples (35.3%)\n", "   Cluster 1: 50 samples (33.3%)\n", "   Cluster 2: 47 samples (31.3%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1429: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n"]}], "source": ["# Initialize K-Means model with optimal parameters\n", "# <PERSON>'s algorithm iteratively assigns points to nearest centroid\n", "# and updates centroids as the mean of assigned points\n", "kmeans_optimal = KMeans(\n", "    n_clusters=optimal_k,\n", "    init='k-means++',\n", "    n_init=10,\n", "    max_iter=300,\n", "    random_state=42\n", ")\n", "\n", "# Fit the model to scaled data\n", "# The algorithm iteratively minimizes within-cluster sum of squares\n", "kmeans_optimal.fit(X_scaled)\n", "\n", "# Get cluster assignments and centroids\n", "cluster_labels = kmeans_optimal.labels_\n", "cluster_centers = kmeans_optimal.cluster_centers_\n", "\n", "# Get the scaler from the pipeline\n", "scaler = preprocessing_pipeline.named_steps['preprocessor'].named_transformers_['num'].named_steps['scaler']\n", "\n", "# Inverse transform the cluster centers\n", "cluster_centers_original = scaler.inverse_transform(kmeans_optimal.cluster_centers_)  \n", "\n", "\n", "# Display training results\n", "print(\"K-Means Training Completed!\")\n", "print(f\"   Number of iterations: {kmeans_optimal.n_iter_}\")\n", "print(f\"   Final inertia (WCSS): {kmeans_optimal.inertia_:.2f}\")\n", "print(f\"\\nCluster Distribution:\")\n", "unique, counts = np.unique(cluster_labels, return_counts=True)\n", "for cluster, count in zip(unique, counts):\n", "    print(f\"   Cluster {cluster}: {count} samples ({count/len(X)*100:.1f}%)\")\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "fb20dda3", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "evaluation-title", "metadata": {}, "source": ["## <a id='toc8_2_'></a>[Model Evaluation](#toc0_)"]}, {"cell_type": "markdown", "id": "806fb57c", "metadata": {}, "source": ["\n", "\n", "## <a id='toc8_3_'></a>[ Si<PERSON><PERSON><PERSON> Score](#toc0_)\n", "\n", "**Range: [-1, 1]**\n", "**Direction: Higher is Better** \n", "\n", "The Silhouette Score measures how similar an object is to its own cluster (cohesion) compared to other clusters (separation). It combines both cluster tightness and separation between clusters.\n", "\n", "### <a id='toc8_3_1_'></a>[Interpretation Scale](#toc0_)\n", "\n", "| Score Range | Interpretation | Cluster Quality | Description |\n", "|-------------|----------------|-----------------|-------------|\n", "| **0.71 - 1.00** | Strong structure | Excellent | Clusters are highly dense and well-separated |\n", "| **0.51 - 0.70** | Reasonable structure | Good | Clusters are fairly separated with moderate overlap |\n", "| **0.26 - 0.50** | Weak structure | Fair | Clusters exist but boundaries are fuzzy |\n", "| **0.00 - 0.25** | No meaningful structure | Poor | Points are between clusters |\n", "| **< 0.00** | Wrong clustering | Failed | Many points likely assigned to wrong clusters |\n", "\n"]}, {"cell_type": "markdown", "id": "7f1ed5e7", "metadata": {}, "source": ["## <a id='toc8_4_'></a>[ Davies-<PERSON> Index](#toc0_)\n", "\n", "**Range: [0, ∞)**\n", "**Direction: Lower is Better**\n", "\n", "The Davies-Bouldin Index evaluates clustering quality by measuring the average similarity ratio between each cluster and its most similar cluster. It considers both within-cluster scatter and between-cluster separation.\n", "\n", "### <a id='toc8_4_1_'></a>[Interpretation Scale](#toc0_)\n", "\n", "| Score Range | Interpretation | Cluster Quality | Description |\n", "|-------------|----------------|-----------------|-------------|\n", "| **0.00 - 0.50** | Excellent clustering | Excellent | Clusters are compact and well-separated |\n", "| **0.50 - 1.00** | Good clustering | Good | Decent separation with reasonable compactness |\n", "| **1.00 - 1.50** | Fair clustering | Fair | Some cluster overlap or loose structure |\n", "| **> 1.50** | Poor clustering | Poor | Significant overlap or very dispersed clusters |\n", "\n"]}, {"cell_type": "markdown", "id": "4f126b41", "metadata": {}, "source": ["\n", "## <a id='toc8_5_'></a>[ Calinski-Harabasz Index](#toc0_)\n", "\n", "**Range: [0, ∞)**\n", "**Direction: Higher is Better** \n", "\n", "The Calinski-Harabasz Index (Variance Ratio Criterion) measures the ratio of the sum of between-cluster dispersion to within-cluster dispersion. Higher scores indicate better-defined clusters.\n", "\n", "### <a id='toc8_5_1_'></a>[Interpretation Guidelines](#toc0_)\n", "\n", "| Characteristic | Interpretation | Notes |\n", "|----------------|----------------|-------|\n", "| **Higher values** | Better defined, more separated clusters | Dense, well-separated groups |\n", "| **Lower values** | Clusters are dispersed and overlapping | Poor cluster definition |\n", "| **Comparison use** | Best for comparing different K values | Relative metric |\n", "| **No absolute threshold** | Values are dataset-dependent | Context-specific |\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 62, "id": "evaluation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Clustering Metrics:\n", "==================================================\n", "Silhouette Score:        0.460\n", "  Interpretation: Fair\n", "\n", "Davies-Bouldin Index:    0.834\n", "  Interpretation: Good\n", "\n", "Calinski-Harabasz Index: 241.9\n", "  Interpretation: Higher is better (no absolute threshold)\n", "==================================================\n", "\n", "Comparison with True Labels (for validation):\n", "   Adjusted Rand Index: 0.620 (1.0 = perfect match)\n", "   Normalized Mutual Information: 0.659 (1.0 = perfect match)\n"]}], "source": ["from sklearn.metrics import silhouette_score, davies_bouldin_score, calinski_harabasz_score\n", "\n", "# Silhouette Score: Measures cluster cohesion and separation\n", "# Range: [-1, 1], higher is better\n", "# Considers both intra-cluster and inter-cluster distances\n", "silhouette_avg = silhouette_score(X_scaled, cluster_labels)\n", "\n", "# Davies-Bouldin Index: Average similarity between clusters\n", "# Lower values indicate better clustering\n", "# Measures ratio of within-cluster to between-cluster distances\n", "davies_bouldin = davies_bouldin_score(X_scaled, cluster_labels)\n", "\n", "# Calinski-Harabasz Index: Ratio of between-cluster to within-cluster variance\n", "# Higher values indicate better defined clusters\n", "calinski_harabasz = calinski_harabasz_score(X_scaled, cluster_labels)\n", "\n", "\n", "\n", "# Display evaluation metrics\n", "print(\"Clustering Metrics:\")\n", "print(\"=\" * 50)\n", "print(f\"Silhouette Score:        {silhouette_avg:.3f}\")\n", "print(f\"  Interpretation: {'Excellent' if silhouette_avg > 0.7 else 'Good' if silhouette_avg > 0.5 else 'Fair' if silhouette_avg > 0.25 else 'Poor'}\")\n", "print(f\"\\nDavies-Bouldin Index:    {davies_bouldin:.3f}\")\n", "print(f\"  Interpretation: {'Excellent' if davies_bouldin < 0.5 else 'Good' if davies_bouldin < 1 else 'Fair' if davies_bouldin < 1.5 else 'Poor'}\")\n", "print(f\"\\nCalinski-Harabasz Index: {calinski_harabasz:.1f}\")\n", "print(f\"  Interpretation: Higher is better (no absolute threshold)\")\n", "print(\"=\" * 50)\n", "\n", "# Compare with true labels (for educational purposes only)\n", "# In real clustering, we wouldn't have true labels\n", "from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score\n", "ari = adjusted_rand_score(y_true, cluster_labels)\n", "nmi = normalized_mutual_info_score(y_true, cluster_labels)\n", "print(f\"\\nComparison with True Labels (for validation):\")\n", "print(f\"   Adjusted Rand Index: {ari:.3f} (1.0 = perfect match)\")\n", "print(f\"   Normalized Mutual Information: {nmi:.3f} (1.0 = perfect match)\")\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "45b93a49", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "cluster-characteristics-title", "metadata": {}, "source": ["## <a id='toc8_6_'></a>[Cluster Characteristics](#toc0_)"]}, {"cell_type": "code", "execution_count": 63, "id": "cluster-characteristics", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cluster Characteristics (Original Scale):\n", "\n", "Cluster Centers:\n", "           sepal_length_(cm)  sepal_width_(cm)  petal_length_(cm)  \\\n", "Cluster 0               5.80              2.67               4.37   \n", "Cluster 1               5.01              3.43               1.46   \n", "Cluster 2               6.78              3.10               5.51   \n", "\n", "           petal_width_(cm)  \n", "Cluster 0              1.41  \n", "Cluster 1              0.25  \n", "Cluster 2              1.97  \n", "\n", "Feature Ranges by Cluster:\n", "\n", "Cluster 0:\n", "  sepal_length_(cm): 4.90 - 6.60 (mean: 5.80)\n", "  sepal_width_(cm): 2.00 - 3.00 (mean: 2.67)\n", "  petal_length_(cm): 3.00 - 5.60 (mean: 4.37)\n", "  petal_width_(cm): 1.00 - 2.40 (mean: 1.41)\n", "\n", "Cluster 1:\n", "  sepal_length_(cm): 4.30 - 5.80 (mean: 5.01)\n", "  sepal_width_(cm): 2.30 - 4.40 (mean: 3.43)\n", "  petal_length_(cm): 1.00 - 1.90 (mean: 1.46)\n", "  petal_width_(cm): 0.10 - 0.60 (mean: 0.25)\n", "\n", "Cluster 2:\n", "  sepal_length_(cm): 5.90 - 7.90 (mean: 6.78)\n", "  sepal_width_(cm): 2.50 - 3.80 (mean: 3.10)\n", "  petal_length_(cm): 4.40 - 6.90 (mean: 5.51)\n", "  petal_width_(cm): 1.40 - 2.50 (mean: 1.97)\n"]}], "source": ["\n", "# Analyze cluster characteristics to understand what each cluster represents\n", "print(\"Cluster Characteristics (Original Scale):\\n\")\n", "\n", "# Create DataFrame with cluster centers\n", "centers_df = pd.DataFrame(\n", "    cluster_centers_original,\n", "    columns=feature_names,\n", "    index=[f'Cluster {i}' for i in range(optimal_k)]\n", ")\n", "\n", "print(\"Cluster Centers:\")\n", "print(centers_df.round(2))\n", "\n", "# Analyze feature ranges by cluster\n", "print(\"\\nFeature Ranges by Cluster:\")\n", "for cluster in range(optimal_k):\n", "    mask = cluster_labels == cluster\n", "    cluster_data = df.iloc[mask]\n", "    print(f\"\\nCluster {cluster}:\")\n", "    for feature in feature_names:\n", "        min_val = cluster_data[feature].min()\n", "        max_val = cluster_data[feature].max()\n", "        mean_val = cluster_data[feature].mean()\n", "        print(f\"  {feature}: {min_val:.2f} - {max_val:.2f} (mean: {mean_val:.2f})\")\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "1915568e", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "visualization-title", "metadata": {}, "source": ["## <a id='toc8_7_'></a>[Visualization](#toc0_)"]}, {"cell_type": "code", "execution_count": 64, "id": "visualization", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Comprehensive visualization of clustering results\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Plot 1: Sepal dimensions with cluster labels\n", "for cluster in range(optimal_k):\n", "    mask = cluster_labels == cluster\n", "    axes[0, 0].scatter(df[mask]['sepal_length_(cm)'], \n", "                       df[mask]['sepal_width_(cm)'],\n", "                       label=f'Cluster {cluster}', s=50, alpha=0.7)\n", "# Plot cluster centers\n", "axes[0, 0].scatter(centers_df['sepal_length_(cm)'], \n", "                   centers_df['sepal_width_(cm)'],\n", "                   c='red', marker='*', s=500, edgecolors='black', linewidth=2,\n", "                   label='Centroids')\n", "axes[0, 0].set_xlabel('Sepal Length (cm)')\n", "axes[0, 0].set_ylabel('Sepal Width (cm)')\n", "axes[0, 0].set_title(f'K-Means Clustering - Sepal (K={optimal_k})', fontsize=14, fontweight='bold')\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Petal dimensions with cluster labels\n", "for cluster in range(optimal_k):\n", "    mask = cluster_labels == cluster\n", "    axes[0, 1].scatter(df[mask]['petal_length_(cm)'], \n", "                       df[mask]['petal_width_(cm)'],\n", "                       label=f'Cluster {cluster}', s=50, alpha=0.7)\n", "# Plot cluster centers\n", "axes[0, 1].scatter(centers_df['petal_length_(cm)'], \n", "                   centers_df['petal_width_(cm)'],\n", "                   c='red', marker='*', s=500, edgecolors='black', linewidth=2,\n", "                   label='Centroids')\n", "axes[0, 1].set_xlabel('Petal Length (cm)')\n", "axes[0, 1].set_ylabel('<PERSON><PERSON> Width (cm)')\n", "axes[0, 1].set_title(f'K-Means Clustering - Petal (K={optimal_k})', fontsize=14, fontweight='bold')\n", "axes[0, 1].legend()\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Silhouette plot for each cluster\n", "from sklearn.metrics import silhouette_samples\n", "silhouette_vals = silhouette_samples(X_scaled, cluster_labels)\n", "\n", "y_lower = 10\n", "for i in range(optimal_k):\n", "    cluster_silhouette_vals = silhouette_vals[cluster_labels == i]\n", "    cluster_silhouette_vals.sort()\n", "    \n", "    size_cluster_i = cluster_silhouette_vals.shape[0]\n", "    y_upper = y_lower + size_cluster_i\n", "    \n", "    color = plt.cm.viridis(float(i) / optimal_k)\n", "    axes[1, 0].fill_betweenx(np.arange(y_lower, y_upper),\n", "                              0, cluster_silhouette_vals,\n", "                              facecolor=color, edgecolor=color, alpha=0.7)\n", "    \n", "    # Label clusters\n", "    axes[1, 0].text(-0.05, y_lower + 0.5 * size_cluster_i, str(i))\n", "    y_lower = y_upper + 10\n", "\n", "axes[1, 0].set_xlabel('Silhouette Coefficient Values')\n", "axes[1, 0].set_ylabel('Cluster Label')\n", "axes[1, 0].axvline(x=silhouette_avg, color='red', linestyle='--', \n", "                   label=f'Average: {silhouette_avg:.3f}')\n", "axes[1, 0].set_title('Silhouette Plot for Each Cluster', fontsize=14, fontweight='bold')\n", "axes[1, 0].legend()\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 4: Cluster vs True Labels Comparison\n", "from sklearn.metrics import confusion_matrix\n", "cm = confusion_matrix(y_true, cluster_labels)\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1, 1],\n", "            xticklabels=[f'Cluster {i}' for i in range(optimal_k)],\n", "            yticklabels=target_names)\n", "axes[1, 1].set_xlabel('Predicted Cluster')\n", "axes[1, 1].set_ylabel('True Species')\n", "axes[1, 1].set_title('Cluster vs True Labels Comparison', fontsize=14, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n"]}, {"cell_type": "markdown", "id": "banner-9", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "02db21d7", "metadata": {}, "source": ["# <a id='toc10_'></a>[Saving and Loading with Joblib](#toc0_)\n"]}, {"cell_type": "code", "execution_count": 65, "id": "3d124ebc", "metadata": {}, "outputs": [], "source": ["\n", "# Import joblib for model persistence\n", "import joblib\n", "from datetime import datetime\n", "\n"]}, {"cell_type": "markdown", "id": "54be016c", "metadata": {}, "source": ["## Saving the Complete Model Package"]}, {"cell_type": "code", "execution_count": 70, "id": "82e6f578", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model package saved successfully to 'iris_kmeans_clustering.joblib'\n", "\n", "Saved components:\n", "  - feature_names\n", "  - numerical_features\n", "  - n_clusters\n", "  - cluster_centers_scaled\n", "  - cluster_centers_original\n", "  - evaluation_metrics\n", "  - model_params\n", "  - cluster_distribution\n", "  - training_date\n", "  - data_shape\n"]}], "source": ["\n", "# Create a comprehensive dictionary to store all model components\n", "model_package = {\n", "    'model': kmeans_optimal,\n", "    'preprocessor': preprocessing_pipeline,\n", "    'feature_names': feature_names,\n", "    'numerical_features': numerical_features,\n", "    'n_clusters': optimal_k,\n", "    'cluster_centers_scaled': cluster_centers,\n", "    'cluster_centers_original': cluster_centers_original,\n", "    'evaluation_metrics': {\n", "        'silhouette_score': silhouette_avg,\n", "        'davies_bouldin_index': davies_bouldin,\n", "        'calinski_harabasz_index': calinski_harabasz,\n", "        'inertia': kmeans_optimal.inertia_,\n", "        'n_iterations': kmeans_optimal.n_iter_\n", "    },\n", "    'model_params': kmeans_optimal.get_params(),\n", "    'cluster_distribution': {f'cluster_{i}': int(count) for i, count in enumerate(np.bincount(cluster_labels))},\n", "    'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "    'data_shape': X_scaled.shape\n", "}\n", "\n", "# Define the filename for saving the complete model package\n", "model_filename = 'iris_kmeans_clustering.joblib'\n", "\n", "# Save the model package to disk\n", "joblib.dump(model_package, model_filename)\n", "\n", "print(f\"Model package saved successfully to '{model_filename}'\")\n", "print(f\"\\nSaved components:\")\n", "for key in model_package.keys():\n", "    if key != 'model' and key != 'preprocessor':\n", "        print(f\"  - {key}\")\n", "\n"]}, {"cell_type": "markdown", "id": "acd66411", "metadata": {}, "source": ["## Loading the Saved Model Package"]}, {"cell_type": "code", "execution_count": 71, "id": "5194cf32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model package loaded successfully!\n", "\n", "Model Information:\n", "  Training Date: 2025-09-30 14:55:22\n", "  Number of Clusters: 3\n", "  Original Data Shape: (150, 4)\n", "\n", "Expected Features:\n", "  1. sepal_length_(cm)\n", "  2. sepal_width_(cm)\n", "  3. petal_length_(cm)\n", "  4. petal_width_(cm)\n", "\n", "Performance Metrics:\n", "  silhouette_score: 0.460\n", "  davies_bouldin_index: 0.834\n", "  calinski_harabasz_index: 241.904\n", "  inertia: 139.820\n", "  n_iterations: 4\n"]}], "source": ["# Load the complete model package from disk\n", "loaded_package = joblib.load(model_filename)\n", "\n", "# Extract components from the loaded package\n", "loaded_model = loaded_package['model']\n", "loaded_preprocessor = loaded_package['preprocessor']\n", "expected_features = loaded_package['feature_names']\n", "n_clusters = loaded_package['n_clusters']\n", "loaded_centers_original = loaded_package['cluster_centers_original']\n", "loaded_centers_scaled = loaded_package['cluster_centers_scaled']\n", "loaded_metrics = loaded_package['evaluation_metrics']\n", "training_date = loaded_package['training_date']\n", "\n", "print(\"Model package loaded successfully!\")\n", "print(f\"\\nModel Information:\")\n", "print(f\"  Training Date: {training_date}\")\n", "print(f\"  Number of Clusters: {n_clusters}\")\n", "print(f\"  Original Data Shape: {loaded_package['data_shape']}\")\n", "print(f\"\\nExpected Features:\")\n", "for i, feature in enumerate(expected_features):\n", "    print(f\"  {i+1}. {feature}\")\n", "print(f\"\\nPerformance Metrics:\")\n", "for metric, value in loaded_metrics.items():\n", "    if isinstance(value, float):\n", "        print(f\"  {metric}: {value:.3f}\")\n", "    else:\n", "        print(f\"  {metric}: {value}\")"]}, {"cell_type": "markdown", "id": "2c51ff57", "metadata": {}, "source": ["## Using the Loaded Model for New Predictions"]}, {"cell_type": "code", "execution_count": 72, "id": "7ce5650f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New samples to classify:\n", "   sepal_length_(cm)  sepal_width_(cm)  petal_length_(cm)  petal_width_(cm)\n", "0                5.1               3.5                1.4               0.2\n", "1                6.7               3.1                5.6               2.4\n", "2                5.5               2.4                3.7               1.0\n", "\n", "Cluster Assignment Results:\n", "============================================================\n", "\n", "Sample 1:\n", "  Assigned to: Cluster 1\n", "  Distance to cluster centers:\n", "    Cluster 0: 3.121\n", "    Cluster 1: 0.213\n", "    Cluster 2: 3.989\n", "\n", "  Cluster 1 typical characteristics:\n", "    sepal_length_(cm): 5.01\n", "    sepal_width_(cm): 3.43\n", "    petal_length_(cm): 1.46\n", "    petal_width_(cm): 0.25\n", "\n", "Sample 2:\n", "  Assigned to: Cluster 2\n", "  Distance to cluster centers:\n", "    Cluster 0: 2.079\n", "    Cluster 1: 4.284\n", "    Cluster 2: 0.574\n", "\n", "  Cluster 2 typical characteristics:\n", "    sepal_length_(cm): 6.78\n", "    sepal_width_(cm): 3.10\n", "    petal_length_(cm): 5.51\n", "    petal_width_(cm): 1.97\n", "\n", "Sample 3:\n", "  Assigned to: Cluster 0\n", "  Distance to cluster centers:\n", "    Cluster 0: 0.985\n", "    Cluster 1: 2.926\n", "    Cluster 2: 2.770\n", "\n", "  Cluster 0 typical characteristics:\n", "    sepal_length_(cm): 5.80\n", "    sepal_width_(cm): 2.67\n", "    petal_length_(cm): 4.37\n", "    petal_width_(cm): 1.41\n"]}], "source": ["\n", "# Create sample data for prediction (simulating new observations)\n", "new_samples = pd.DataFrame({\n", "    'sepal_length_(cm)': [5.1, 6.7, 5.5],\n", "    'sepal_width_(cm)': [3.5, 3.1, 2.4],\n", "    'petal_length_(cm)': [1.4, 5.6, 3.7],\n", "    'petal_width_(cm)': [0.2, 2.4, 1.0]\n", "})\n", "\n", "print(\"New samples to classify:\")\n", "print(new_samples)\n", "print()\n", "\n", "# Preprocess new data using loaded pipeline\n", "new_samples_scaled = loaded_preprocessor.transform(new_samples)\n", "\n", "# Make predictions using loaded model\n", "predictions = loaded_model.predict(new_samples_scaled)\n", "\n", "# Calculate distances to each cluster center\n", "distances = loaded_model.transform(new_samples_scaled)\n", "\n", "# Display results\n", "print(\"Cluster Assignment Results:\")\n", "print(\"=\" * 60)\n", "for i, pred in enumerate(predictions):\n", "    print(f\"\\nSample {i+1}:\")\n", "    print(f\"  Assigned to: Cluster {pred}\")\n", "    print(f\"  Distance to cluster centers:\")\n", "    for j in range(n_clusters):\n", "        print(f\"    Cluster {j}: {distances[i][j]:.3f}\")\n", "    \n", "    # Show the cluster characteristics\n", "    print(f\"\\n  Cluster {pred} typical characteristics:\")\n", "    for feat_idx, feature in enumerate(expected_features):\n", "        print(f\"    {feature}: {loaded_centers_original[pred][feat_idx]:.2f}\")\n"]}, {"cell_type": "markdown", "id": "58a5f073", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}, {"cell_type": "markdown", "id": "model-assumptions-title", "metadata": {}, "source": ["# <a id='toc9_'></a>[Model Assumptions, Limitations, and Best Use Cases](#toc0_)\n", "\n", "## <a id='toc9_1_'></a>[Assumptions of K-Means](#toc0_)\n", "\n", "**1. Spherical Clusters**\n", "- Assumes clusters are roughly spherical/circular in shape\n", "- Works poorly with elongated or irregular clusters\n", "\n", "**2. Similar Cluster Sizes**\n", "- Assumes all clusters have similar variance\n", "- May perform poorly with clusters of very different sizes\n", "\n", "**3. Similar Cluster Density**\n", "- Assumes uniform density within clusters\n", "- Struggles with clusters of varying densities\n", "\n", "**4. Number of Clusters Known**\n", "- Requires K to be specified in advance\n", "- No automatic determination of optimal K\n", "\n", "---\n", "\n", "## <a id='toc9_2_'></a>[Limitations](#toc0_)\n", "\n", "**1. Sensitive to Initial Centroids**\n", "- Different initializations can yield different results\n", "- Mitigated by k-means++ initialization and multiple runs\n", "\n", "**2. Sensitive to Outliers**\n", "- Outliers can significantly affect centroid positions\n", "- Consider outlier removal or use robust alternatives\n", "\n", "**3. <PERSON><PERSON> Non-Convex Shapes**\n", "- Fails with complex cluster shapes (e.g., crescents, rings)\n", "- Consider DBSCAN or spectral clustering for such cases\n", "\n", "**4. Requires Feature Scaling**\n", "- Distance-based algorithm sensitive to feature scales\n", "- Always standardize features before applying\n", "\n", "**5. <PERSON><PERSON><PERSON><PERSON><PERSON> with High Dimensions**\n", "- Curse of dimensionality affects distance calculations\n", "- Consider dimensionality reduction first\n", "\n", "---\n", "\n", "## <a id='toc9_3_'></a>[Best Use Cases](#toc0_)\n", "\n", "**1. Customer Segmentation**\n", "- Group customers by purchasing behavior\n", "- Identify market segments for targeted marketing\n", "\n", "**2. Image Compression**\n", "- Reduce color palette by clustering similar colors\n", "- Efficient storage with minimal quality loss\n", "\n", "**3. Document Clustering**\n", "- Group similar documents or articles\n", "- Topic modeling and content organization\n", "\n", "**4. Anomaly Detection**\n", "- Identify outliers as points far from all centroids\n", "- Quality control in manufacturing\n", "\n", "**5. Biological Classification**\n", "- Group organisms by characteristics (as with Iris)\n", "- Gene expression analysis\n", "\n", "**6. Preprocessing for Other Algorithms**\n", "- Create features for supervised learning\n", "- Reduce computational complexity\n", "\n", "---\n", "\n", "## <a id='toc9_4_'></a>[Performance Optimization Tips](#toc0_)\n", "\n", "**1. Data Preprocessing**\n", "- Always scale features (StandardScaler or MinMaxScaler)\n", "- Remove or handle outliers appropriately\n", "- Consider dimensionality reduction (PCA) for high-dimensional data\n", "\n", "**2. <PERSON><PERSON><PERSON> K**\n", "- Use elbow method for initial estimate\n", "- Validate with silhouette analysis\n", "- **Always consider domain knowledge** (as with Iris having 3 species)\n", "\n", "**3. Initialization**\n", "- Use 'k-means++' for smart initialization\n", "- Run multiple times with different seeds\n", "- Select best result based on lowest inertia\n", "\n", "**4. <PERSON><PERSON><PERSON><PERSON>**\n", "- Mini-batch K-Means for large datasets\n", "- K-Medoids for better outlier handling\n", "- Fuzzy C-Means for soft clustering\n", "\n", "**5. Evaluation**\n", "- Use multiple metrics (<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>)\n", "- Visualize results when possible\n", "- Validate with domain experts"]}, {"cell_type": "markdown", "id": "banner-11", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/henrylahteenmaki/Data-Analysis-with-Python/refs/heads/main/figs/division_banner.png\" width=\"2000\" height=\"6\">"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}